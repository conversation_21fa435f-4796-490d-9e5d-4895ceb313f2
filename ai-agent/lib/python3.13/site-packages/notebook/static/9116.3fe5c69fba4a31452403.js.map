{"version": 3, "file": "9116.3fe5c69fba4a31452403.js?v=3fe5c69fba4a31452403", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/diff.js"], "sourcesContent": ["var TOKEN_NAMES = {\n  '+': 'inserted',\n  '-': 'deleted',\n  '@': 'meta'\n};\n\nexport const diff = {\n  name: \"diff\",\n  token: function(stream) {\n    var tw_pos = stream.string.search(/[\\t ]+?$/);\n\n    if (!stream.sol() || tw_pos === 0) {\n      stream.skipToEnd();\n      return (\"error \" + (\n        TOKEN_NAMES[stream.string.charAt(0)] || '')).replace(/ $/, '');\n    }\n\n    var token_name = TOKEN_NAMES[stream.peek()] || stream.skipToEnd();\n\n    if (tw_pos === -1) {\n      stream.skipToEnd();\n    } else {\n      stream.pos = tw_pos;\n    }\n\n    return token_name;\n  }\n};\n\n"], "names": [], "sourceRoot": ""}