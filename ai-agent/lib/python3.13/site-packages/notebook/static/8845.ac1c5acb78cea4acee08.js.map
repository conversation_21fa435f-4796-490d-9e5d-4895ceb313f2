{"version": 3, "file": "8845.ac1c5acb78cea4acee08.js?v=ac1c5acb78cea4acee08", "mappings": ";;;;;;AAAa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,mBAAO,CAAC,KAA6B;AACrC,mBAAO,CAAC,KAAiC;AACzC,mBAAO,CAAC,KAA2B;AACnC,mBAAO,CAAC,KAA+B;AACvC,mBAAO,CAAC,KAA6B;AACrC,mBAAO,CAAC,KAAyC;AACjD,mBAAO,CAAC,KAAiC;AACzC,mBAAO,CAAC,KAAyC;AACjD,mBAAO,CAAC,KAAiC;AACzC,mBAAO,CAAC,KAA+B;AACvC,mBAAO,CAAC,KAAuC;AAC/C,mBAAO,CAAC,KAA+B;AACvC,mBAAO,CAAC,KAAmC;AAC3C,mBAAO,CAAC,KAAqC;AAC7C,mBAAO,CAAC,KAA6C;AACrD,mBAAO,CAAC,KAAiC;AACzC,mBAAO,CAAC,KAAmC;AAC3C,mBAAO,CAAC,KAAqC;AAC7C,mBAAO,CAAC,KAAmC;AAC3C,mBAAO,CAAC,GAA6B;AACrC,mBAAO,CAAC,KAAuC;AAC/C,mBAAO,CAAC,KAAiC;AACzC,mBAAO,CAAC,KAAyC;AACjD,mBAAO,CAAC,KAAqC;AAC7C,mBAAO,CAAC,KAA2C;AACnD,mBAAO,CAAC,KAAmC;AAC3C,mBAAO,CAAC,KAAyC;AACjD,mBAAO,CAAC,KAAuC;AAC/C,mBAAO,CAAC,KAAqC;AAC7C,mBAAO,CAAC,KAAyC;AACjD,mBAAO,CAAC,KAAmC;AAC3C,mBAAO,CAAC,KAAmC;AAC3C,mBAAO,CAAC,KAA6B;AACrC;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvEa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B,GAAG,qBAAqB;AACnD,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,uCAAuC,mBAAO,CAAC,KAAwB;AACvE,qBAAqB;AACrB,2BAA2B;AAC3B,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA,6DAA6D,sBAAsB;AACnF;AACA,6BAA6B;AAC7B;AACA;AACA,+DAA+D,uBAAuB;AACtF;AACA;AACA;AACA;AACA,kCAAkC,IAAI,OAAO,IAAI;AACjD,CAAC;AACD,2BAA2B,uDAAuD,WAAW,4BAA4B;AACzH;;;;;;;AC/Ba;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,wBAAwB,GAAG,eAAe;AAC1C,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,oBAAoB,mBAAO,CAAC,KAAe;AAC3C,gBAAgB,mBAAO,CAAC,KAAY;AACpC,sBAAsB,mBAAO,CAAC,KAAiB;AAC/C,mBAAO,CAAC,KAAkB;AAC1B,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,eAAe;AACf;AACA,6DAA6D,IAAI;AACjE,2DAA2D,WAAW,kCAAkC;AACxG,sBAAsB;AACtB;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,mBAAmB;AACnB;AACA;AACA;AACA,YAAY,gBAAgB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;ACnEa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB,GAAG,oBAAoB;AAC1C,qBAAqB,mBAAO,CAAC,KAAsB;AACnD,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,wBAAwB,mBAAO,CAAC,KAAoB;AACpD;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,mEAAmE,qBAAqB,IAAI;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,UAAU,wBAAwB,oBAAoB,IAAI;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mBAAmB;AACnB;;;;;;;AC1Ka;AACb;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,0CAA0C,4BAA4B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,sBAAsB,mBAAO,CAAC,KAAiB;AAC/C,sBAAsB,mBAAO,CAAC,KAAiB;AAC/C,wBAAwB,mBAAO,CAAC,KAAoB;AACpD,wCAAwC,mBAAO,CAAC,IAAoB;AACpE,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,mBAAmB,mBAAO,CAAC,KAA0B;AACrD;AACA,yBAAyB,oCAAoC;AAC7D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,gBAAgB,GAAG,EAAE;AAClD;AACA;AACA;AACA,iCAAiC,UAAU,EAAE,QAAQ,SAAS;AAC9D,oCAAoC;AACpC,sCAAsC;AACtC,mCAAmC,YAAY,WAAW,IAAI,MAAM;AACpE,mCAAmC,WAAW,WAAW,IAAI,MAAM;AACnE,mCAAmC,kBAAkB,WAAW,IAAI,MAAM;AAC1E,oCAAoC,iBAAiB,WAAW,IAAI,MAAM;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,OAAO;AACxC;AACA;AACA,+BAA+B;AAC/B,CAAC;AACD;AACA,4BAA4B,sCAAsC;AAClE,4BAA4B,uCAAuC;AACnE,4BAA4B,sCAAsC;AAClE,4BAA4B,uCAAuC;AACnE,CAAC;AACD;AACA;AACA;AACA,2BAA2B,2DAA2D;AACtF,2BAA2B,2DAA2D;AACtF,2BAA2B,2DAA2D;AACtF,4BAA4B,2DAA2D;AACvF,wBAAwB,2DAA2D;AACnF,wBAAwB,2DAA2D;AACnF,2BAA2B,2DAA2D;AACtF,6BAA6B,2DAA2D;AACxF,yBAAyB,2DAA2D;AACpF,yBAAyB,2DAA2D;AACpF,2BAA2B,2DAA2D;AACtF;AACA;AACA;AACA,4BAA4B,mBAAmB;AAC/C;AACA,6BAA6B,mBAAmB;AAChD;AACA,+BAA+B,mBAAmB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,2DAA2D;AACtF;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,2DAA2D;AACjF;AACA;AACA;AACA;AACA;AACA,UAAU,iEAAiE;AAC3E;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,gCAAgC,mBAAmB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,mBAAmB;AAC9C;AACA,8BAA8B,mBAAmB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAmB;AAC3C;AACA;AACA,6BAA6B,mBAAmB;AAChD,2BAA2B,mBAAmB;AAC9C,6BAA6B,mBAAmB;AAChD,gCAAgC,mBAAmB;AACnD;AACA;AACA;AACA;AACA,4BAA4B,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,mBAAmB;AACjD;AACA;AACA;AACA;AACA;AACA,4BAA4B,mBAAmB;AAC/C,4BAA4B,mBAAmB;AAC/C,wBAAwB,mBAAmB;AAC3C,wBAAwB,mBAAmB;AAC3C;AACA;AACA;AACA;AACA,4BAA4B,mBAAmB;AAC/C,4BAA4B,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,mBAAmB;AAC7C,0BAA0B,mBAAmB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,mBAAmB;AAC/C,iCAAiC,mBAAmB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,mBAAmB;AAChD,6BAA6B,mBAAmB;AAChD;AACA;AACA,+BAA+B,mBAAmB;AAClD,+BAA+B,mBAAmB;AAClD;AACA;AACA,gCAAgC,mBAAmB;AACnD,gCAAgC,mBAAmB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,mBAAmB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,2BAA2B,mBAAmB;AAC9C,6BAA6B,kBAAkB;AAC/C,CAAC;AACD;;;;;;;AC9Wa;AACb;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,GAAG,kBAAkB;AACpC,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,wCAAwC,mBAAO,CAAC,IAAoB;AACpE,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,wBAAwB,mBAAO,CAAC,KAAoB;AACpD,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,kBAAkB,mBAAO,CAAC,KAAc;AACxC,uCAAuC,mBAAO,CAAC,KAAwB;AACvE,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,kBAAkB;AAClB,8BAA8B;AAC9B,2CAA2C,wBAAwB;AACnE;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA,6CAA6C,aAAa;AAC1D;AACA,oCAAoC,aAAa;AACjD;AACA,oHAAoH,aAAa;AACjI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,wCAAwC,wBAAwB;AAChE;AACA,oHAAoH,wBAAwB;AAC5I;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qGAAqG,gBAAgB;AACrH;AACA,qCAAqC;AACrC;AACA;AACA,iEAAiE,uBAAuB,iHAAiH;AACzM;AACA;AACA;AACA,+CAA+C,2EAA2E;AAC1H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oGAAoG,UAAU;AAC9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,2EAA2E;AACjI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,uBAAuB;AAClE;AACA;AACA,8CAA8C,0BAA0B;AACxE;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,gBAAgB;AAChB;AACA;AACA,uDAAuD,gBAAgB;AACvE,+CAA+C,qDAAqD;AACpG,uDAAuD,gBAAgB;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,iBAAiB;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE,YAAY;AAC7E,iEAAiE,YAAY;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,8BAA8B;AAClF;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD,oCAAoC;AAC5F;AACA;AACA,wDAAwD;AACxD,6CAA6C;AAC7C;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,4BAA4B;AAC5B,wBAAwB;AACxB,yBAAyB;AACzB,wBAAwB;AACxB,wBAAwB;AACxB,yBAAyB;AACzB,0BAA0B;AAC1B,2BAA2B;AAC3B,2BAA2B;AAC3B;;;;;;;ACpVa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,0BAA0B;AAC1B,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,mBAAO,CAAC,KAAoB;AAC5B,0BAA0B;AAC1B;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;ACrBa;AACb;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,0CAA0C,4BAA4B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,sBAAsB,mBAAO,CAAC,KAAiB;AAC/C,wCAAwC,mBAAO,CAAC,IAAoB;AACpE,wCAAwC,mBAAO,CAAC,KAAmB;AACnE,oFAAoF,UAAU;AAC9F;AACA;AACA;AACA,CAAC;AACD,mCAAmC,cAAc;AACjD;;;;;;;ACrCa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,6BAA6B,mBAAO,CAAC,KAA8B;AACnE,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,kDAAkD,WAAW;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,2BAA2B;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+GAA+G,UAAU;AACzH;AACA;AACA;AACA;AACA,+GAA+G,UAAU;AACzH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,+BAA+B;AAC1F;AACA,kEAAkE,2BAA2B;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAe;AACf;;;;;;;ACzHa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,yBAAyB,GAAG,mBAAmB;AAC/C,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,mBAAmB;AACnB,wBAAwB;AACxB;AACA;AACA;AACA;AACA,sCAAsC,OAAO;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,EAAE,YAAY,EAAE;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,4BAA4B;AAC7D;AACA;AACA,iCAAiC,cAAc;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,cAAc;AACtD,yBAAyB,qDAAqD,WAAW,mBAAmB;AAC5G;;;;;;;ACvEa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,+BAA+B,GAAG,yBAAyB,GAAG,uBAAuB,GAAG,yBAAyB;AACjH,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,wBAAwB,mBAAO,CAAC,KAAoB;AACpD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,uBAAuB,mBAAO,CAAC,KAAmB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,0BAA0B;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA,6EAA6E,UAAU;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA,yBAAyB;AACzB,+BAA+B;AAC/B,eAAe,uBAAuB;AACtC,aAAa,0BAA0B;AACvC;AACA,CAAC;AACD;;;;;;;ACjFa;AACb;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B;AAC3B,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,uBAAuB,mBAAO,CAAC,KAAkB;AACjD,mBAAO,CAAC,KAAqB;AAC7B,2BAA2B;AAC3B;AACA;AACA;AACA,KAAK;AACL,mBAAmB;AACnB;AACA;AACA,CAAC;AACD;;;;;;;AChBa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,+EAA+E,iEAAiE;AAChJ;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;;;;;;;ACzEa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,yCAAyC,mBAAO,CAAC,KAAoB;AACrE;AACA,qBAAqB,UAAU,IAAI,OAAO;AAC1C,qBAAqB,QAAQ,IAAI,SAAS;AAC1C;AACA,wBAAwB,KAAK;AAC7B,qBAAqB,gBAAgB,IAAI,cAAc;AACvD,qBAAqB,cAAc,IAAI,gBAAgB;AACvD;AACA,sBAAsB,KAAK;AAC3B,wBAAwB,QAAQ,IAAI,kBAAkB,IAAI,OAAO;AACjE,wBAAwB,cAAc,IAAI,+BAA+B,IAAI,cAAc;AAC3F;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;;;;;;;ACvBa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uCAAuC,mBAAO,CAAC,KAAwB;AACvE,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA,yBAAyB;AACzB,0DAA0D;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,sDAAsD;AACvG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,kCAAkC;AACtF;AACA;AACA;AACA,sDAAsD,uCAAuC;AAC7F;AACA;AACA,0CAA0C,iCAAiC;AAC3E;AACA,kDAAkD,sCAAsC;AACxF;AACA;AACA,kBAAe;AACf;;;;;;;ACpDa;AACb;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,+BAA+B;AAC/B,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,2BAA2B,mBAAO,CAAC,KAAsB;AACzD,0BAA0B,mBAAO,CAAC,KAAqB;AACvD,mBAAO,CAAC,KAAyB;AACjC,+BAA+B;AAC/B;AACA;AACA;AACA,KAAK;AACL,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;ACzBa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,0CAA0C,4BAA4B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB;AACrB,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,iCAAiC,mBAAO,CAAC,KAAa;AACtD,kCAAkC,mBAAO,CAAC,KAAqB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA,mEAAmE;AACnE;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4EAA4E,GAAG;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6DAA6D;AAC7D;AACA;AACA,CAAC;AACD,qBAAqB;AACrB;;;;;;;ACzFa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,6CAA6C,mBAAO,CAAC,KAAwB;AAC7E,wCAAwC,mBAAO,CAAC,IAAoB;AACpE,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;;;;;;;AC9Ca;AACb;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,0CAA0C,4BAA4B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,kCAAkC,mBAAO,CAAC,KAAqB;AAC/D;AACA;AACA;AACA;AACA,wBAAwB;AACxB,+DAA+D;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,eAAe;AACpE,qDAAqD,eAAe;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gEAAgE;AAChE;AACA,qEAAqE,4CAA4C;AACjH;AACA,MAAM;AACN,uDAAuD;AACvD,yDAAyD,qBAAqB;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wFAAwF,MAAM;AAC9F,0FAA0F,MAAM;AAChG,8FAA8F,sDAAsD;AACpJ;AACA;AACA;AACA,+DAA+D,oDAAoD;AACnH;AACA;AACA;AACA,iEAAiE,oDAAoD;AACrH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD;AACzD,2DAA2D;AAC3D,6DAA6D;AAC7D,oEAAoE;AACpE,yDAAyD,gDAAgD;AACzG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gEAAgE;AAChE;AACA,qEAAqE,4CAA4C;AACjH;AACA,MAAM;AACN,uDAAuD;AACvD,yDAAyD,qBAAqB;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAe;AACf;;;;;;;AClPa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB,GAAG,oBAAoB,GAAG,0BAA0B,GAAG,sBAAsB,GAAG,mBAAmB,GAAG,mBAAmB,GAAG,oBAAoB;AACrK,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,yCAAyC;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,mBAAmB;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mEAAmE,yCAAyC;AAC5G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4FAA4F,sBAAsB;AAClH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4EAA4E,0BAA0B;AACtG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA,oBAAoB;AACpB;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,6EAA6E;AAC7E;AACA,KAAK;AACL;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA,qBAAqB;AACrB;;;;;;;ACxUa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B,GAAG,qBAAqB;AACnD,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,wBAAwB,mBAAO,CAAC,KAAoB;AACpD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,gCAAgC,mBAAO,CAAC,KAAoC;AAC5E,qBAAqB;AACrB,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD,kDAAkD;AAC1G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,2BAA2B,uDAAuD,WAAW,qBAAqB;AAClH;;;;;;;ACtCa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA,6CAA6C;AAC7C;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,0BAA0B,GAAG,oBAAoB,GAAG,iBAAiB,GAAG,sBAAsB;AAC9F,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,uCAAuC,mBAAO,CAAC,KAAwB;AACvE,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qBAAqB,mBAAO,CAAC,KAAsB;AACnD,4BAA4B,mBAAO,CAAC,KAA4B;AAChE,sBAAsB,mBAAO,CAAC,KAAyB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA,CAAC;AACD,iBAAiB;AACjB,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,wBAAwB;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,sEAAsE,2BAA2B;AACjG;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD,0BAA0B;AAC1B;AACA;AACA;AACA,KAAK;AACL,mBAAmB;AACnB;AACA;AACA,YAAY;AACZ,CAAC;AACD;;;;;;;AC3Ka;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,8BAA8B,GAAG,wBAAwB;AACzD,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,uCAAuC,mBAAO,CAAC,KAAwB;AACvE;AACA;AACA,uCAAuC,KAAK,QAAQ;AACpD,CAAC;AACD;AACA,oBAAoB,iCAAiC;AACrD;AACA;AACA;AACA;AACA;AACA,2DAA2D,8BAA8B;AACzF;AACA,iBAAiB,8BAA8B;AAC/C;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,4EAA4E,UAAU;AACtF;AACA;AACA;AACA,sGAAsG,oBAAoB;AAC1H;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA,wBAAwB;AACxB,8BAA8B;AAC9B,eAAe,sBAAsB;AACrC;AACA,CAAC;AACD;;;;;;;ACnEa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,0BAA0B;AAC1B,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,wBAAwB,mBAAO,CAAC,KAAmB;AACnD,qBAAqB,mBAAO,CAAC,KAAgB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,gDAAgD,wCAAwC;AACxF;AACA,0BAA0B;AAC1B;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD;;;;;;;AC7Ba;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,cAAc;AACd,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACzEa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB;AACpB,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA,yBAAyB,UAAU,oBAAoB;AACvD;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,kBAAkB;AAC3E;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;;;;;;AC7Ea;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,0BAA0B,mBAAO,CAAC,KAAqB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oFAAoF,oBAAoB;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oFAAoF,oBAAoB;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;ACxIa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,6BAA6B,GAAG,sBAAsB;AACtD,qBAAqB,mBAAO,CAAC,KAAsB;AACnD,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,sBAAsB;AACtB;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,eAAe,qBAAqB;AACpC,aAAa,yBAAyB;AACtC;AACA;AACA,CAAC;AACD;;;;;;;AC1Ga;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,0BAA0B,GAAG,sBAAsB;AACnD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6DAA6D,kBAAkB;AAC/E;AACA;AACA;AACA,2CAA2C,gBAAgB;AAC3D,0BAA0B,wDAAwD,WAAW,sBAAsB;AACnH;;;;;;;ACvBa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iCAAiC;AACjC,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,mBAAmB,mBAAO,CAAC,IAA0B;AACrD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,wCAAwC,mBAAO,CAAC,IAAoB;AACpE,kBAAkB,mBAAO,CAAC,KAAc;AACxC,6CAA6C,mBAAO,CAAC,KAAoC;AACzF,2BAA2B,mBAAO,CAAC,KAAkC;AACrE;AACA;AACA;AACA,+CAA+C,IAAI;AACnD,+FAA+F,IAAI;AACnG;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2EAA2E,UAAU;AACrF;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA,iCAAiC;AACjC;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA,+CAA+C;AAC/C,qDAAqD;AACrD;AACA,CAAC;AACD;;;;;;;AC7Fa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B,GAAG,qBAAqB,GAAG,uBAAuB;AAC7E,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qBAAqB,mBAAO,CAAC,KAAsB;AACnD,sBAAsB,mBAAO,CAAC,KAAiB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,wBAAwB;AAC3E,yDAAyD,wBAAwB;AACjF;AACA;AACA;AACA;AACA,oFAAoF,mBAAmB;AACvG;AACA;AACA,qCAAqC,YAAY,WAAW,UAAU;AACtE;AACA;AACA;AACA,KAAK;AACL;AACA,mDAAmD;AACnD,KAAK;AACL;AACA;AACA,mDAAmD,iCAAiC;AACpF;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,iCAAiC;AACjC,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,2BAA2B;AAC3B;AACA;AACA;AACA,KAAK;AACL,mBAAmB;AACnB;AACA;AACA,CAAC;AACD;;;;;;;ACtJa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,kBAAkB;AAClB;AACA;AACA,8EAA8E,sBAAsB;AACpG;AACA,KAAK;AACL;AACA,kCAAkC;AAClC;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,sEAAsE,UAAU;AAChF;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,KAAK;AACL;AACA;AACA,6DAA6D,0CAA0C;AACvG;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+FAA+F,UAAU;AACzG;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,wDAAwD,UAAU;AAClE;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,qDAAqD,+CAA+C,UAAU;AAC9G,KAAK;AACL;AACA;AACA;AACA;AACA,aAAa,wCAAwC;AACrD,KAAK;AACL;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA,yFAAyF,UAAU;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,KAAK;AACL;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;;;;;;AClLa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,4BAA4B,GAAG,sBAAsB,GAAG,uBAAuB;AAC/E,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,oBAAoB;AAC/D,4BAA4B,wDAAwD,WAAW,sBAAsB;AACrH;;;;;;;AC7Ba;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,6BAA6B,GAAG,uBAAuB;AACvD,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,sBAAsB,mBAAO,CAAC,KAAsB;AACpD,0CAA0C,mBAAO,CAAC,KAAiC;AACnF,mCAAmC,mBAAO,CAAC,KAA0C;AACrF,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,uBAAuB;AACvB,8BAA8B;AAC9B,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,6BAA6B;AAC7B,eAAe,qBAAqB;AACpC;AACA,CAAC;AACD;;;;;;;AC9Ca;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,4BAA4B;AAC5B,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,wBAAwB,mBAAO,CAAC,KAAoB;AACpD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,4BAA4B;AAC5B,eAAe,4BAA4B;AAC3C,CAAC;AACD;;;;;;;ACvBa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,yBAAyB;AACzB,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,uCAAuC,mBAAO,CAAC,KAAkB;AACjE;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,yBAAyB,qDAAqD,WAAW,0BAA0B;AACnH;;;;;;;AChBa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD;AACjD,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAe;AACf;;;;;;;ACzDa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,8BAA8B,GAAG,qBAAqB,GAAG,oBAAoB;AAC7E,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,mBAAmB,mBAAO,CAAC,IAA0B;AACrD,mBAAO,CAAC,GAAwB;AAChC,yBAAyB,mBAAO,CAAC,KAAoB;AACrD,yBAAyB,mBAAO,CAAC,KAAoB;AACrD,0BAA0B,mBAAO,CAAC,KAAqB;AACvD,oBAAoB;AACpB;AACA,0DAA0D,IAAI;AAC9D,2DAA2D,WAAW,+BAA+B,gBAAgB;AACrH;AACA;AACA;AACA;AACA;AACA;AACA,2EAA2E,UAAU;AACrF;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+EAA+E,UAAU;AACzF;AACA;AACA;AACA;AACA;AACA;AACA,gFAAgF,UAAU;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA,gFAAgF,UAAU;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA,qBAAqB;AACrB,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6DAA6D;AAC7D,qDAAqD;AACrD;AACA;AACA,CAAC;AACD;;;;;;;AC7Ia;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB;AACrB,oBAAoB,mBAAO,CAAC,KAAoB;AAChD,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,wBAAwB,mBAAO,CAAC,KAAoB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sEAAsE,kBAAkB;AACxF;AACA;AACA;AACA;AACA;AACA,gEAAgE,iBAAiB;AACjF;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,qBAAqB;AACrB;;;;;;;AC/Da;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,wCAAwC,mBAAO,CAAC,IAAoB;AACpE,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,wBAAwB,mBAAO,CAAC,KAAoB;AACpD,4BAA4B,mBAAO,CAAC,KAAuB;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,iBAAiB,EAAE,QAAQ,gBAAgB;AACnF;AACA;AACA;AACA;AACA;AACA,oCAAoC,KAAK,UAAU,MAAM;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,+BAA+B,QAAQ,YAAY;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,GAAG,YAAY,KAAK,GAAG,GAAG,YAAY,KAAK,UAAU,GAAG,YAAY,KAAK,GAAG,GAAG,YAAY,KAAK;AACnH;AACA;AACA;AACA,CAAC;AACD;AACA,gCAAgC;AAChC,oCAAoC;AACpC,qCAAqC;AACrC,iCAAiC;AACjC,qCAAqC;AACrC,sCAAsC;AACtC,gCAAgC;AAChC;AACA;AACA;AACA,uCAAuC,OAAO;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,OAAO;AACrD,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;;;;;;;AC9Ha;AACb;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,wBAAwB;AACxB,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,sBAAsB,mBAAO,CAAC,KAAsB;AACpD,uCAAuC,mBAAO,CAAC,KAAwB;AACvE,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,mBAAmB,mBAAO,CAAC,KAA0B;AACrD,mBAAmB,mBAAO,CAAC,IAA0B;AACrD,0CAA0C,mBAAO,CAAC,KAAiC;AACnF,6CAA6C,mBAAO,CAAC,KAAoC;AACzF,yBAAyB,mBAAO,CAAC,KAAoB;AACrD,wBAAwB;AACxB;AACA,gDAAgD,4BAA4B;AAC5E;AACA,KAAK;AACL;AACA;AACA,gDAAgD,4BAA4B;AAC5E;AACA;AACA,KAAK;AACL;AACA;AACA,4BAA4B,4BAA4B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,cAAc;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oGAAoG,UAAU;AAC9G;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,4BAA4B;AAC9E;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,0BAA0B;AACxD;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,iFAAiF,UAAU,oBAAoB,IAAI,gDAAgD;AACnK,aAAa,yBAAyB;AACtC;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,2DAA2D,sBAAsB;AACjF;AACA;AACA,KAAK;AACL;AACA;AACA,8DAA8D,UAAU;AACxE;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,yBAAyB;AAC7E;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD;AACjD;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uFAAuF,QAAQ,IAAI,IAAI,sBAAsB,GAAG,aAAa,GAAG;AAChJ;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,8BAA8B,KAAK,2CAA2C,KAAK;AACnF;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,8BAA8B,eAAe,qCAAqC,IAAI;AAChH;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,+BAA+B;AAC/B,4BAA4B;AAC5B,8BAA8B;AAC9B,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,OAAO;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,yDAAyD;AAClG;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,gCAAgC;AAChC,+BAA+B;AAC/B;AACA,iDAAiD;AACjD;AACA;AACA,qEAAqE,4DAA4D,YAAY,kEAAkE,IAAI;AACnN;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,uDAAuD,qCAAqC;AAC5F;AACA;AACA,6BAA6B,gEAAgE,EAAE,OAAO;AACtG;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,8CAA8C;AAC9C;AACA;AACA;AACA,8DAA8D,yCAAyC;AACvG,yBAAyB,4EAA4E;AACrG,qBAAqB,2CAA2C;AAChE;AACA,sDAAsD;AACtD;AACA,iBAAiB,8BAA8B;AAC/C,aAAa,qCAAqC;AAClD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uDAAuD,cAAc;AACrE,qBAAqB,gBAAgB;AACrC;AACA,uDAAuD,cAAc;AACrE;AACA;AACA,qBAAqB,gBAAgB;AACrC,iBAAiB,yDAAyD;AAC1E,aAAa,uCAAuC;AACpD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,iBAAiB;AACpE;AACA,iBAAiB,sDAAsD;AACvE,aAAa,qCAAqC;AAClD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,uEAAuE,UAAU;AACjF;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvea;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,0BAA0B;AAC1B,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,gBAAgB,mBAAO,CAAC,KAAY;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4EAA4E,UAAU;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,iBAAiB;AACnF;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;;;;;;;AClGa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB;AACrB,qBAAqB,mBAAO,CAAC,KAAsB;AACnD,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,kBAAkB,mBAAO,CAAC,KAAc;AACxC,mBAAmB,mBAAO,CAAC,IAA0B;AACrD,4BAA4B,mBAAO,CAAC,KAAuB;AAC3D,kCAAkC,mBAAO,CAAC,KAA6B;AACvE,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,4FAA4F;AAChI;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,iBAAiB;AAC9D;AACA;AACA;AACA;;;;;;;ACzFa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B;AAC3B,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,uCAAuC,mBAAO,CAAC,KAAwB;AACvE,sBAAsB,mBAAO,CAAC,KAAsB;AACpD,wBAAwB,mBAAO,CAAC,KAAmC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,YAAY,EAAE,OAAO,mBAAmB,SAAS,kBAAkB,OAAO,IAAI;AAClG;AACA;AACA;AACA,oBAAoB,YAAY,EAAE,OAAO,mBAAmB,QAAQ,mBAAmB;AACvF;AACA;AACA;AACA,oBAAoB,uBAAuB,IAAI,mBAAmB,SAAS,kBAAkB,OAAO,IAAI;AACxG;AACA;AACA;AACA,oBAAoB,kBAAkB,QAAQ,gBAAgB,OAAO,KAAK,OAAO,KAAK;AACtF;AACA;AACA;AACA,oBAAoB,EAAE,WAAW,sBAAsB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,UAAU;AACvG;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,2BAA2B,uDAAuD,WAAW,qBAAqB;AAClH;;;;;;;ACxDa;AACb;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,0CAA0C,4BAA4B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,+BAA+B;AAC/B,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,2BAA2B,mBAAO,CAAC,KAAsB;AACzD,0CAA0C,mBAAO,CAAC,KAAqB;AACvE,mBAAO,CAAC,KAAyB;AACjC,wCAAwC,mBAAO,CAAC,IAAoB;AACpE,sBAAsB,mBAAO,CAAC,KAAiB;AAC/C;AACA,0GAA0G;AAC1G,iEAAiE,IAAI;AACrE,gHAAgH,IAAI;AACpH,2DAA2D,WAAW;AACtE;AACA;AACA;AACA;AACA,SAAS;AACT,sBAAsB;AACtB;AACA,+BAA+B;AAC/B;AACA;AACA,KAAK;AACL,mBAAmB;AACnB;AACA;AACA,eAAe,iBAAiB;AAChC;AACA,CAAC;AACD;;;;;;;AC1Da;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB;AACpB,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,sEAAsE,IAAI,iBAAiB,GAAG;AAC9F;AACA;AACA;AACA;AACA,4EAA4E,GAAG;AAC/E;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;;;;;;;ACzDa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,6CAA6C,mBAAO,CAAC,KAAwB;AAC7E,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;ACfa;AACb;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,0CAA0C,4BAA4B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,sBAAsB,mBAAO,CAAC,KAAiB;AAC/C,uCAAuC,mBAAO,CAAC,KAAwB;AACvE,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,0CAA0C,mBAAO,CAAC,KAAqB;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,cAAc;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uDAAuD,wBAAwB;AAC/E;AACA;AACA,kCAAkC,OAAO;AACzC,kDAAkD,wBAAwB;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAe;AACf;;;;;;;ACxJa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,kBAAkB,mBAAO,CAAC,KAAc;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,wCAAwC;AACzC,kBAAe;AACf;;;;;;;AChLa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,6BAA6B;AAC7B,yBAAyB,mBAAO,CAAC,KAAqB;AACtD;AACA,mDAAmD;AACnD,4DAA4D,2CAA2C;AACvG;AACA;AACA,6BAA6B,yDAAyD,SAAS,qBAAqB;AACpH;;;;;;;ACVa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gCAAgC;AAChC,yBAAyB,mBAAO,CAAC,KAAqB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,iFAAiF,UAAU;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,gCAAgC;AAChC,gBAAgB,oBAAoB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD;;;;;;;AChDa;AACb;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,4BAA4B;AAC5B,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,wBAAwB,mBAAO,CAAC,KAAmB;AACnD,mBAAO,CAAC,KAAsB;AAC9B,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;ACjCa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB;AAChB,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,oEAAoE,qBAAqB;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,gBAAgB;AAChB;;;;;;;ACrFa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,0CAA0C,mBAAO,CAAC,KAAqB;AACvE,wBAAwB,mBAAO,CAAC,KAAoB;AACpD,wCAAwC,mBAAO,CAAC,IAAoB;AACpE,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,OAAO;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,OAAO;AAChD,gCAAgC,OAAO;AACvC,yCAAyC,OAAO;AAChD,6BAA6B,OAAO;AACpC,CAAC;AACD;AACA,6BAA6B,yDAAyD;AACtF,uBAAuB,yDAAyD;AAChF;AACA;AACA;AACA,gCAAgC,yDAAyD;AACzF,CAAC;AACD;AACA,uBAAuB,2DAA2D;AAClF,4BAA4B,2DAA2D;AACvF,CAAC;AACD;AACA;AACA;AACA;AACA,2CAA2C,SAAS,IAAI;AACxD,kCAAkC,SAAS,IAAI;AAC/C,0CAA0C,SAAS,IAAI;AACvD,kCAAkC,SAAS,IAAI;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,IAAI;AACvC,4DAA4D;AAC5D,gDAAgD,QAAQ;AACxD,oCAAoC,QAAQ;AAC5C,oCAAoC,UAAU;AAC9C,4CAA4C,GAAG,KAAK;AACpD,4CAA4C,GAAG,KAAK;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,mCAAmC,EAAE;AACrC,+BAA+B,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,GAAG;AAC/C,+BAA+B,GAAG;AAClC,iCAAiC,GAAG;AACpC;AACA;AACA;AACA;AACA,oCAAoC,GAAG,IAAI,GAAG;AAC9C,8BAA8B,GAAG,IAAI,GAAG;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;ACnOa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uCAAuC,mBAAO,CAAC,KAAwB;AACvE,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,uBAAuB,mBAAO,CAAC,KAAmB;AAClD;AACA;AACA;AACA;AACA,MAAM,KAAK;AACX;AACA;AACA,uBAAuB,IAAI;AAC3B;AACA,2BAA2B;AAC3B,4BAA4B;AAC5B,0BAA0B;AAC1B,4BAA4B;AAC5B,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,oCAAoC;AACrF;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA,gCAAgC;AAChC,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,oCAAoC;AAC7D;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA,6BAA6B,YAAY;AACzC,8BAA8B,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,4CAA4C,MAAM,GAAG;AACrD;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA;AACA;AACA;AACA,kCAAkC,cAAc;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA,qCAAqC;AACrC,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,0BAA0B;AACnD;AACA;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B,yBAAyB;AACzB;AACA;AACA,6CAA6C,oCAAoC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA,mDAAmD,YAAY;AAC/D;AACA;AACA;AACA;AACA;AACA,oCAAoC,eAAe;AACnD;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D,oCAAoC;AAClG;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,0BAA0B;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,eAAe;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,eAAe;AACjD;AACA;AACA,yBAAyB,4BAA4B;AACrD,UAAU,gDAAgD;AAC1D;AACA;AACA;AACA;AACA,6BAA6B,uCAAuC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,gBAAgB,OAAO,iBAAiB;AAC9D,4BAA4B,gBAAgB,eAAe,iBAAiB;AAC5E;AACA;AACA;AACA,sBAAsB,gBAAgB,2BAA2B,gBAAgB,cAAc,iBAAiB;AAChH;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,gBAAgB;AAC/C,sBAAsB,gBAAgB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA,sBAAsB,gBAAgB,OAAO,iBAAiB;AAC9D,4BAA4B,gBAAgB,eAAe,iBAAiB;AAC5E;AACA;AACA;AACA,sBAAsB,gBAAgB,OAAO,iBAAiB;AAC9D,4BAA4B,gBAAgB,eAAe,iBAAiB;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA,oBAAoB,gBAAgB,oBAAoB,iBAAiB;AACzE,0BAA0B,gBAAgB,oCAAoC,iBAAiB;AAC/F;AACA;AACA;AACA,oBAAoB,gBAAgB,oBAAoB,iBAAiB;AACzE,0BAA0B,gBAAgB,oCAAoC,iBAAiB;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB,eAAe,kBAAkB,eAAe,kBAAkB;AAC3G,2BAA2B,iBAAiB,OAAO,kBAAkB,OAAO,kBAAkB;AAC9F,4BAA4B,iBAAiB,cAAc,kBAAkB,aAAa,kBAAkB;AAC5G;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA,4BAA4B,kBAAkB;AAC9C,6BAA6B,kBAAkB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,iBAAiB,iBAAiB,cAAc;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,UAAU;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC,wCAAwC,eAAe;AACvD;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC,wCAAwC,eAAe;AACvD;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA,4BAA4B,QAAQ;AACpC,6CAA6C,gBAAgB,gBAAgB;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,YAAY;AAChC;AACA,oBAAoB,oBAAoB;AACxC;AACA;AACA;AACA;AACA,4CAA4C,iBAAiB;AAC7D;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA,eAAe,gBAAgB,MAAM,gBAAgB;AACrD;AACA;AACA;AACA,+DAA+D,OAAO;AACtE;AACA;AACA;AACA;AACA;AACA,kBAAe;AACf;;;;;;;AC5nBa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,+BAA+B,GAAG,sBAAsB;AACxD,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,kBAAkB,mBAAO,CAAC,KAAc;AACxC,uCAAuC,mBAAO,CAAC,KAAwB;AACvE,mBAAmB,mBAAO,CAAC,IAA0B;AACrD,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0EAA0E,UAAU;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,kHAAkH,GAAG,iBAAiB,GAAG;AACzI;AACA;AACA,+BAA+B;AAC/B,eAAe,uBAAuB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,CAAC;AACD;;;;;;;AC7Ha;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,8BAA8B,GAAG,uBAAuB;AACxD,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,gBAAgB,mBAAO,CAAC,KAAY;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB,8BAA8B;AAC9B;AACA;AACA;AACA,mCAAmC,sBAAsB;AACzD,kCAAkC,yBAAyB;AAC3D,gCAAgC,6CAA6C;AAC7E,uCAAuC,6CAA6C;AACpF;AACA;AACA,CAAC;AACD;;;;;;;AC/Da;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,6BAA6B;AAC7B,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,mBAAO,CAAC,KAAuB;AAC/B,6BAA6B;AAC7B,eAAe;AACf,CAAC;AACD;;;;;;;ACRa;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,wBAAwB,mBAAO,CAAC,KAAoB;AACpD,6BAA6B,mBAAO,CAAC,KAAoC;AACzE,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,sBAAsB,mBAAO,CAAC,KAA6B;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4EAA4E,oBAAoB,IAAI;AACpG;AACA,CAAC;AACD;;;;;;;AC5Ia;AACb;AACA,6CAA6C;AAC7C;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,+BAA+B,GAAG,6BAA6B;AAC/D,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,wCAAwC,mBAAO,CAAC,KAAoB;AACpE,gBAAgB,mBAAO,CAAC,KAAY;AACpC,qBAAqB,mBAAO,CAAC,KAAsB;AACnD,sBAAsB,mBAAO,CAAC,KAAiB;AAC/C,6BAA6B,mBAAO,CAAC,KAAwB;AAC7D,mBAAO,CAAC,KAAyB;AACjC,6BAA6B;AAC7B;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,kEAAkE,2BAA2B,IAAI;AACjG;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,uDAAuD;AAC5G;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;ACvEa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,wBAAwB,mBAAO,CAAC,KAAoB;AACpD,6BAA6B,mBAAO,CAAC,KAAwB;AAC7D,mBAAmB,mBAAO,CAAC,KAA0B;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,MAAM;AACN;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,YAAY;AACjC,4BAA4B,QAAQ;AACpC,wBAAwB,QAAQ;AAChC,wBAAwB,QAAQ;AAChC,wBAAwB,QAAQ;AAChC,wBAAwB,QAAQ;AAChC,wBAAwB,QAAQ;AAChC,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;AC9Ga;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,yBAAyB;AACzB,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,mBAAmB,mBAAO,CAAC,KAA0B;AACrD,uCAAuC,mBAAO,CAAC,KAAwB;AACvE,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,2CAA2C;AAC3C,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,oDAAoD;AACpD;AACA;AACA,KAAK;AACL;AACA;AACA,gDAAgD,sBAAsB;AACtE,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC3Ia;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qBAAqB,mBAAO,CAAC,KAAsB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA,wDAAwD,8CAA8C;AACtG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4FAA4F,2BAA2B,IAAI;AAC3H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+EAA+E,UAAU;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8FAA8F,UAAU;AACxG;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;;;;;;;AC3La;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,4BAA4B,GAAG,sBAAsB;AACrD,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,oBAAoB,mBAAO,CAAC,KAA2B;AACvD,sBAAsB;AACtB;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,oBAAoB;AAC/D,4BAA4B,wDAAwD,WAAW,sBAAsB;AACrH;;;;;;;ACjEa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,4BAA4B;AAC5B,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,wBAAwB,mBAAO,CAAC,KAAoB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,4BAA4B;AAC5B,eAAe,oBAAoB;AACnC,CAAC;AACD;;;;;;;AC1Da;AACb;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,yBAAyB,GAAG,mBAAmB;AAC/C,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,wBAAwB,mBAAO,CAAC,KAAoB;AACpD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,mBAAmB;AACnB,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,8DAA8D;AAChH;AACA,wCAAwC,cAAc;AACtD,yBAAyB,qDAAqD,WAAW,mBAAmB;AAC5G;;;;;;;AC9Ba;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,uBAAuB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,uBAAuB;AACnD;AACA;AACA,gCAAgC,sBAAsB;AACtD;AACA,yCAAyC;AACzC;AACA;AACA;AACA,gCAAgC,yBAAyB;AACzD;AACA;AACA;AACA,kDAAkD,mCAAmC;AACrF;AACA;AACA;AACA,0DAA0D,mCAAmC;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,cAAc;AAC9D;AACA;AACA;AACA,qCAAqC,0BAA0B;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,iCAAiC,eAAe;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yQAAyQ,EAAE;AAC3Q,8NAA8N,EAAE;AAChO;AACA;AACA,2NAA2N,EAAE;AAC7N;AACA;AACA;AACA;AACA,iOAAiO,cAAc;AAC/O;AACA,6BAA6B;AAC7B;AACA;AACA,aAAa;AACb,sHAAsH,cAAc;AACpI;AACA,sFAAsF,IAAI;AAC1F,qDAAqD,MAAM;AAC3D;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA,aAAa;AACb,eAAe,uBAAuB,SAAS,SAAS,IAAI,KAAK;AACjE,cAAc,cAAc;AAC5B,gBAAgB,kBAAkB;AAClC,uBAAuB;AACvB,sBAAsB;AACtB;AACA;AACA;AACA,eAAe,MAAM,sBAAsB,0DAA0D,aAAa,KAAK;AACvH,2CAA2C,4EAA4E;AACvH;AACA,qBAAqB,sBAAsB,2EAA2E,KAAK,aAAa,KAAK,eAAe;AAC5J,mBAAmB,sBAAsB,2EAA2E,KAAK,SAAS;AAClI;AACA;AACA;AACA,eAAe,MAAM,sBAAsB,0DAA0D,aAAa,KAAK;AACvH,2CAA2C,4EAA4E;AACvH;AACA,qBAAqB,sBAAsB,2EAA2E,KAAK,aAAa,KAAK,eAAe;AAC5J,mBAAmB,sBAAsB,2EAA2E,KAAK,SAAS;AAClI;AACA;AACA,eAAe,QAAQ,EAAE;AACzB,eAAe,OAAO,EAAE;AACxB,cAAc,IAAI,sBAAsB,6DAA6D,KAAK,SAAS;AACnH,cAAc,MAAM,sBAAsB,yDAAyD,aAAa,KAAK;AACrH,wCAAwC,2EAA2E;AACnH,eAAe,MAAM;AACrB,0EAA0E,aAAa;AACvF,aAAa;AACb;AACA;AACA;AACA,6BAA6B,EAAE;AAC/B;AACA,6DAA6D,MAAM;AACnE;AACA;AACA;AACA,4DAA4D,MAAM;AAClE,oBAAoB,MAAM,sBAAsB,+DAA+D,aAAa,KAAK;AACjI;AACA;AACA,0CAA0C,2EAA2E;AACrH;AACA,+BAA+B;AAC/B,oBAAoB,sBAAsB,0EAA0E,KAAK,aAAa,KAAK,eAAe;AAC1J,kBAAkB,sBAAsB,0EAA0E,KAAK,SAAS;AAChI;AACA,8CAA8C;AAC9C,kCAAkC,IAAI,eAAe,IAAI;AACzD;AACA,oBAAoB,MAAM,sBAAsB,+DAA+D,aAAa,KAAK,aAAa,KAAK;AACnJ,uBAAuB,MAAM,sBAAsB,kEAAkE,aAAa,KAAK,aAAa,KAAK;AACzJ,wBAAwB,MAAM,sBAAsB,mEAAmE,aAAa,KAAK,aAAa,KAAK;AAC3J,0BAA0B,MAAM,sBAAsB,qEAAqE,aAAa,MAAM,aAAa,KAAK;AAChK,qBAAqB,MAAM,sBAAsB,gEAAgE,aAAa,KAAK;AACnI,qBAAqB,OAAO,MAAM;AAClC,gFAAgF,aAAa,KAAK,aAAa;AAC/G,gGAAgG,MAAM,aAAa;AACnH,aAAa;AACb,kBAAkB,MAAM,sBAAsB,6DAA6D,aAAa,KAAK;AAC7H,kBAAkB,MAAM,sBAAsB,6DAA6D,aAAa,KAAK;AAC7H;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA,aAAa;AACb,0CAA0C,+BAA+B;AACzE,6CAA6C,IAAI;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA,qCAAqC;AACrC;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,qCAAqC,iCAAiC,mBAAmB;AACzF,qCAAqC,iCAAiC,mBAAmB;AACzF,qCAAqC,iCAAiC,mBAAmB;AACzF,qCAAqC,iCAAiC,mBAAmB;AACzF,4CAA4C,iCAAiC,mBAAmB;AAChG,qCAAqC,iCAAiC,mBAAmB;AACzF,qCAAqC,iCAAiC,mBAAmB;AACzF,sCAAsC,mCAAmC,mBAAmB;AAC5F,wCAAwC,yCAAyC,mBAAmB;AACpG,8CAA8C,SAAS,aAAa;AACpE,gDAAgD,SAAS,oBAAoB;AAC7E,mDAAmD,SAAS,iCAAiC;AAC7F,wCAAwC,WAAW;AACnD,6CAA6C,WAAW;AACxD,sCAAsC,SAAS,uBAAuB;AACtE,wCAAwC,qCAAqC;AAC7E,4CAA4C,yCAAyC;AACrF,kDAAkD,+CAA+C;AACjG,2CAA2C,SAAS,iCAAiC;AACrF,iDAAiD,SAAS,8BAA8B;AACxF,sCAAsC,mCAAmC;AACzE,sCAAsC,mCAAmC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,mCAAmC;AAC1D;AACA;AACA,2BAA2B,6BAA6B;AACxD;AACA;AACA,SAAS;AACT,uCAAuC;AACvC,KAAK;AACL;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,iBAAiB;AACjB,sBAAsB,MAAM;AAC5B,2BAA2B,YAAY,0BAA0B,GAAG,UAAU,0BAA0B,GAAG;AAC3G,iBAAiB;AACjB,sBAAsB,MAAM;AAC5B,2BAA2B,YAAY,0BAA0B,GAAG,UAAU,0BAA0B,GAAG;AAC3G,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,+BAA+B;AAC/B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B,kCAAkC;AAC7D,4BAA4B;AAC5B,iBAAiB;AACjB;AACA,kCAAkC;AAClC,iBAAiB;AACjB;AACA,+BAA+B;AAC/B,iBAAiB;AACjB;AACA,iCAAiC;AACjC,iBAAiB;AACjB;AACA,iCAAiC,+BAA+B;AAChE,8BAA8B,2CAA2C;AACzE,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B,kCAAkC;AAC7D,6BAA6B,+BAA+B;AAC5D,2BAA2B,gCAAgC;AAC3D,+BAA+B,gCAAgC;AAC/D,4BAA4B,2CAA2C;AACvE,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,6BAA6B;AAC7B,iBAAiB;AACjB;AACA,oCAAoC,kCAAkC,mCAAmC;AACzG,iBAAiB;AACjB;AACA,oCAAoC;AACpC,iBAAiB;AACjB;AACA,6BAA6B,wDAAwD;AACrF,2BAA2B,+BAA+B;AAC1D,2BAA2B,sBAAsB,4BAA4B,mBAAmB;AAChG,2BAA2B,gCAAgC;AAC3D,4BAA4B,gCAAgC;AAC5D,+BAA+B,sBAAsB,4BAA4B;AACjF,iBAAiB;AACjB;AACA,6BAA6B,sBAAsB,mCAAmC;AACtF,iBAAiB;AACjB;AACA,2BAA2B,sBAAsB,mCAAmC,mBAAmB;AACvG,2BAA2B,sBAAsB,mCAAmC;AACpF,iBAAiB;AACjB;AACA,+BAA+B,YAAY,4BAA4B,2BAA2B,4BAA4B,mBAAmB;AACjJ,2BAA2B,WAAW,8BAA8B;AACpE,2BAA2B,sBAAsB,mCAAmC,mBAAmB;AACvG,4BAA4B,YAAY,4BAA4B,IAAI,4BAA4B,mBAAmB;AACvH,2BAA2B,eAAe;AAC1C,2BAA2B,WAAW,qCAAqC,kBAAkB;AAC7F,2BAA2B,WAAW,qCAAqC,kBAAkB;AAC7F,iCAAiC,WAAW,oCAAoC,kBAAkB;AAClG,gCAAgC,sBAAsB,4BAA4B;AAClF,iBAAiB;AACjB;AACA,6BAA6B;AAC7B,iBAAiB;AACjB;AACA,+CAA+C,+BAA+B;AAC9E,8BAA8B,2CAA2C;AACzE,mCAAmC;AACnC,iBAAiB;AACjB;AACA,2BAA2B,+BAA+B;AAC1D,6BAA6B,gCAAgC;AAC7D,2BAA2B,2CAA2C;AACtE,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,gCAAgC;AAChC,iBAAiB;AACjB;AACA,2BAA2B,8BAA8B;AACzD,2BAA2B,qBAAqB;AAChD,6BAA6B,oBAAoB;AACjD,yCAAyC,mCAAmC;AAC5E,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,6BAA6B,sBAAsB,gDAAgD,GAAG;AACtG,2BAA2B,sBAAsB,gDAAgD;AACjG,iBAAiB;AACjB;AACA,8BAA8B,iCAAiC;AAC/D,gCAAgC;AAChC,iBAAiB;AACjB;AACA,wCAAwC,sBAAsB,8BAA8B,mBAAmB;AAC/G,2BAA2B,YAAY,4BAA4B,IAAI,qCAAqC;AAC5G,iBAAiB;AACjB;AACA,2BAA2B,sBAAsB,8CAA8C;AAC/F,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB,kBAAkB;AAClB,gCAAgC,iEAAiE;AACjG,iCAAiC,iEAAiE;AAClG,2BAA2B;AAC3B,iBAAiB;AACjB,oBAAoB;AACpB,0CAA0C,uDAAuD;AACjG,6CAA6C;AAC7C,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB,mBAAmB,MAAM;AACzB,kCAAkC,+BAA+B;AACjE,2BAA2B,gCAAgC;AAC3D,6BAA6B,kCAAkC;AAC/D,2BAA2B,gCAAgC;AAC3D,sCAAsC;AACtC,iBAAiB;AACjB,4BAA4B,OAAO;AACnC,kCAAkC,+BAA+B;AACjE,2BAA2B,gCAAgC;AAC3D,6BAA6B,kCAAkC;AAC/D,2BAA2B,gCAAgC;AAC3D,mCAAmC,eAAe;AAClD,4BAA4B;AAC5B,iBAAiB;AACjB,mBAAmB,uBAAuB;AAC1C,wCAAwC;AACxC,iBAAiB;AACjB,mBAAmB,MAAM,qBAAqB,OAAO;AACrD,kCAAkC,+BAA+B;AACjE,2BAA2B,gCAAgC;AAC3D,6BAA6B,+BAA+B;AAC5D,6BAA6B,gCAAgC;AAC7D,oCAAoC;AACpC,iBAAiB;AACjB;AACA,uDAAuD,YAAY,4BAA4B;AAC/F,iBAAiB;AACjB;AACA,wCAAwC,YAAY,4BAA4B,IAAI,4BAA4B;AAChH,iBAAiB;AACjB,mBAAmB;AACnB,2BAA2B,YAAY,4BAA4B,IAAI,qCAAqC;AAC5G,iBAAiB;AACjB,mBAAmB;AACnB,2BAA2B,WAAW,4BAA4B;AAClE,iBAAiB;AACjB,kBAAkB,IAAI;AACtB,6CAA6C,+BAA+B;AAC5E,0CAA0C;AAC1C,iBAAiB;AACjB;AACA,2BAA2B,eAAe;AAC1C,6CAA6C,+BAA+B;AAC5E,8BAA8B,eAAe;AAC7C,wCAAwC;AACxC,iBAAiB;AACjB,wBAAwB,MAAM;AAC9B,2BAA2B,YAAY,4BAA4B;AACnE,iBAAiB;AACjB,wBAAwB,MAAM;AAC9B,2BAA2B,YAAY,4BAA4B;AACnE,iBAAiB;AACjB,2BAA2B,MAAM;AACjC,2BAA2B,YAAY,4BAA4B;AACnE,iBAAiB;AACjB,4BAA4B,MAAM;AAClC,2BAA2B,YAAY,4BAA4B;AACnE,iBAAiB;AACjB,8BAA8B,MAAM;AACpC,2BAA2B,YAAY,4BAA4B;AACnE,iBAAiB;AACjB,yBAAyB,OAAO,MAAM;AACtC,2BAA2B,YAAY,4BAA4B;AACnE,iBAAiB;AACjB,yBAAyB,MAAM;AAC/B,2BAA2B,YAAY,4BAA4B;AACnE,iBAAiB;AACjB,sBAAsB,MAAM;AAC5B,2BAA2B,YAAY,4BAA4B;AACnE,iBAAiB;AACjB;AACA,2BAA2B,YAAY,4BAA4B;AACnE,iBAAiB;AACjB,sBAAsB,MAAM;AAC5B,2BAA2B,sBAAsB,0BAA0B,GAAG,UAAU,0BAA0B,GAAG;AACrH,iBAAiB;AACjB,wBAAwB,MAAM;AAC9B,kDAAkD,2CAA2C;AAC7F,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B,YAAY,4BAA4B;AACnE,iBAAiB;AACjB;AACA,2BAA2B,kDAAkD;AAC7E,4BAA4B,+DAA+D;AAC3F,yCAAyC,oDAAoD;AAC7F,2BAA2B;AAC3B;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,mBAAmB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,iBAAiB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB,+CAA+C,kBAAkB,mBAAmB;AACpF,gDAAgD,mBAAmB,mBAAmB;AACtF,2DAA2D,iCAAiC,mBAAmB;AAC/G,4DAA4D,kCAAkC,mBAAmB;AACjH,0DAA0D,8BAA8B,mBAAmB;AAC3G,0DAA0D,8BAA8B,mBAAmB;AAC3G;AACA,6BAA6B;AAC7B,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA,iCAAiC;AACjC;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,qBAAqB;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,oCAAoC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,oCAAoC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,iCAAiC;AACjC;AACA,+BAA+B;AAC/B;AACA,iBAAiB;AACjB;AACA,6BAA6B;AAC7B,iBAAiB;AACjB;AACA,6BAA6B;AAC7B,iBAAiB;AACjB;AACA,6BAA6B;AAC7B,iBAAiB;AACjB;AACA,6BAA6B;AAC7B,iBAAiB;AACjB;AACA,6BAA6B;AAC7B,iBAAiB;AACjB,6CAA6C,cAAc,mBAAmB;AAC9E,+CAA+C,gBAAgB,mBAAmB;AAClF,8CAA8C,eAAe,mBAAmB;AAChF,+CAA+C,gBAAgB,mBAAmB;AAClF,8CAA8C,eAAe,mBAAmB;AAChF,wDAAwD,SAAS;AACjE;AACA,SAAS;AACT;AACA;AACA;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB,mBAAmB,MAAM;AACzB,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B,WAAW;AACtC,iBAAiB;AACjB;AACA,2BAA2B;AAC3B;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B,WAAW;AACtC,iBAAiB;AACjB,sBAAsB,MAAM;AAC5B,2BAA2B,YAAY,0BAA0B,GAAG,UAAU,0BAA0B,GAAG;AAC3G,iBAAiB;AACjB,wBAAwB,MAAM;AAC9B,2BAA2B;AAC3B,iBAAiB;AACjB,mBAAmB,MAAM;AACzB,2BAA2B;AAC3B,iBAAiB;AACjB,kBAAkB,MAAM;AACxB,2BAA2B,YAAY,0BAA0B,GAAG,YAAY,0BAA0B,GAAG;AAC7G,iBAAiB;AACjB;AACA,2BAA2B;AAC3B;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA,2BAA2B;AAC3B,iBAAiB;AACjB,kBAAkB,IAAI;AACtB,2BAA2B;AAC3B,iBAAiB;AACjB,mBAAmB,MAAM;AACzB,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB,sBAAsB,MAAM;AAC5B,2BAA2B,sBAAsB,0BAA0B,GAAG,UAAU,0BAA0B,GAAG;AACrH,iBAAiB;AACjB,4BAA4B,MAAM;AAClC,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B;AACA,aAAa;AACb;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB,mBAAmB,MAAM;AACzB,2BAA2B;AAC3B,iBAAiB;AACjB,kBAAkB,MAAM;AACxB,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B,WAAW;AACtC,iBAAiB;AACjB,yBAAyB,OAAO,MAAM;AACtC,2BAA2B;AAC3B,iBAAiB;AACjB,yBAAyB,MAAM;AAC/B,2BAA2B;AAC3B,iBAAiB;AACjB,sBAAsB,MAAM;AAC5B,2BAA2B;AAC3B,iBAAiB;AACjB,sBAAsB,MAAM;AAC5B,2BAA2B,YAAY,0BAA0B,GAAG,UAAU,0BAA0B,GAAG;AAC3G,iBAAiB;AACjB,4BAA4B,MAAM;AAClC,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B;AACA,aAAa;AACb;AACA;AACA,6BAA6B;AAC7B,iBAAiB;AACjB;AACA,6BAA6B;AAC7B;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B,WAAW;AACtC,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B,WAAW;AACtC,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B,WAAW;AACtC,iBAAiB;AACjB,mBAAmB,MAAM;AACzB,2BAA2B;AAC3B,iBAAiB;AACjB,kBAAkB,MAAM;AACxB,2BAA2B;AAC3B,iBAAiB;AACjB,yBAAyB,OAAO,MAAM;AACtC,2BAA2B;AAC3B,iBAAiB;AACjB,yBAAyB,MAAM;AAC/B,2BAA2B;AAC3B,iBAAiB;AACjB,sBAAsB,MAAM;AAC5B,2BAA2B;AAC3B,iBAAiB;AACjB,sBAAsB,MAAM;AAC5B,2BAA2B,YAAY,0BAA0B,GAAG,UAAU,0BAA0B,GAAG;AAC3G,iBAAiB;AACjB,4BAA4B,MAAM;AAClC,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B;AACA,aAAa;AACb;AACA;AACA,6BAA6B;AAC7B;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B,WAAW;AACtC,iBAAiB;AACjB;AACA,2BAA2B;AAC3B;AACA,aAAa;AACb;AACA,qDAAqD,SAAS;AAC9D;AACA,SAAS;AACT;AACA;AACA;AACA,2BAA2B;AAC3B,iBAAiB;AACjB,sBAAsB,MAAM;AAC5B,2BAA2B;AAC3B,iBAAiB;AACjB,sBAAsB,MAAM;AAC5B,2BAA2B,sBAAsB,0BAA0B,GAAG,UAAU,0BAA0B,GAAG;AACrH,iBAAiB;AACjB,kBAAkB,IAAI,YAAY,MAAM;AACxC,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B;AACA,aAAa;AACb;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,2BAA2B;AAC3B,iBAAiB;AACjB,sBAAsB,MAAM;AAC5B,2BAA2B;AAC3B,iBAAiB;AACjB,sBAAsB,MAAM;AAC5B,2BAA2B,sBAAsB,0BAA0B,GAAG,UAAU,0BAA0B,GAAG;AACrH,iBAAiB;AACjB,kBAAkB,IAAI,YAAY,MAAM;AACxC,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B;AACA,aAAa;AACb;AACA,yDAAyD,gCAAgC,UAAU,GAAG,mBAAmB;AACzH;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B;AACA,aAAa;AACb;AACA,uCAAuC,SAAS;AAChD;AACA,SAAS;AACT;AACA;AACA;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB,kBAAkB,MAAM;AACxB,6BAA6B;AAC7B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,6BAA6B;AAC7B,iBAAiB;AACjB;AACA,6BAA6B,WAAW,mCAAmC;AAC3E,iBAAiB;AACjB;AACA,6BAA6B;AAC7B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB,kBAAkB,IAAI;AACtB,6BAA6B,+BAA+B;AAC5D,2BAA2B,0CAA0C;AACrE,6BAA6B;AAC7B;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,eAAe;AAC1D;AACA;AACA,2CAA2C,gBAAgB;AAC3D;AACA;AACA;AACA;AACA,sCAAsC,aAAa;AACnD;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,aAAa;AAC7C;AACA,iBAAiB;AACjB,wDAAwD,SAAS,wCAAwC;AACzG,uCAAuC,SAAS,wBAAwB;AACxE;AACA;AACA,6DAA6D,MAAM;AACnE;AACA;AACA;AACA,6DAA6D,MAAM;AACnE;AACA;AACA;AACA;AACA,qEAAqE,EAAE,QAAQ,EAAE,OAAO;AACxF,qEAAqE,EAAE,QAAQ,EAAE,OAAO;AACxF;AACA;AACA,qEAAqE,EAAE,QAAQ,EAAE,OAAO;AACxF,qEAAqE,EAAE,QAAQ,EAAE,OAAO;AACxF;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA,2CAA2C,cAAc;AACzD;AACA;AACA,2CAA2C,YAAY;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB,mBAAmB,MAAM;AACzB,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B,gCAAgC;AAC3D,2BAA2B;AAC3B,iBAAiB;AACjB,kBAAkB,IAAI;AACtB,2BAA2B;AAC3B;AACA,aAAa;AACb;AACA,sCAAsC,SAAS,wBAAwB;AACvE,gDAAgD,gBAAgB,UAAU,GAAG,mBAAmB;AAChG,uCAAuC,SAAS,wBAAwB;AACxE;AACA;AACA;AACA,kEAAkE,MAAM;AACxE;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,2BAA2B,qBAAqB;AAChD,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B,iBAAiB;AACjB;AACA,2BAA2B;AAC3B;AACA,aAAa;AACb;AACA,uCAAuC,SAAS,0BAA0B;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D,OAAO;AACrE;AACA,uCAAuC,yBAAyB;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,OAAO;AAC3C;AACA,uCAAuC,yBAAyB;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,YAAY;AAChC;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,aAAa;AAC9C;AACA;AACA;AACA;AACA,6BAA6B,WAAW,GAAG;AAC3C,8BAA8B,WAAW,sBAAsB,EAAE,WAAW,sBAAsB;AAClG;AACA,6BAA6B,WAAW,GAAG;AAC3C,8BAA8B,WAAW,WAAW,GAAG,OAAO,sBAAsB;AACpF,8BAA8B,WAAW,EAAE,OAAO,WAAW,uBAAuB;AACpF;AACA;AACA;AACA,iCAAiC,aAAa;AAC9C;AACA;AACA;AACA;AACA;AACA,iCAAiC,WAAW,GAAG;AAC/C;AACA;AACA,kCAAkC,aAAa;AAC/C;AACA;AACA,kCAAkC,WAAW,cAAc;AAC3D;AACA;AACA;AACA;AACA,iCAAiC,WAAW,GAAG;AAC/C,kCAAkC,aAAa;AAC/C;AACA;AACA,iCAAiC,WAAW,GAAG;AAC/C,kCAAkC,WAAW,cAAc;AAC3D;AACA;AACA;AACA;AACA,iCAAiC,WAAW,GAAG;AAC/C,kCAAkC,WAAW,cAAc;AAC3D;AACA;AACA,iCAAiC,WAAW,GAAG;AAC/C,kCAAkC,aAAa;AAC/C;AACA;AACA;AACA;AACA,gCAAgC,eAAe;AAC/C;AACA;AACA;AACA,2EAA2E,EAAE;AAC7E,oCAAoC,eAAe;AACnD;AACA;AACA,kCAAkC,eAAe;AACjD;AACA;AACA;AACA,gCAAgC,eAAe;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,gBAAgB,eAAe;AAC/D,oCAAoC,sBAAsB,WAAW,WAAW,UAAU;AAC1F;AACA;AACA,gCAAgC,wCAAwC,uCAAuC;AAC/G,oCAAoC,sBAAsB,WAAW,WAAW,UAAU;AAC1F;AACA;AACA;AACA;AACA;AACA,8BAA8B,wCAAwC,uCAAuC;AAC7G;AACA;AACA,iCAAiC,wCAAwC,uCAAuC;AAChH;AACA;AACA,kCAAkC,wCAAwC,uCAAuC;AACjH;AACA;AACA,oCAAoC,uCAAuC,EAAE,uCAAuC;AACpH;AACA;AACA,wBAAwB,QAAQ,oBAAoB,4CAA4C;AAChG;AACA;AACA,+BAA+B,kBAAkB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,eAAe,cAAc;AAC5E;AACA;AACA;AACA,oDAAoD,WAAW,gBAAgB,cAAc;AAC7F;AACA;AACA,oDAAoD,WAAW,gBAAgB,cAAc;AAC7F;AACA;AACA,oCAAoC,UAAU,iBAAiB;AAC/D;AACA;AACA;AACA,wCAAwC,cAAc;AACtD;AACA,mCAAmC,cAAc;AACjD,oCAAoC,UAAU,oBAAoB;AAClE;AACA;AACA;AACA,gCAAgC,UAAU,uBAAuB;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,EAAE;AAC1B;AACA;AACA,wBAAwB,eAAe;AACvC;AACA;AACA,wBAAwB,eAAe;AACvC;AACA;AACA,wBAAwB,eAAe;AACvC;AACA;AACA,8BAA8B,EAAE;AAChC;AACA;AACA,2BAA2B,OAAO;AAClC;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,OAAO;AACzC;AACA;AACA;AACA;AACA;AACA,wBAAwB,MAAM;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,+BAA+B,EAAE;AACjC,+BAA+B,EAAE;AACjC,+BAA+B,EAAE;AACjC,+BAA+B,EAAE;AACjC,+BAA+B,QAAQ;AACvC,+BAA+B,QAAQ;AACvC,+BAA+B,aAAa;AAC5C,gCAAgC,OAAO,YAAY,GAAG,YAAY,cAAc;AAChF,gCAAgC,OAAO,YAAY,GAAG,OAAO,YAAY,cAAc,EAAE;AACzF,iCAAiC,OAAO,YAAY,GAAG,OAAO,YAAY,cAAc,EAAE;AAC1F,iCAAiC,OAAO,YAAY,GAAG,OAAO,YAAY,GAAG,aAAa;AAC1F,kCAAkC,QAAQ,QAAQ,QAAQ;AAC1D,mCAAmC,QAAQ,QAAQ,QAAQ,QAAQ;AACnE,gCAAgC,aAAa;AAC7C,gCAAgC,YAAY;AAC5C,+BAA+B,EAAE;AACjC,+BAA+B,EAAE;AACjC;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,iCAAiC,IAAI;AACrC,iCAAiC,IAAI;AACrC,iCAAiC,IAAI;AACrC,iCAAiC,IAAI;AACrC,iCAAiC,IAAI;AACrC,kCAAkC,OAAO;AACzC,kCAAkC,OAAO;AACzC,oCAAoC,OAAO;AAC3C,wCAAwC,WAAW;AACnD,0CAA0C,WAAW;AACrD,6CAA6C;AAC7C,+CAA+C;AAC/C,2CAA2C;AAC3C,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/AllPackages.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/action/ActionConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/ams/AmsConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/ams/AmsItems.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/ams/AmsMappings.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/ams/AmsMethods.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/amscd/AmsCdConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/amscd/AmsCdMappings.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/amscd/AmsCdMethods.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/bbox/BboxConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/boldsymbol/BoldsymbolConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/braket/BraketConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/braket/BraketItems.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/braket/BraketMappings.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/braket/BraketMethods.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/bussproofs/BussproofsConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/bussproofs/BussproofsItems.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/bussproofs/BussproofsMappings.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/bussproofs/BussproofsMethods.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/bussproofs/BussproofsUtil.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/cancel/CancelConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/cases/CasesConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/centernot/CenternotConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/color/ColorConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/color/ColorConstants.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/color/ColorMethods.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/color/ColorUtil.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/colortbl/ColortblConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/colorv2/ColorV2Configuration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/configmacros/ConfigMacrosConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/empheq/EmpheqConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/empheq/EmpheqUtil.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/enclose/EncloseConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/extpfeil/ExtpfeilConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/gensymb/GensymbConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/html/HtmlConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/html/HtmlMethods.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/mathtools/MathtoolsConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/mathtools/MathtoolsItems.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/mathtools/MathtoolsMappings.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/mathtools/MathtoolsMethods.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/mathtools/MathtoolsTags.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/mathtools/MathtoolsUtil.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/mhchem/MhchemConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/newcommand/NewcommandConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/newcommand/NewcommandItems.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/newcommand/NewcommandMappings.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/newcommand/NewcommandMethods.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/newcommand/NewcommandUtil.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/noerrors/NoErrorsConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/noundefined/NoUndefinedConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/physics/PhysicsConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/physics/PhysicsItems.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/physics/PhysicsMappings.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/physics/PhysicsMethods.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/setoptions/SetOptionsConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/tagformat/TagFormatConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/textcomp/TextcompConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/textcomp/TextcompMappings.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/textmacros/TextMacrosConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/textmacros/TextMacrosMappings.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/textmacros/TextMacrosMethods.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/textmacros/TextParser.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/unicode/UnicodeConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/upgreek/UpgreekConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/verb/VerbConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mhchemparser/dist/mhchemParser.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AllPackages = void 0;\nrequire(\"./base/BaseConfiguration.js\");\nrequire(\"./action/ActionConfiguration.js\");\nrequire(\"./ams/AmsConfiguration.js\");\nrequire(\"./amscd/AmsCdConfiguration.js\");\nrequire(\"./bbox/BboxConfiguration.js\");\nrequire(\"./boldsymbol/BoldsymbolConfiguration.js\");\nrequire(\"./braket/BraketConfiguration.js\");\nrequire(\"./bussproofs/BussproofsConfiguration.js\");\nrequire(\"./cancel/CancelConfiguration.js\");\nrequire(\"./cases/CasesConfiguration.js\");\nrequire(\"./centernot/CenternotConfiguration.js\");\nrequire(\"./color/ColorConfiguration.js\");\nrequire(\"./colorv2/ColorV2Configuration.js\");\nrequire(\"./colortbl/ColortblConfiguration.js\");\nrequire(\"./configmacros/ConfigMacrosConfiguration.js\");\nrequire(\"./empheq/EmpheqConfiguration.js\");\nrequire(\"./enclose/EncloseConfiguration.js\");\nrequire(\"./extpfeil/ExtpfeilConfiguration.js\");\nrequire(\"./gensymb/GensymbConfiguration.js\");\nrequire(\"./html/HtmlConfiguration.js\");\nrequire(\"./mathtools/MathtoolsConfiguration.js\");\nrequire(\"./mhchem/MhchemConfiguration.js\");\nrequire(\"./newcommand/NewcommandConfiguration.js\");\nrequire(\"./noerrors/NoErrorsConfiguration.js\");\nrequire(\"./noundefined/NoUndefinedConfiguration.js\");\nrequire(\"./physics/PhysicsConfiguration.js\");\nrequire(\"./setoptions/SetOptionsConfiguration.js\");\nrequire(\"./tagformat/TagFormatConfiguration.js\");\nrequire(\"./textcomp/TextcompConfiguration.js\");\nrequire(\"./textmacros/TextMacrosConfiguration.js\");\nrequire(\"./upgreek/UpgreekConfiguration.js\");\nrequire(\"./unicode/UnicodeConfiguration.js\");\nrequire(\"./verb/VerbConfiguration.js\");\nif (typeof MathJax !== 'undefined' && MathJax.loader) {\n    MathJax.loader.preLoad('[tex]/action', '[tex]/ams', '[tex]/amscd', '[tex]/bbox', '[tex]/boldsymbol', '[tex]/braket', '[tex]/bussproofs', '[tex]/cancel', '[tex]/cases', '[tex]/centernot', '[tex]/color', '[tex]/colorv2', '[tex]/colortbl', '[tex]/empheq', '[tex]/enclose', '[tex]/extpfeil', '[tex]/gensymb', '[tex]/html', '[tex]/mathtools', '[tex]/mhchem', '[tex]/newcommand', '[tex]/noerrors', '[tex]/noundefined', '[tex]/physics', '[tex]/upgreek', '[tex]/unicode', '[tex]/verb', '[tex]/configmacros', '[tex]/tagformat', '[tex]/textcomp', '[tex]/textmacros', '[tex]/setoptions');\n}\nexports.AllPackages = [\n    'base',\n    'action',\n    'ams',\n    'amscd',\n    'bbox',\n    'boldsymbol',\n    'braket',\n    'bussproofs',\n    'cancel',\n    'cases',\n    'centernot',\n    'color',\n    'colortbl',\n    'empheq',\n    'enclose',\n    'extpfeil',\n    'gensymb',\n    'html',\n    'mathtools',\n    'mhchem',\n    'newcommand',\n    'noerrors',\n    'noundefined',\n    'upgreek',\n    'unicode',\n    'verb',\n    'configmacros',\n    'tagformat',\n    'textcomp',\n    'textmacros'\n];\n//# sourceMappingURL=AllPackages.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ActionConfiguration = exports.ActionMethods = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar TexParser_js_1 = __importDefault(require(\"../TexParser.js\"));\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar BaseMethods_js_1 = __importDefault(require(\"../base/BaseMethods.js\"));\nexports.ActionMethods = {};\nexports.ActionMethods.Macro = BaseMethods_js_1.default.Macro;\nexports.ActionMethods.Toggle = function (parser, name) {\n    var children = [];\n    var arg;\n    while ((arg = parser.GetArgument(name)) !== '\\\\endtoggle') {\n        children.push(new TexParser_js_1.default(arg, parser.stack.env, parser.configuration).mml());\n    }\n    parser.Push(parser.create('node', 'maction', children, { actiontype: 'toggle' }));\n};\nexports.ActionMethods.Mathtip = function (parser, name) {\n    var arg = parser.ParseArg(name);\n    var tip = parser.ParseArg(name);\n    parser.Push(parser.create('node', 'maction', [arg, tip], { actiontype: 'tooltip' }));\n};\nnew SymbolMap_js_1.CommandMap('action-macros', {\n    toggle: 'Toggle',\n    mathtip: 'Mathtip',\n    texttip: ['Macro', '\\\\mathtip{#1}{\\\\text{#2}}', 2]\n}, exports.ActionMethods);\nexports.ActionConfiguration = Configuration_js_1.Configuration.create('action', { handler: { macro: ['action-macros'] } });\n//# sourceMappingURL=ActionConfiguration.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AmsConfiguration = exports.AmsTags = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar AmsItems_js_1 = require(\"./AmsItems.js\");\nvar Tags_js_1 = require(\"../Tags.js\");\nvar AmsMethods_js_1 = require(\"./AmsMethods.js\");\nrequire(\"./AmsMappings.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar AmsTags = (function (_super) {\n    __extends(AmsTags, _super);\n    function AmsTags() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return AmsTags;\n}(Tags_js_1.AbstractTags));\nexports.AmsTags = AmsTags;\nvar init = function (config) {\n    new SymbolMap_js_1.CommandMap(AmsMethods_js_1.NEW_OPS, {}, {});\n    config.append(Configuration_js_1.Configuration.local({ handler: { macro: [AmsMethods_js_1.NEW_OPS] },\n        priority: -1 }));\n};\nexports.AmsConfiguration = Configuration_js_1.Configuration.create('ams', {\n    handler: {\n        character: ['AMSmath-operatorLetter'],\n        delimiter: ['AMSsymbols-delimiter', 'AMSmath-delimiter'],\n        macro: ['AMSsymbols-mathchar0mi', 'AMSsymbols-mathchar0mo',\n            'AMSsymbols-delimiter', 'AMSsymbols-macros',\n            'AMSmath-mathchar0mo', 'AMSmath-macros', 'AMSmath-delimiter'],\n        environment: ['AMSmath-environment']\n    },\n    items: (_a = {},\n        _a[AmsItems_js_1.MultlineItem.prototype.kind] = AmsItems_js_1.MultlineItem,\n        _a[AmsItems_js_1.FlalignItem.prototype.kind] = AmsItems_js_1.FlalignItem,\n        _a),\n    tags: { 'ams': AmsTags },\n    init: init,\n    config: function (_config, jax) {\n        if (jax.parseOptions.options.multlineWidth) {\n            jax.parseOptions.options.ams.multlineWidth = jax.parseOptions.options.multlineWidth;\n        }\n        delete jax.parseOptions.options.multlineWidth;\n    },\n    options: {\n        multlineWidth: '',\n        ams: {\n            multlineWidth: '100%',\n            multlineIndent: '1em',\n        }\n    }\n});\n//# sourceMappingURL=AmsConfiguration.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.FlalignItem = exports.MultlineItem = void 0;\nvar BaseItems_js_1 = require(\"../base/BaseItems.js\");\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar TexConstants_js_1 = require(\"../TexConstants.js\");\nvar MultlineItem = (function (_super) {\n    __extends(MultlineItem, _super);\n    function MultlineItem(factory) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        var _this = _super.call(this, factory) || this;\n        _this.factory.configuration.tags.start('multline', true, args[0]);\n        return _this;\n    }\n    Object.defineProperty(MultlineItem.prototype, \"kind\", {\n        get: function () {\n            return 'multline';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MultlineItem.prototype.EndEntry = function () {\n        if (this.table.length) {\n            ParseUtil_js_1.default.fixInitialMO(this.factory.configuration, this.nodes);\n        }\n        var shove = this.getProperty('shove');\n        var mtd = this.create('node', 'mtd', this.nodes, shove ? { columnalign: shove } : {});\n        this.setProperty('shove', null);\n        this.row.push(mtd);\n        this.Clear();\n    };\n    MultlineItem.prototype.EndRow = function () {\n        if (this.row.length !== 1) {\n            throw new TexError_js_1.default('MultlineRowsOneCol', 'The rows within the %1 environment must have exactly one column', 'multline');\n        }\n        var row = this.create('node', 'mtr', this.row);\n        this.table.push(row);\n        this.row = [];\n    };\n    MultlineItem.prototype.EndTable = function () {\n        _super.prototype.EndTable.call(this);\n        if (this.table.length) {\n            var m = this.table.length - 1, label = -1;\n            if (!NodeUtil_js_1.default.getAttribute(NodeUtil_js_1.default.getChildren(this.table[0])[0], 'columnalign')) {\n                NodeUtil_js_1.default.setAttribute(NodeUtil_js_1.default.getChildren(this.table[0])[0], 'columnalign', TexConstants_js_1.TexConstant.Align.LEFT);\n            }\n            if (!NodeUtil_js_1.default.getAttribute(NodeUtil_js_1.default.getChildren(this.table[m])[0], 'columnalign')) {\n                NodeUtil_js_1.default.setAttribute(NodeUtil_js_1.default.getChildren(this.table[m])[0], 'columnalign', TexConstants_js_1.TexConstant.Align.RIGHT);\n            }\n            var tag = this.factory.configuration.tags.getTag();\n            if (tag) {\n                label = (this.arraydef.side === TexConstants_js_1.TexConstant.Align.LEFT ? 0 : this.table.length - 1);\n                var mtr = this.table[label];\n                var mlabel = this.create('node', 'mlabeledtr', [tag].concat(NodeUtil_js_1.default.getChildren(mtr)));\n                NodeUtil_js_1.default.copyAttributes(mtr, mlabel);\n                this.table[label] = mlabel;\n            }\n        }\n        this.factory.configuration.tags.end();\n    };\n    return MultlineItem;\n}(BaseItems_js_1.ArrayItem));\nexports.MultlineItem = MultlineItem;\nvar FlalignItem = (function (_super) {\n    __extends(FlalignItem, _super);\n    function FlalignItem(factory, name, numbered, padded, center) {\n        var _this = _super.call(this, factory) || this;\n        _this.name = name;\n        _this.numbered = numbered;\n        _this.padded = padded;\n        _this.center = center;\n        _this.factory.configuration.tags.start(name, numbered, numbered);\n        return _this;\n    }\n    Object.defineProperty(FlalignItem.prototype, \"kind\", {\n        get: function () {\n            return 'flalign';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    FlalignItem.prototype.EndEntry = function () {\n        _super.prototype.EndEntry.call(this);\n        var n = this.getProperty('xalignat');\n        if (!n)\n            return;\n        if (this.row.length > n) {\n            throw new TexError_js_1.default('XalignOverflow', 'Extra %1 in row of %2', '&', this.name);\n        }\n    };\n    FlalignItem.prototype.EndRow = function () {\n        var cell;\n        var row = this.row;\n        var n = this.getProperty('xalignat');\n        while (row.length < n) {\n            row.push(this.create('node', 'mtd'));\n        }\n        this.row = [];\n        if (this.padded) {\n            this.row.push(this.create('node', 'mtd'));\n        }\n        while ((cell = row.shift())) {\n            this.row.push(cell);\n            cell = row.shift();\n            if (cell)\n                this.row.push(cell);\n            if (row.length || this.padded) {\n                this.row.push(this.create('node', 'mtd'));\n            }\n        }\n        if (this.row.length > this.maxrow) {\n            this.maxrow = this.row.length;\n        }\n        _super.prototype.EndRow.call(this);\n        var mtr = this.table[this.table.length - 1];\n        if (this.getProperty('zeroWidthLabel') && mtr.isKind('mlabeledtr')) {\n            var mtd = NodeUtil_js_1.default.getChildren(mtr)[0];\n            var side = this.factory.configuration.options['tagSide'];\n            var def = __assign({ width: 0 }, (side === 'right' ? { lspace: '-1width' } : {}));\n            var mpadded = this.create('node', 'mpadded', NodeUtil_js_1.default.getChildren(mtd), def);\n            mtd.setChildren([mpadded]);\n        }\n    };\n    FlalignItem.prototype.EndTable = function () {\n        _super.prototype.EndTable.call(this);\n        if (this.center) {\n            if (this.maxrow <= 2) {\n                var def = this.arraydef;\n                delete def.width;\n                delete this.global.indentalign;\n            }\n        }\n    };\n    return FlalignItem;\n}(BaseItems_js_1.EqnArrayItem));\nexports.FlalignItem = FlalignItem;\n//# sourceMappingURL=AmsItems.js.map", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar AmsMethods_js_1 = require(\"./AmsMethods.js\");\nvar sm = __importStar(require(\"../SymbolMap.js\"));\nvar TexConstants_js_1 = require(\"../TexConstants.js\");\nvar ParseMethods_js_1 = __importDefault(require(\"../ParseMethods.js\"));\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nvar lengths_js_1 = require(\"../../../util/lengths.js\");\nnew sm.CharacterMap('AMSmath-mathchar0mo', ParseMethods_js_1.default.mathchar0mo, {\n    iiiint: ['\\u2A0C', { texClass: MmlNode_js_1.TEXCLASS.OP }]\n});\nnew sm.RegExpMap('AMSmath-operatorLetter', AmsMethods_js_1.AmsMethods.operatorLetter, /[-*]/i);\nnew sm.CommandMap('AMSmath-macros', {\n    mathring: ['Accent', '02DA'],\n    nobreakspace: 'Tilde',\n    negmedspace: ['Spacer', lengths_js_1.MATHSPACE.negativemediummathspace],\n    negthickspace: ['Spacer', lengths_js_1.MATHSPACE.negativethickmathspace],\n    idotsint: ['MultiIntegral', '\\\\int\\\\cdots\\\\int'],\n    dddot: ['Accent', '20DB'],\n    ddddot: ['Accent', '20DC'],\n    sideset: 'SideSet',\n    boxed: ['Macro', '\\\\fbox{$\\\\displaystyle{#1}$}', 1],\n    tag: 'HandleTag',\n    notag: 'HandleNoTag',\n    eqref: ['HandleRef', true],\n    substack: ['Macro', '\\\\begin{subarray}{c}#1\\\\end{subarray}', 1],\n    injlim: ['NamedOp', 'inj&thinsp;lim'],\n    projlim: ['NamedOp', 'proj&thinsp;lim'],\n    varliminf: ['Macro', '\\\\mathop{\\\\underline{\\\\mmlToken{mi}{lim}}}'],\n    varlimsup: ['Macro', '\\\\mathop{\\\\overline{\\\\mmlToken{mi}{lim}}}'],\n    varinjlim: ['Macro', '\\\\mathop{\\\\underrightarrow{\\\\mmlToken{mi}{lim}}}'],\n    varprojlim: ['Macro', '\\\\mathop{\\\\underleftarrow{\\\\mmlToken{mi}{lim}}}'],\n    DeclareMathOperator: 'HandleDeclareOp',\n    operatorname: 'HandleOperatorName',\n    genfrac: 'Genfrac',\n    frac: ['Genfrac', '', '', '', ''],\n    tfrac: ['Genfrac', '', '', '', '1'],\n    dfrac: ['Genfrac', '', '', '', '0'],\n    binom: ['Genfrac', '(', ')', '0', ''],\n    tbinom: ['Genfrac', '(', ')', '0', '1'],\n    dbinom: ['Genfrac', '(', ')', '0', '0'],\n    cfrac: 'CFrac',\n    shoveleft: ['HandleShove', TexConstants_js_1.TexConstant.Align.LEFT],\n    shoveright: ['HandleShove', TexConstants_js_1.TexConstant.Align.RIGHT],\n    xrightarrow: ['xArrow', 0x2192, 5, 10],\n    xleftarrow: ['xArrow', 0x2190, 10, 5]\n}, AmsMethods_js_1.AmsMethods);\nnew sm.EnvironmentMap('AMSmath-environment', ParseMethods_js_1.default.environment, {\n    'equation*': ['Equation', null, false],\n    'eqnarray*': ['EqnArray', null, false, true, 'rcl',\n        ParseUtil_js_1.default.cols(0, lengths_js_1.MATHSPACE.thickmathspace), '.5em'],\n    align: ['EqnArray', null, true, true, 'rl', ParseUtil_js_1.default.cols(0, 2)],\n    'align*': ['EqnArray', null, false, true, 'rl', ParseUtil_js_1.default.cols(0, 2)],\n    multline: ['Multline', null, true],\n    'multline*': ['Multline', null, false],\n    split: ['EqnArray', null, false, false, 'rl', ParseUtil_js_1.default.cols(0)],\n    gather: ['EqnArray', null, true, true, 'c'],\n    'gather*': ['EqnArray', null, false, true, 'c'],\n    alignat: ['AlignAt', null, true, true],\n    'alignat*': ['AlignAt', null, false, true],\n    alignedat: ['AlignAt', null, false, false],\n    aligned: ['AmsEqnArray', null, null, null, 'rl', ParseUtil_js_1.default.cols(0, 2), '.5em', 'D'],\n    gathered: ['AmsEqnArray', null, null, null, 'c', null, '.5em', 'D'],\n    xalignat: ['XalignAt', null, true, true],\n    'xalignat*': ['XalignAt', null, false, true],\n    xxalignat: ['XalignAt', null, false, false],\n    flalign: ['FlalignArray', null, true, false, true, 'rlc', 'auto auto fit'],\n    'flalign*': ['FlalignArray', null, false, false, true, 'rlc', 'auto auto fit'],\n    subarray: ['Array', null, null, null, null, ParseUtil_js_1.default.cols(0), '0.1em', 'S', 1],\n    smallmatrix: ['Array', null, null, null, 'c', ParseUtil_js_1.default.cols(1 / 3),\n        '.2em', 'S', 1],\n    matrix: ['Array', null, null, null, 'c'],\n    pmatrix: ['Array', null, '(', ')', 'c'],\n    bmatrix: ['Array', null, '[', ']', 'c'],\n    Bmatrix: ['Array', null, '\\\\{', '\\\\}', 'c'],\n    vmatrix: ['Array', null, '\\\\vert', '\\\\vert', 'c'],\n    Vmatrix: ['Array', null, '\\\\Vert', '\\\\Vert', 'c'],\n    cases: ['Array', null, '\\\\{', '.', 'll', null, '.2em', 'T']\n}, AmsMethods_js_1.AmsMethods);\nnew sm.DelimiterMap('AMSmath-delimiter', ParseMethods_js_1.default.delimiter, {\n    '\\\\lvert': ['\\u007C', { texClass: MmlNode_js_1.TEXCLASS.OPEN }],\n    '\\\\rvert': ['\\u007C', { texClass: MmlNode_js_1.TEXCLASS.CLOSE }],\n    '\\\\lVert': ['\\u2016', { texClass: MmlNode_js_1.TEXCLASS.OPEN }],\n    '\\\\rVert': ['\\u2016', { texClass: MmlNode_js_1.TEXCLASS.CLOSE }]\n});\nnew sm.CharacterMap('AMSsymbols-mathchar0mi', ParseMethods_js_1.default.mathchar0mi, {\n    digamma: '\\u03DD',\n    varkappa: '\\u03F0',\n    varGamma: ['\\u0393', { mathvariant: TexConstants_js_1.TexConstant.Variant.ITALIC }],\n    varDelta: ['\\u0394', { mathvariant: TexConstants_js_1.TexConstant.Variant.ITALIC }],\n    varTheta: ['\\u0398', { mathvariant: TexConstants_js_1.TexConstant.Variant.ITALIC }],\n    varLambda: ['\\u039B', { mathvariant: TexConstants_js_1.TexConstant.Variant.ITALIC }],\n    varXi: ['\\u039E', { mathvariant: TexConstants_js_1.TexConstant.Variant.ITALIC }],\n    varPi: ['\\u03A0', { mathvariant: TexConstants_js_1.TexConstant.Variant.ITALIC }],\n    varSigma: ['\\u03A3', { mathvariant: TexConstants_js_1.TexConstant.Variant.ITALIC }],\n    varUpsilon: ['\\u03A5', { mathvariant: TexConstants_js_1.TexConstant.Variant.ITALIC }],\n    varPhi: ['\\u03A6', { mathvariant: TexConstants_js_1.TexConstant.Variant.ITALIC }],\n    varPsi: ['\\u03A8', { mathvariant: TexConstants_js_1.TexConstant.Variant.ITALIC }],\n    varOmega: ['\\u03A9', { mathvariant: TexConstants_js_1.TexConstant.Variant.ITALIC }],\n    beth: '\\u2136',\n    gimel: '\\u2137',\n    daleth: '\\u2138',\n    backprime: ['\\u2035', { variantForm: true }],\n    hslash: '\\u210F',\n    varnothing: ['\\u2205', { variantForm: true }],\n    blacktriangle: '\\u25B4',\n    triangledown: ['\\u25BD', { variantForm: true }],\n    blacktriangledown: '\\u25BE',\n    square: '\\u25FB',\n    Box: '\\u25FB',\n    blacksquare: '\\u25FC',\n    lozenge: '\\u25CA',\n    Diamond: '\\u25CA',\n    blacklozenge: '\\u29EB',\n    circledS: ['\\u24C8', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    bigstar: '\\u2605',\n    sphericalangle: '\\u2222',\n    measuredangle: '\\u2221',\n    nexists: '\\u2204',\n    complement: '\\u2201',\n    mho: '\\u2127',\n    eth: ['\\u00F0', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    Finv: '\\u2132',\n    diagup: '\\u2571',\n    Game: '\\u2141',\n    diagdown: '\\u2572',\n    Bbbk: ['\\u006B',\n        { mathvariant: TexConstants_js_1.TexConstant.Variant.DOUBLESTRUCK }],\n    yen: '\\u00A5',\n    circledR: '\\u00AE',\n    checkmark: '\\u2713',\n    maltese: '\\u2720'\n});\nnew sm.CharacterMap('AMSsymbols-mathchar0mo', ParseMethods_js_1.default.mathchar0mo, {\n    dotplus: '\\u2214',\n    ltimes: '\\u22C9',\n    smallsetminus: ['\\u2216', { variantForm: true }],\n    rtimes: '\\u22CA',\n    Cap: '\\u22D2',\n    doublecap: '\\u22D2',\n    leftthreetimes: '\\u22CB',\n    Cup: '\\u22D3',\n    doublecup: '\\u22D3',\n    rightthreetimes: '\\u22CC',\n    barwedge: '\\u22BC',\n    curlywedge: '\\u22CF',\n    veebar: '\\u22BB',\n    curlyvee: '\\u22CE',\n    doublebarwedge: '\\u2A5E',\n    boxminus: '\\u229F',\n    circleddash: '\\u229D',\n    boxtimes: '\\u22A0',\n    circledast: '\\u229B',\n    boxdot: '\\u22A1',\n    circledcirc: '\\u229A',\n    boxplus: '\\u229E',\n    centerdot: ['\\u22C5', { variantForm: true }],\n    divideontimes: '\\u22C7',\n    intercal: '\\u22BA',\n    leqq: '\\u2266',\n    geqq: '\\u2267',\n    leqslant: '\\u2A7D',\n    geqslant: '\\u2A7E',\n    eqslantless: '\\u2A95',\n    eqslantgtr: '\\u2A96',\n    lesssim: '\\u2272',\n    gtrsim: '\\u2273',\n    lessapprox: '\\u2A85',\n    gtrapprox: '\\u2A86',\n    approxeq: '\\u224A',\n    lessdot: '\\u22D6',\n    gtrdot: '\\u22D7',\n    lll: '\\u22D8',\n    llless: '\\u22D8',\n    ggg: '\\u22D9',\n    gggtr: '\\u22D9',\n    lessgtr: '\\u2276',\n    gtrless: '\\u2277',\n    lesseqgtr: '\\u22DA',\n    gtreqless: '\\u22DB',\n    lesseqqgtr: '\\u2A8B',\n    gtreqqless: '\\u2A8C',\n    doteqdot: '\\u2251',\n    Doteq: '\\u2251',\n    eqcirc: '\\u2256',\n    risingdotseq: '\\u2253',\n    circeq: '\\u2257',\n    fallingdotseq: '\\u2252',\n    triangleq: '\\u225C',\n    backsim: '\\u223D',\n    thicksim: ['\\u223C', { variantForm: true }],\n    backsimeq: '\\u22CD',\n    thickapprox: ['\\u2248', { variantForm: true }],\n    subseteqq: '\\u2AC5',\n    supseteqq: '\\u2AC6',\n    Subset: '\\u22D0',\n    Supset: '\\u22D1',\n    sqsubset: '\\u228F',\n    sqsupset: '\\u2290',\n    preccurlyeq: '\\u227C',\n    succcurlyeq: '\\u227D',\n    curlyeqprec: '\\u22DE',\n    curlyeqsucc: '\\u22DF',\n    precsim: '\\u227E',\n    succsim: '\\u227F',\n    precapprox: '\\u2AB7',\n    succapprox: '\\u2AB8',\n    vartriangleleft: '\\u22B2',\n    lhd: '\\u22B2',\n    vartriangleright: '\\u22B3',\n    rhd: '\\u22B3',\n    trianglelefteq: '\\u22B4',\n    unlhd: '\\u22B4',\n    trianglerighteq: '\\u22B5',\n    unrhd: '\\u22B5',\n    vDash: ['\\u22A8', { variantForm: true }],\n    Vdash: '\\u22A9',\n    Vvdash: '\\u22AA',\n    smallsmile: ['\\u2323', { variantForm: true }],\n    shortmid: ['\\u2223', { variantForm: true }],\n    smallfrown: ['\\u2322', { variantForm: true }],\n    shortparallel: ['\\u2225', { variantForm: true }],\n    bumpeq: '\\u224F',\n    between: '\\u226C',\n    Bumpeq: '\\u224E',\n    pitchfork: '\\u22D4',\n    varpropto: ['\\u221D', { variantForm: true }],\n    backepsilon: '\\u220D',\n    blacktriangleleft: '\\u25C2',\n    blacktriangleright: '\\u25B8',\n    therefore: '\\u2234',\n    because: '\\u2235',\n    eqsim: '\\u2242',\n    vartriangle: ['\\u25B3', { variantForm: true }],\n    Join: '\\u22C8',\n    nless: '\\u226E',\n    ngtr: '\\u226F',\n    nleq: '\\u2270',\n    ngeq: '\\u2271',\n    nleqslant: ['\\u2A87', { variantForm: true }],\n    ngeqslant: ['\\u2A88', { variantForm: true }],\n    nleqq: ['\\u2270', { variantForm: true }],\n    ngeqq: ['\\u2271', { variantForm: true }],\n    lneq: '\\u2A87',\n    gneq: '\\u2A88',\n    lneqq: '\\u2268',\n    gneqq: '\\u2269',\n    lvertneqq: ['\\u2268', { variantForm: true }],\n    gvertneqq: ['\\u2269', { variantForm: true }],\n    lnsim: '\\u22E6',\n    gnsim: '\\u22E7',\n    lnapprox: '\\u2A89',\n    gnapprox: '\\u2A8A',\n    nprec: '\\u2280',\n    nsucc: '\\u2281',\n    npreceq: ['\\u22E0', { variantForm: true }],\n    nsucceq: ['\\u22E1', { variantForm: true }],\n    precneqq: '\\u2AB5',\n    succneqq: '\\u2AB6',\n    precnsim: '\\u22E8',\n    succnsim: '\\u22E9',\n    precnapprox: '\\u2AB9',\n    succnapprox: '\\u2ABA',\n    nsim: '\\u2241',\n    ncong: '\\u2247',\n    nshortmid: ['\\u2224', { variantForm: true }],\n    nshortparallel: ['\\u2226', { variantForm: true }],\n    nmid: '\\u2224',\n    nparallel: '\\u2226',\n    nvdash: '\\u22AC',\n    nvDash: '\\u22AD',\n    nVdash: '\\u22AE',\n    nVDash: '\\u22AF',\n    ntriangleleft: '\\u22EA',\n    ntriangleright: '\\u22EB',\n    ntrianglelefteq: '\\u22EC',\n    ntrianglerighteq: '\\u22ED',\n    nsubseteq: '\\u2288',\n    nsupseteq: '\\u2289',\n    nsubseteqq: ['\\u2288', { variantForm: true }],\n    nsupseteqq: ['\\u2289', { variantForm: true }],\n    subsetneq: '\\u228A',\n    supsetneq: '\\u228B',\n    varsubsetneq: ['\\u228A', { variantForm: true }],\n    varsupsetneq: ['\\u228B', { variantForm: true }],\n    subsetneqq: '\\u2ACB',\n    supsetneqq: '\\u2ACC',\n    varsubsetneqq: ['\\u2ACB', { variantForm: true }],\n    varsupsetneqq: ['\\u2ACC', { variantForm: true }],\n    leftleftarrows: '\\u21C7',\n    rightrightarrows: '\\u21C9',\n    leftrightarrows: '\\u21C6',\n    rightleftarrows: '\\u21C4',\n    Lleftarrow: '\\u21DA',\n    Rrightarrow: '\\u21DB',\n    twoheadleftarrow: '\\u219E',\n    twoheadrightarrow: '\\u21A0',\n    leftarrowtail: '\\u21A2',\n    rightarrowtail: '\\u21A3',\n    looparrowleft: '\\u21AB',\n    looparrowright: '\\u21AC',\n    leftrightharpoons: '\\u21CB',\n    rightleftharpoons: ['\\u21CC', { variantForm: true }],\n    curvearrowleft: '\\u21B6',\n    curvearrowright: '\\u21B7',\n    circlearrowleft: '\\u21BA',\n    circlearrowright: '\\u21BB',\n    Lsh: '\\u21B0',\n    Rsh: '\\u21B1',\n    upuparrows: '\\u21C8',\n    downdownarrows: '\\u21CA',\n    upharpoonleft: '\\u21BF',\n    upharpoonright: '\\u21BE',\n    downharpoonleft: '\\u21C3',\n    restriction: '\\u21BE',\n    multimap: '\\u22B8',\n    downharpoonright: '\\u21C2',\n    leftrightsquigarrow: '\\u21AD',\n    rightsquigarrow: '\\u21DD',\n    leadsto: '\\u21DD',\n    dashrightarrow: '\\u21E2',\n    dashleftarrow: '\\u21E0',\n    nleftarrow: '\\u219A',\n    nrightarrow: '\\u219B',\n    nLeftarrow: '\\u21CD',\n    nRightarrow: '\\u21CF',\n    nleftrightarrow: '\\u21AE',\n    nLeftrightarrow: '\\u21CE'\n});\nnew sm.DelimiterMap('AMSsymbols-delimiter', ParseMethods_js_1.default.delimiter, {\n    '\\\\ulcorner': '\\u231C',\n    '\\\\urcorner': '\\u231D',\n    '\\\\llcorner': '\\u231E',\n    '\\\\lrcorner': '\\u231F'\n});\nnew sm.CommandMap('AMSsymbols-macros', {\n    implies: ['Macro', '\\\\;\\\\Longrightarrow\\\\;'],\n    impliedby: ['Macro', '\\\\;\\\\Longleftarrow\\\\;']\n}, AmsMethods_js_1.AmsMethods);\n//# sourceMappingURL=AmsMappings.js.map", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.NEW_OPS = exports.AmsMethods = void 0;\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar ParseMethods_js_1 = __importDefault(require(\"../ParseMethods.js\"));\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar TexConstants_js_1 = require(\"../TexConstants.js\");\nvar TexParser_js_1 = __importDefault(require(\"../TexParser.js\"));\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar Symbol_js_1 = require(\"../Symbol.js\");\nvar BaseMethods_js_1 = __importDefault(require(\"../base/BaseMethods.js\"));\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nexports.AmsMethods = {};\nexports.AmsMethods.AmsEqnArray = function (parser, begin, numbered, taggable, align, spacing, style) {\n    var args = parser.GetBrackets('\\\\begin{' + begin.getName() + '}');\n    var array = BaseMethods_js_1.default.EqnArray(parser, begin, numbered, taggable, align, spacing, style);\n    return ParseUtil_js_1.default.setArrayAlign(array, args);\n};\nexports.AmsMethods.AlignAt = function (parser, begin, numbered, taggable) {\n    var name = begin.getName();\n    var n, valign, align = '', spacing = [];\n    if (!taggable) {\n        valign = parser.GetBrackets('\\\\begin{' + name + '}');\n    }\n    n = parser.GetArgument('\\\\begin{' + name + '}');\n    if (n.match(/[^0-9]/)) {\n        throw new TexError_js_1.default('PositiveIntegerArg', 'Argument to %1 must me a positive integer', '\\\\begin{' + name + '}');\n    }\n    var count = parseInt(n, 10);\n    while (count > 0) {\n        align += 'rl';\n        spacing.push('0em 0em');\n        count--;\n    }\n    var spaceStr = spacing.join(' ');\n    if (taggable) {\n        return exports.AmsMethods.EqnArray(parser, begin, numbered, taggable, align, spaceStr);\n    }\n    var array = exports.AmsMethods.EqnArray(parser, begin, numbered, taggable, align, spaceStr);\n    return ParseUtil_js_1.default.setArrayAlign(array, valign);\n};\nexports.AmsMethods.Multline = function (parser, begin, numbered) {\n    parser.Push(begin);\n    ParseUtil_js_1.default.checkEqnEnv(parser);\n    var item = parser.itemFactory.create('multline', numbered, parser.stack);\n    item.arraydef = {\n        displaystyle: true,\n        rowspacing: '.5em',\n        columnspacing: '100%',\n        width: parser.options.ams['multlineWidth'],\n        side: parser.options['tagSide'],\n        minlabelspacing: parser.options['tagIndent'],\n        framespacing: parser.options.ams['multlineIndent'] + ' 0',\n        frame: '',\n        'data-width-includes-label': true\n    };\n    return item;\n};\nexports.AmsMethods.XalignAt = function (parser, begin, numbered, padded) {\n    var n = parser.GetArgument('\\\\begin{' + begin.getName() + '}');\n    if (n.match(/[^0-9]/)) {\n        throw new TexError_js_1.default('PositiveIntegerArg', 'Argument to %1 must me a positive integer', '\\\\begin{' + begin.getName() + '}');\n    }\n    var align = (padded ? 'crl' : 'rlc');\n    var width = (padded ? 'fit auto auto' : 'auto auto fit');\n    var item = exports.AmsMethods.FlalignArray(parser, begin, numbered, padded, false, align, width, true);\n    item.setProperty('xalignat', 2 * parseInt(n));\n    return item;\n};\nexports.AmsMethods.FlalignArray = function (parser, begin, numbered, padded, center, align, width, zeroWidthLabel) {\n    if (zeroWidthLabel === void 0) { zeroWidthLabel = false; }\n    parser.Push(begin);\n    ParseUtil_js_1.default.checkEqnEnv(parser);\n    align = align\n        .split('')\n        .join(' ')\n        .replace(/r/g, 'right')\n        .replace(/l/g, 'left')\n        .replace(/c/g, 'center');\n    var item = parser.itemFactory.create('flalign', begin.getName(), numbered, padded, center, parser.stack);\n    item.arraydef = {\n        width: '100%',\n        displaystyle: true,\n        columnalign: align,\n        columnspacing: '0em',\n        columnwidth: width,\n        rowspacing: '3pt',\n        side: parser.options['tagSide'],\n        minlabelspacing: (zeroWidthLabel ? '0' : parser.options['tagIndent']),\n        'data-width-includes-label': true,\n    };\n    item.setProperty('zeroWidthLabel', zeroWidthLabel);\n    return item;\n};\nexports.NEW_OPS = 'ams-declare-ops';\nexports.AmsMethods.HandleDeclareOp = function (parser, name) {\n    var star = (parser.GetStar() ? '*' : '');\n    var cs = ParseUtil_js_1.default.trimSpaces(parser.GetArgument(name));\n    if (cs.charAt(0) === '\\\\') {\n        cs = cs.substr(1);\n    }\n    var op = parser.GetArgument(name);\n    parser.configuration.handlers.retrieve(exports.NEW_OPS).\n        add(cs, new Symbol_js_1.Macro(cs, exports.AmsMethods.Macro, [\"\\\\operatorname\".concat(star, \"{\").concat(op, \"}\")]));\n};\nexports.AmsMethods.HandleOperatorName = function (parser, name) {\n    var star = parser.GetStar();\n    var op = ParseUtil_js_1.default.trimSpaces(parser.GetArgument(name));\n    var mml = new TexParser_js_1.default(op, __assign(__assign({}, parser.stack.env), { font: TexConstants_js_1.TexConstant.Variant.NORMAL, multiLetterIdentifiers: /^[-*a-z]+/i, operatorLetters: true }), parser.configuration).mml();\n    if (!mml.isKind('mi')) {\n        mml = parser.create('node', 'TeXAtom', [mml]);\n    }\n    NodeUtil_js_1.default.setProperties(mml, { movesupsub: star, movablelimits: true, texClass: MmlNode_js_1.TEXCLASS.OP });\n    if (!star) {\n        var c = parser.GetNext(), i = parser.i;\n        if (c === '\\\\' && ++parser.i && parser.GetCS() !== 'limits') {\n            parser.i = i;\n        }\n    }\n    parser.Push(mml);\n};\nexports.AmsMethods.SideSet = function (parser, name) {\n    var _a = __read(splitSideSet(parser.ParseArg(name)), 2), preScripts = _a[0], preRest = _a[1];\n    var _b = __read(splitSideSet(parser.ParseArg(name)), 2), postScripts = _b[0], postRest = _b[1];\n    var base = parser.ParseArg(name);\n    var mml = base;\n    if (preScripts) {\n        if (preRest) {\n            preScripts.replaceChild(parser.create('node', 'mphantom', [\n                parser.create('node', 'mpadded', [ParseUtil_js_1.default.copyNode(base, parser)], { width: 0 })\n            ]), NodeUtil_js_1.default.getChildAt(preScripts, 0));\n        }\n        else {\n            mml = parser.create('node', 'mmultiscripts', [base]);\n            if (postScripts) {\n                NodeUtil_js_1.default.appendChildren(mml, [\n                    NodeUtil_js_1.default.getChildAt(postScripts, 1) || parser.create('node', 'none'),\n                    NodeUtil_js_1.default.getChildAt(postScripts, 2) || parser.create('node', 'none')\n                ]);\n            }\n            NodeUtil_js_1.default.setProperty(mml, 'scriptalign', 'left');\n            NodeUtil_js_1.default.appendChildren(mml, [\n                parser.create('node', 'mprescripts'),\n                NodeUtil_js_1.default.getChildAt(preScripts, 1) || parser.create('node', 'none'),\n                NodeUtil_js_1.default.getChildAt(preScripts, 2) || parser.create('node', 'none')\n            ]);\n        }\n    }\n    if (postScripts && mml === base) {\n        postScripts.replaceChild(base, NodeUtil_js_1.default.getChildAt(postScripts, 0));\n        mml = postScripts;\n    }\n    var mrow = parser.create('node', 'TeXAtom', [], { texClass: MmlNode_js_1.TEXCLASS.OP, movesupsub: true, movablelimits: true });\n    if (preRest) {\n        preScripts && mrow.appendChild(preScripts);\n        mrow.appendChild(preRest);\n    }\n    mrow.appendChild(mml);\n    postRest && mrow.appendChild(postRest);\n    parser.Push(mrow);\n};\nfunction splitSideSet(mml) {\n    if (!mml || (mml.isInferred && mml.childNodes.length === 0))\n        return [null, null];\n    if (mml.isKind('msubsup') && checkSideSetBase(mml))\n        return [mml, null];\n    var child = NodeUtil_js_1.default.getChildAt(mml, 0);\n    if (!(mml.isInferred && child && checkSideSetBase(child)))\n        return [null, mml];\n    mml.childNodes.splice(0, 1);\n    return [child, mml];\n}\nfunction checkSideSetBase(mml) {\n    var base = mml.childNodes[0];\n    return base && base.isKind('mi') && base.getText() === '';\n}\nexports.AmsMethods.operatorLetter = function (parser, c) {\n    return parser.stack.env.operatorLetters ? ParseMethods_js_1.default.variable(parser, c) : false;\n};\nexports.AmsMethods.MultiIntegral = function (parser, name, integral) {\n    var next = parser.GetNext();\n    if (next === '\\\\') {\n        var i = parser.i;\n        next = parser.GetArgument(name);\n        parser.i = i;\n        if (next === '\\\\limits') {\n            if (name === '\\\\idotsint') {\n                integral = '\\\\!\\\\!\\\\mathop{\\\\,\\\\,' + integral + '}';\n            }\n            else {\n                integral = '\\\\!\\\\!\\\\!\\\\mathop{\\\\,\\\\,\\\\,' + integral + '}';\n            }\n        }\n    }\n    parser.string = integral + ' ' + parser.string.slice(parser.i);\n    parser.i = 0;\n};\nexports.AmsMethods.xArrow = function (parser, name, chr, l, r) {\n    var def = { width: '+' + ParseUtil_js_1.default.Em((l + r) / 18), lspace: ParseUtil_js_1.default.Em(l / 18) };\n    var bot = parser.GetBrackets(name);\n    var first = parser.ParseArg(name);\n    var dstrut = parser.create('node', 'mspace', [], { depth: '.25em' });\n    var arrow = parser.create('token', 'mo', { stretchy: true, texClass: MmlNode_js_1.TEXCLASS.REL }, String.fromCodePoint(chr));\n    arrow = parser.create('node', 'mstyle', [arrow], { scriptlevel: 0 });\n    var mml = parser.create('node', 'munderover', [arrow]);\n    var mpadded = parser.create('node', 'mpadded', [first, dstrut], def);\n    NodeUtil_js_1.default.setAttribute(mpadded, 'voffset', '-.2em');\n    NodeUtil_js_1.default.setAttribute(mpadded, 'height', '-.2em');\n    NodeUtil_js_1.default.setChild(mml, mml.over, mpadded);\n    if (bot) {\n        var bottom = new TexParser_js_1.default(bot, parser.stack.env, parser.configuration).mml();\n        var bstrut = parser.create('node', 'mspace', [], { height: '.75em' });\n        mpadded = parser.create('node', 'mpadded', [bottom, bstrut], def);\n        NodeUtil_js_1.default.setAttribute(mpadded, 'voffset', '.15em');\n        NodeUtil_js_1.default.setAttribute(mpadded, 'depth', '-.15em');\n        NodeUtil_js_1.default.setChild(mml, mml.under, mpadded);\n    }\n    NodeUtil_js_1.default.setProperty(mml, 'subsupOK', true);\n    parser.Push(mml);\n};\nexports.AmsMethods.HandleShove = function (parser, _name, shove) {\n    var top = parser.stack.Top();\n    if (top.kind !== 'multline') {\n        throw new TexError_js_1.default('CommandOnlyAllowedInEnv', '%1 only allowed in %2 environment', parser.currentCS, 'multline');\n    }\n    if (top.Size()) {\n        throw new TexError_js_1.default('CommandAtTheBeginingOfLine', '%1 must come at the beginning of the line', parser.currentCS);\n    }\n    top.setProperty('shove', shove);\n};\nexports.AmsMethods.CFrac = function (parser, name) {\n    var lr = ParseUtil_js_1.default.trimSpaces(parser.GetBrackets(name, ''));\n    var num = parser.GetArgument(name);\n    var den = parser.GetArgument(name);\n    var lrMap = {\n        l: TexConstants_js_1.TexConstant.Align.LEFT, r: TexConstants_js_1.TexConstant.Align.RIGHT, '': ''\n    };\n    var numNode = new TexParser_js_1.default('\\\\strut\\\\textstyle{' + num + '}', parser.stack.env, parser.configuration).mml();\n    var denNode = new TexParser_js_1.default('\\\\strut\\\\textstyle{' + den + '}', parser.stack.env, parser.configuration).mml();\n    var frac = parser.create('node', 'mfrac', [numNode, denNode]);\n    lr = lrMap[lr];\n    if (lr == null) {\n        throw new TexError_js_1.default('IllegalAlign', 'Illegal alignment specified in %1', parser.currentCS);\n    }\n    if (lr) {\n        NodeUtil_js_1.default.setProperties(frac, { numalign: lr, denomalign: lr });\n    }\n    parser.Push(frac);\n};\nexports.AmsMethods.Genfrac = function (parser, name, left, right, thick, style) {\n    if (left == null) {\n        left = parser.GetDelimiterArg(name);\n    }\n    if (right == null) {\n        right = parser.GetDelimiterArg(name);\n    }\n    if (thick == null) {\n        thick = parser.GetArgument(name);\n    }\n    if (style == null) {\n        style = ParseUtil_js_1.default.trimSpaces(parser.GetArgument(name));\n    }\n    var num = parser.ParseArg(name);\n    var den = parser.ParseArg(name);\n    var frac = parser.create('node', 'mfrac', [num, den]);\n    if (thick !== '') {\n        NodeUtil_js_1.default.setAttribute(frac, 'linethickness', thick);\n    }\n    if (left || right) {\n        NodeUtil_js_1.default.setProperty(frac, 'withDelims', true);\n        frac = ParseUtil_js_1.default.fixedFence(parser.configuration, left, frac, right);\n    }\n    if (style !== '') {\n        var styleDigit = parseInt(style, 10);\n        var styleAlpha = ['D', 'T', 'S', 'SS'][styleDigit];\n        if (styleAlpha == null) {\n            throw new TexError_js_1.default('BadMathStyleFor', 'Bad math style for %1', parser.currentCS);\n        }\n        frac = parser.create('node', 'mstyle', [frac]);\n        if (styleAlpha === 'D') {\n            NodeUtil_js_1.default.setProperties(frac, { displaystyle: true, scriptlevel: 0 });\n        }\n        else {\n            NodeUtil_js_1.default.setProperties(frac, { displaystyle: false,\n                scriptlevel: styleDigit - 1 });\n        }\n    }\n    parser.Push(frac);\n};\nexports.AmsMethods.HandleTag = function (parser, name) {\n    if (!parser.tags.currentTag.taggable && parser.tags.env) {\n        throw new TexError_js_1.default('CommandNotAllowedInEnv', '%1 not allowed in %2 environment', parser.currentCS, parser.tags.env);\n    }\n    if (parser.tags.currentTag.tag) {\n        throw new TexError_js_1.default('MultipleCommand', 'Multiple %1', parser.currentCS);\n    }\n    var star = parser.GetStar();\n    var tagId = ParseUtil_js_1.default.trimSpaces(parser.GetArgument(name));\n    parser.tags.tag(tagId, star);\n};\nexports.AmsMethods.HandleNoTag = BaseMethods_js_1.default.HandleNoTag;\nexports.AmsMethods.HandleRef = BaseMethods_js_1.default.HandleRef;\nexports.AmsMethods.Macro = BaseMethods_js_1.default.Macro;\nexports.AmsMethods.Accent = BaseMethods_js_1.default.Accent;\nexports.AmsMethods.Tilde = BaseMethods_js_1.default.Tilde;\nexports.AmsMethods.Array = BaseMethods_js_1.default.Array;\nexports.AmsMethods.Spacer = BaseMethods_js_1.default.Spacer;\nexports.AmsMethods.NamedOp = BaseMethods_js_1.default.NamedOp;\nexports.AmsMethods.EqnArray = BaseMethods_js_1.default.EqnArray;\nexports.AmsMethods.Equation = BaseMethods_js_1.default.Equation;\n//# sourceMappingURL=AmsMethods.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AmsCdConfiguration = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nrequire(\"./AmsCdMappings.js\");\nexports.AmsCdConfiguration = Configuration_js_1.Configuration.create('amscd', {\n    handler: {\n        character: ['amscd_special'],\n        macro: ['amscd_macros'],\n        environment: ['amscd_environment']\n    },\n    options: {\n        amscd: {\n            colspace: '5pt',\n            rowspace: '5pt',\n            harrowsize: '2.75em',\n            varrowsize: '1.75em',\n            hideHorizontalLabels: false\n        }\n    }\n});\n//# sourceMappingURL=AmsCdConfiguration.js.map", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar sm = __importStar(require(\"../SymbolMap.js\"));\nvar ParseMethods_js_1 = __importDefault(require(\"../ParseMethods.js\"));\nvar AmsCdMethods_js_1 = __importDefault(require(\"./AmsCdMethods.js\"));\nnew sm.EnvironmentMap('amscd_environment', ParseMethods_js_1.default.environment, { CD: 'CD' }, AmsCdMethods_js_1.default);\nnew sm.CommandMap('amscd_macros', {\n    minCDarrowwidth: 'minCDarrowwidth',\n    minCDarrowheight: 'minCDarrowheight',\n}, AmsCdMethods_js_1.default);\nnew sm.MacroMap('amscd_special', { '@': 'arrow' }, AmsCdMethods_js_1.default);\n//# sourceMappingURL=AmsCdMappings.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar TexParser_js_1 = __importDefault(require(\"../TexParser.js\"));\nvar BaseConfiguration_js_1 = require(\"../base/BaseConfiguration.js\");\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar AmsCdMethods = {};\nAmsCdMethods.CD = function (parser, begin) {\n    parser.Push(begin);\n    var item = parser.itemFactory.create('array');\n    var options = parser.configuration.options.amscd;\n    item.setProperties({\n        minw: parser.stack.env.CD_minw || options.harrowsize,\n        minh: parser.stack.env.CD_minh || options.varrowsize\n    });\n    item.arraydef = {\n        columnalign: 'center',\n        columnspacing: options.colspace,\n        rowspacing: options.rowspace,\n        displaystyle: true\n    };\n    return item;\n};\nAmsCdMethods.arrow = function (parser, name) {\n    var c = parser.string.charAt(parser.i);\n    if (!c.match(/[><VA.|=]/)) {\n        return (0, BaseConfiguration_js_1.Other)(parser, name);\n    }\n    else {\n        parser.i++;\n    }\n    var first = parser.stack.Top();\n    if (!first.isKind('array') || first.Size()) {\n        AmsCdMethods.cell(parser, name);\n        first = parser.stack.Top();\n    }\n    var top = first;\n    var arrowRow = ((top.table.length % 2) === 1);\n    var n = (top.row.length + (arrowRow ? 0 : 1)) % 2;\n    while (n) {\n        AmsCdMethods.cell(parser, name);\n        n--;\n    }\n    var mml;\n    var hdef = { minsize: top.getProperty('minw'), stretchy: true }, vdef = { minsize: top.getProperty('minh'),\n        stretchy: true, symmetric: true, lspace: 0, rspace: 0 };\n    if (c === '.') {\n    }\n    else if (c === '|') {\n        mml = parser.create('token', 'mo', vdef, '\\u2225');\n    }\n    else if (c === '=') {\n        mml = parser.create('token', 'mo', hdef, '=');\n    }\n    else {\n        var arrow = {\n            '>': '\\u2192', '<': '\\u2190', 'V': '\\u2193', 'A': '\\u2191'\n        }[c];\n        var a = parser.GetUpTo(name + c, c);\n        var b = parser.GetUpTo(name + c, c);\n        if (c === '>' || c === '<') {\n            mml = parser.create('token', 'mo', hdef, arrow);\n            if (!a) {\n                a = '\\\\kern ' + top.getProperty('minw');\n            }\n            if (a || b) {\n                var pad = { width: '+.67em', lspace: '.33em' };\n                mml = parser.create('node', 'munderover', [mml]);\n                if (a) {\n                    var nodeA = new TexParser_js_1.default(a, parser.stack.env, parser.configuration).mml();\n                    var mpadded = parser.create('node', 'mpadded', [nodeA], pad);\n                    NodeUtil_js_1.default.setAttribute(mpadded, 'voffset', '.1em');\n                    NodeUtil_js_1.default.setChild(mml, mml.over, mpadded);\n                }\n                if (b) {\n                    var nodeB = new TexParser_js_1.default(b, parser.stack.env, parser.configuration).mml();\n                    NodeUtil_js_1.default.setChild(mml, mml.under, parser.create('node', 'mpadded', [nodeB], pad));\n                }\n                if (parser.configuration.options.amscd.hideHorizontalLabels) {\n                    mml = parser.create('node', 'mpadded', mml, { depth: 0, height: '.67em' });\n                }\n            }\n        }\n        else {\n            var arrowNode = parser.create('token', 'mo', vdef, arrow);\n            mml = arrowNode;\n            if (a || b) {\n                mml = parser.create('node', 'mrow');\n                if (a) {\n                    NodeUtil_js_1.default.appendChildren(mml, [new TexParser_js_1.default('\\\\scriptstyle\\\\llap{' + a + '}', parser.stack.env, parser.configuration).mml()]);\n                }\n                arrowNode.texClass = MmlNode_js_1.TEXCLASS.ORD;\n                NodeUtil_js_1.default.appendChildren(mml, [arrowNode]);\n                if (b) {\n                    NodeUtil_js_1.default.appendChildren(mml, [new TexParser_js_1.default('\\\\scriptstyle\\\\rlap{' + b + '}', parser.stack.env, parser.configuration).mml()]);\n                }\n            }\n        }\n    }\n    if (mml) {\n        parser.Push(mml);\n    }\n    AmsCdMethods.cell(parser, name);\n};\nAmsCdMethods.cell = function (parser, name) {\n    var top = parser.stack.Top();\n    if ((top.table || []).length % 2 === 0 && (top.row || []).length === 0) {\n        parser.Push(parser.create('node', 'mpadded', [], { height: '8.5pt', depth: '2pt' }));\n    }\n    parser.Push(parser.itemFactory.create('cell').setProperties({ isEntry: true, name: name }));\n};\nAmsCdMethods.minCDarrowwidth = function (parser, name) {\n    parser.stack.env.CD_minw = parser.GetDimen(name);\n};\nAmsCdMethods.minCDarrowheight = function (parser, name) {\n    parser.stack.env.CD_minh = parser.GetDimen(name);\n};\nexports.default = AmsCdMethods;\n//# sourceMappingURL=AmsCdMethods.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BboxConfiguration = exports.BboxMethods = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nexports.BboxMethods = {};\nexports.BboxMethods.BBox = function (parser, name) {\n    var bbox = parser.GetBrackets(name, '');\n    var math = parser.ParseArg(name);\n    var parts = bbox.split(/,/);\n    var def, background, style;\n    for (var i = 0, m = parts.length; i < m; i++) {\n        var part = parts[i].trim();\n        var match = part.match(/^(\\.\\d+|\\d+(\\.\\d*)?)(pt|em|ex|mu|px|in|cm|mm)$/);\n        if (match) {\n            if (def) {\n                throw new TexError_js_1.default('MultipleBBoxProperty', '%1 specified twice in %2', 'Padding', name);\n            }\n            var pad = BBoxPadding(match[1] + match[3]);\n            if (pad) {\n                def = {\n                    height: '+' + pad,\n                    depth: '+' + pad,\n                    lspace: pad,\n                    width: '+' + (2 * parseInt(match[1], 10)) + match[3]\n                };\n            }\n        }\n        else if (part.match(/^([a-z0-9]+|\\#[0-9a-f]{6}|\\#[0-9a-f]{3})$/i)) {\n            if (background) {\n                throw new TexError_js_1.default('MultipleBBoxProperty', '%1 specified twice in %2', 'Background', name);\n            }\n            background = part;\n        }\n        else if (part.match(/^[-a-z]+:/i)) {\n            if (style) {\n                throw new TexError_js_1.default('MultipleBBoxProperty', '%1 specified twice in %2', 'Style', name);\n            }\n            style = BBoxStyle(part);\n        }\n        else if (part !== '') {\n            throw new TexError_js_1.default('InvalidBBoxProperty', '\"%1\" doesn\\'t look like a color, a padding dimension, or a style', part);\n        }\n    }\n    if (def) {\n        math = parser.create('node', 'mpadded', [math], def);\n    }\n    if (background || style) {\n        def = {};\n        if (background) {\n            Object.assign(def, { mathbackground: background });\n        }\n        if (style) {\n            Object.assign(def, { style: style });\n        }\n        math = parser.create('node', 'mstyle', [math], def);\n    }\n    parser.Push(math);\n};\nvar BBoxStyle = function (styles) {\n    return styles;\n};\nvar BBoxPadding = function (pad) {\n    return pad;\n};\nnew SymbolMap_js_1.CommandMap('bbox', { bbox: 'BBox' }, exports.BboxMethods);\nexports.BboxConfiguration = Configuration_js_1.Configuration.create('bbox', { handler: { macro: ['bbox'] } });\n//# sourceMappingURL=BboxConfiguration.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BoldsymbolConfiguration = exports.rewriteBoldTokens = exports.createBoldToken = exports.BoldsymbolMethods = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar TexConstants_js_1 = require(\"../TexConstants.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar NodeFactory_js_1 = require(\"../NodeFactory.js\");\nvar BOLDVARIANT = {};\nBOLDVARIANT[TexConstants_js_1.TexConstant.Variant.NORMAL] = TexConstants_js_1.TexConstant.Variant.BOLD;\nBOLDVARIANT[TexConstants_js_1.TexConstant.Variant.ITALIC] = TexConstants_js_1.TexConstant.Variant.BOLDITALIC;\nBOLDVARIANT[TexConstants_js_1.TexConstant.Variant.FRAKTUR] = TexConstants_js_1.TexConstant.Variant.BOLDFRAKTUR;\nBOLDVARIANT[TexConstants_js_1.TexConstant.Variant.SCRIPT] = TexConstants_js_1.TexConstant.Variant.BOLDSCRIPT;\nBOLDVARIANT[TexConstants_js_1.TexConstant.Variant.SANSSERIF] = TexConstants_js_1.TexConstant.Variant.BOLDSANSSERIF;\nBOLDVARIANT['-tex-calligraphic'] = '-tex-bold-calligraphic';\nBOLDVARIANT['-tex-oldstyle'] = '-tex-bold-oldstyle';\nBOLDVARIANT['-tex-mathit'] = TexConstants_js_1.TexConstant.Variant.BOLDITALIC;\nexports.BoldsymbolMethods = {};\nexports.BoldsymbolMethods.Boldsymbol = function (parser, name) {\n    var boldsymbol = parser.stack.env['boldsymbol'];\n    parser.stack.env['boldsymbol'] = true;\n    var mml = parser.ParseArg(name);\n    parser.stack.env['boldsymbol'] = boldsymbol;\n    parser.Push(mml);\n};\nnew SymbolMap_js_1.CommandMap('boldsymbol', { boldsymbol: 'Boldsymbol' }, exports.BoldsymbolMethods);\nfunction createBoldToken(factory, kind, def, text) {\n    var token = NodeFactory_js_1.NodeFactory.createToken(factory, kind, def, text);\n    if (kind !== 'mtext' &&\n        factory.configuration.parser.stack.env['boldsymbol']) {\n        NodeUtil_js_1.default.setProperty(token, 'fixBold', true);\n        factory.configuration.addNode('fixBold', token);\n    }\n    return token;\n}\nexports.createBoldToken = createBoldToken;\nfunction rewriteBoldTokens(arg) {\n    var e_1, _a;\n    try {\n        for (var _b = __values(arg.data.getList('fixBold')), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var node = _c.value;\n            if (NodeUtil_js_1.default.getProperty(node, 'fixBold')) {\n                var variant = NodeUtil_js_1.default.getAttribute(node, 'mathvariant');\n                if (variant == null) {\n                    NodeUtil_js_1.default.setAttribute(node, 'mathvariant', TexConstants_js_1.TexConstant.Variant.BOLD);\n                }\n                else {\n                    NodeUtil_js_1.default.setAttribute(node, 'mathvariant', BOLDVARIANT[variant] || variant);\n                }\n                NodeUtil_js_1.default.removeProperties(node, 'fixBold');\n            }\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n}\nexports.rewriteBoldTokens = rewriteBoldTokens;\nexports.BoldsymbolConfiguration = Configuration_js_1.Configuration.create('boldsymbol', {\n    handler: { macro: ['boldsymbol'] },\n    nodes: { 'token': createBoldToken },\n    postprocessors: [rewriteBoldTokens]\n});\n//# sourceMappingURL=BoldsymbolConfiguration.js.map", "\"use strict\";\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BraketConfiguration = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar BraketItems_js_1 = require(\"./BraketItems.js\");\nrequire(\"./BraketMappings.js\");\nexports.BraketConfiguration = Configuration_js_1.Configuration.create('braket', {\n    handler: {\n        character: ['Braket-characters'],\n        macro: ['Braket-macros']\n    },\n    items: (_a = {},\n        _a[BraketItems_js_1.BraketItem.prototype.kind] = BraketItems_js_1.BraketItem,\n        _a)\n});\n//# sourceMappingURL=BraketConfiguration.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BraketItem = void 0;\nvar StackItem_js_1 = require(\"../StackItem.js\");\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar BraketItem = (function (_super) {\n    __extends(BraketItem, _super);\n    function BraketItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(BraketItem.prototype, \"kind\", {\n        get: function () {\n            return 'braket';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BraketItem.prototype, \"isOpen\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    BraketItem.prototype.checkItem = function (item) {\n        if (item.isKind('close')) {\n            return [[this.factory.create('mml', this.toMml())], true];\n        }\n        if (item.isKind('mml')) {\n            this.Push(item.toMml());\n            if (this.getProperty('single')) {\n                return [[this.toMml()], true];\n            }\n            return StackItem_js_1.BaseItem.fail;\n        }\n        return _super.prototype.checkItem.call(this, item);\n    };\n    BraketItem.prototype.toMml = function () {\n        var inner = _super.prototype.toMml.call(this);\n        var open = this.getProperty('open');\n        var close = this.getProperty('close');\n        if (this.getProperty('stretchy')) {\n            return ParseUtil_js_1.default.fenced(this.factory.configuration, open, inner, close);\n        }\n        var attrs = { fence: true, stretchy: false, symmetric: true, texClass: MmlNode_js_1.TEXCLASS.OPEN };\n        var openNode = this.create('token', 'mo', attrs, open);\n        attrs.texClass = MmlNode_js_1.TEXCLASS.CLOSE;\n        var closeNode = this.create('token', 'mo', attrs, close);\n        var mrow = this.create('node', 'mrow', [openNode, inner, closeNode], { open: open, close: close, texClass: MmlNode_js_1.TEXCLASS.INNER });\n        return mrow;\n    };\n    return BraketItem;\n}(StackItem_js_1.BaseItem));\nexports.BraketItem = BraketItem;\n//# sourceMappingURL=BraketItems.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar BraketMethods_js_1 = __importDefault(require(\"./BraketMethods.js\"));\nnew SymbolMap_js_1.CommandMap('Braket-macros', {\n    bra: ['Macro', '{\\\\langle {#1} \\\\vert}', 1],\n    ket: ['Macro', '{\\\\vert {#1} \\\\rangle}', 1],\n    braket: ['Braket', '\\u27E8', '\\u27E9', false, Infinity],\n    'set': ['Braket', '{', '}', false, 1],\n    Bra: ['Macro', '{\\\\left\\\\langle {#1} \\\\right\\\\vert}', 1],\n    Ket: ['Macro', '{\\\\left\\\\vert {#1} \\\\right\\\\rangle}', 1],\n    Braket: ['Braket', '\\u27E8', '\\u27E9', true, Infinity],\n    Set: ['Braket', '{', '}', true, 1],\n    ketbra: ['Macro', '{\\\\vert {#1} \\\\rangle\\\\langle {#2} \\\\vert}', 2],\n    Ketbra: ['Macro', '{\\\\left\\\\vert {#1} \\\\right\\\\rangle\\\\left\\\\langle {#2} \\\\right\\\\vert}', 2],\n    '|': 'Bar'\n}, BraketMethods_js_1.default);\nnew SymbolMap_js_1.MacroMap('Braket-characters', {\n    '|': 'Bar'\n}, BraketMethods_js_1.default);\n//# sourceMappingURL=BraketMappings.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar BaseMethods_js_1 = __importDefault(require(\"../base/BaseMethods.js\"));\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar BraketMethods = {};\nBraketMethods.Macro = BaseMethods_js_1.default.Macro;\nBraketMethods.Braket = function (parser, _name, open, close, stretchy, barmax) {\n    var next = parser.GetNext();\n    if (next === '') {\n        throw new TexError_js_1.default('MissingArgFor', 'Missing argument for %1', parser.currentCS);\n    }\n    var single = true;\n    if (next === '{') {\n        parser.i++;\n        single = false;\n    }\n    parser.Push(parser.itemFactory.create('braket')\n        .setProperties({ barmax: barmax, barcount: 0, open: open,\n        close: close, stretchy: stretchy, single: single }));\n};\nBraketMethods.Bar = function (parser, name) {\n    var c = name === '|' ? '|' : '\\u2225';\n    var top = parser.stack.Top();\n    if (top.kind !== 'braket' ||\n        top.getProperty('barcount') >= top.getProperty('barmax')) {\n        var mml = parser.create('token', 'mo', { texClass: MmlNode_js_1.TEXCLASS.ORD, stretchy: false }, c);\n        parser.Push(mml);\n        return;\n    }\n    if (c === '|' && parser.GetNext() === '|') {\n        parser.i++;\n        c = '\\u2225';\n    }\n    var stretchy = top.getProperty('stretchy');\n    if (!stretchy) {\n        var node_1 = parser.create('token', 'mo', { stretchy: false, braketbar: true }, c);\n        parser.Push(node_1);\n        return;\n    }\n    var node = parser.create('node', 'TeXAtom', [], { texClass: MmlNode_js_1.TEXCLASS.CLOSE });\n    parser.Push(node);\n    top.setProperty('barcount', top.getProperty('barcount') + 1);\n    node = parser.create('token', 'mo', { stretchy: true, braketbar: true }, c);\n    parser.Push(node);\n    node = parser.create('node', 'TeXAtom', [], { texClass: MmlNode_js_1.TEXCLASS.OPEN });\n    parser.Push(node);\n};\nexports.default = BraketMethods;\n//# sourceMappingURL=BraketMethods.js.map", "\"use strict\";\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BussproofsConfiguration = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar BussproofsItems_js_1 = require(\"./BussproofsItems.js\");\nvar BussproofsUtil_js_1 = require(\"./BussproofsUtil.js\");\nrequire(\"./BussproofsMappings.js\");\nexports.BussproofsConfiguration = Configuration_js_1.Configuration.create('bussproofs', {\n    handler: {\n        macro: ['Bussproofs-macros'],\n        environment: ['Bussproofs-environments']\n    },\n    items: (_a = {},\n        _a[BussproofsItems_js_1.ProofTreeItem.prototype.kind] = BussproofsItems_js_1.ProofTreeItem,\n        _a),\n    preprocessors: [\n        [BussproofsUtil_js_1.saveDocument, 1]\n    ],\n    postprocessors: [\n        [BussproofsUtil_js_1.clearDocument, 3],\n        [BussproofsUtil_js_1.makeBsprAttributes, 2],\n        [BussproofsUtil_js_1.balanceRules, 1]\n    ]\n});\n//# sourceMappingURL=BussproofsConfiguration.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ProofTreeItem = void 0;\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar StackItem_js_1 = require(\"../StackItem.js\");\nvar Stack_js_1 = __importDefault(require(\"../Stack.js\"));\nvar BussproofsUtil = __importStar(require(\"./BussproofsUtil.js\"));\nvar ProofTreeItem = (function (_super) {\n    __extends(ProofTreeItem, _super);\n    function ProofTreeItem() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.leftLabel = null;\n        _this.rigthLabel = null;\n        _this.innerStack = new Stack_js_1.default(_this.factory, {}, true);\n        return _this;\n    }\n    Object.defineProperty(ProofTreeItem.prototype, \"kind\", {\n        get: function () {\n            return 'proofTree';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ProofTreeItem.prototype.checkItem = function (item) {\n        if (item.isKind('end') && item.getName() === 'prooftree') {\n            var node = this.toMml();\n            BussproofsUtil.setProperty(node, 'proof', true);\n            return [[this.factory.create('mml', node), item], true];\n        }\n        if (item.isKind('stop')) {\n            throw new TexError_js_1.default('EnvMissingEnd', 'Missing \\\\end{%1}', this.getName());\n        }\n        this.innerStack.Push(item);\n        return StackItem_js_1.BaseItem.fail;\n    };\n    ProofTreeItem.prototype.toMml = function () {\n        var tree = _super.prototype.toMml.call(this);\n        var start = this.innerStack.Top();\n        if (start.isKind('start') && !start.Size()) {\n            return tree;\n        }\n        this.innerStack.Push(this.factory.create('stop'));\n        var prefix = this.innerStack.Top().toMml();\n        return this.create('node', 'mrow', [prefix, tree], {});\n    };\n    return ProofTreeItem;\n}(StackItem_js_1.BaseItem));\nexports.ProofTreeItem = ProofTreeItem;\n//# sourceMappingURL=BussproofsItems.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar BussproofsMethods_js_1 = __importDefault(require(\"./BussproofsMethods.js\"));\nvar ParseMethods_js_1 = __importDefault(require(\"../ParseMethods.js\"));\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nnew SymbolMap_js_1.CommandMap('Bussproofs-macros', {\n    AxiomC: 'Axiom',\n    UnaryInfC: ['Inference', 1],\n    BinaryInfC: ['Inference', 2],\n    TrinaryInfC: ['Inference', 3],\n    QuaternaryInfC: ['Inference', 4],\n    QuinaryInfC: ['Inference', 5],\n    RightLabel: ['Label', 'right'],\n    LeftLabel: ['Label', 'left'],\n    AXC: 'Axiom',\n    UIC: ['Inference', 1],\n    BIC: ['Inference', 2],\n    TIC: ['Inference', 3],\n    RL: ['Label', 'right'],\n    LL: ['Label', 'left'],\n    noLine: ['SetLine', 'none', false],\n    singleLine: ['SetLine', 'solid', false],\n    solidLine: ['SetLine', 'solid', false],\n    dashedLine: ['SetLine', 'dashed', false],\n    alwaysNoLine: ['SetLine', 'none', true],\n    alwaysSingleLine: ['SetLine', 'solid', true],\n    alwaysSolidLine: ['SetLine', 'solid', true],\n    alwaysDashedLine: ['SetLine', 'dashed', true],\n    rootAtTop: ['RootAtTop', true],\n    alwaysRootAtTop: ['RootAtTop', true],\n    rootAtBottom: ['RootAtTop', false],\n    alwaysRootAtBottom: ['RootAtTop', false],\n    fCenter: 'FCenter',\n    Axiom: 'AxiomF',\n    UnaryInf: ['InferenceF', 1],\n    BinaryInf: ['InferenceF', 2],\n    TrinaryInf: ['InferenceF', 3],\n    QuaternaryInf: ['InferenceF', 4],\n    QuinaryInf: ['InferenceF', 5]\n}, BussproofsMethods_js_1.default);\nnew SymbolMap_js_1.EnvironmentMap('Bussproofs-environments', ParseMethods_js_1.default.environment, {\n    prooftree: ['Prooftree', null, false]\n}, BussproofsMethods_js_1.default);\n//# sourceMappingURL=BussproofsMappings.js.map", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar TexParser_js_1 = __importDefault(require(\"../TexParser.js\"));\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar BussproofsUtil = __importStar(require(\"./BussproofsUtil.js\"));\nvar BussproofsMethods = {};\nBussproofsMethods.Prooftree = function (parser, begin) {\n    parser.Push(begin);\n    var newItem = parser.itemFactory.create('proofTree').\n        setProperties({ name: begin.getName(),\n        line: 'solid', currentLine: 'solid', rootAtTop: false });\n    return newItem;\n};\nBussproofsMethods.Axiom = function (parser, name) {\n    var top = parser.stack.Top();\n    if (top.kind !== 'proofTree') {\n        throw new TexError_js_1.default('IllegalProofCommand', 'Proof commands only allowed in prooftree environment.');\n    }\n    var content = paddedContent(parser, parser.GetArgument(name));\n    BussproofsUtil.setProperty(content, 'axiom', true);\n    top.Push(content);\n};\nvar paddedContent = function (parser, content) {\n    var nodes = ParseUtil_js_1.default.internalMath(parser, ParseUtil_js_1.default.trimSpaces(content), 0);\n    if (!nodes[0].childNodes[0].childNodes.length) {\n        return parser.create('node', 'mrow', []);\n    }\n    var lpad = parser.create('node', 'mspace', [], { width: '.5ex' });\n    var rpad = parser.create('node', 'mspace', [], { width: '.5ex' });\n    return parser.create('node', 'mrow', __spreadArray(__spreadArray([lpad], __read(nodes), false), [rpad], false));\n};\nBussproofsMethods.Inference = function (parser, name, n) {\n    var top = parser.stack.Top();\n    if (top.kind !== 'proofTree') {\n        throw new TexError_js_1.default('IllegalProofCommand', 'Proof commands only allowed in prooftree environment.');\n    }\n    if (top.Size() < n) {\n        throw new TexError_js_1.default('BadProofTree', 'Proof tree badly specified.');\n    }\n    var rootAtTop = top.getProperty('rootAtTop');\n    var childCount = (n === 1 && !top.Peek()[0].childNodes.length) ? 0 : n;\n    var children = [];\n    do {\n        if (children.length) {\n            children.unshift(parser.create('node', 'mtd', [], {}));\n        }\n        children.unshift(parser.create('node', 'mtd', [top.Pop()], { 'rowalign': (rootAtTop ? 'top' : 'bottom') }));\n        n--;\n    } while (n > 0);\n    var row = parser.create('node', 'mtr', children, {});\n    var table = parser.create('node', 'mtable', [row], { framespacing: '0 0' });\n    var conclusion = paddedContent(parser, parser.GetArgument(name));\n    var style = top.getProperty('currentLine');\n    if (style !== top.getProperty('line')) {\n        top.setProperty('currentLine', top.getProperty('line'));\n    }\n    var rule = createRule(parser, table, [conclusion], top.getProperty('left'), top.getProperty('right'), style, rootAtTop);\n    top.setProperty('left', null);\n    top.setProperty('right', null);\n    BussproofsUtil.setProperty(rule, 'inference', childCount);\n    parser.configuration.addNode('inference', rule);\n    top.Push(rule);\n};\nfunction createRule(parser, premise, conclusions, left, right, style, rootAtTop) {\n    var upper = parser.create('node', 'mtr', [parser.create('node', 'mtd', [premise], {})], {});\n    var lower = parser.create('node', 'mtr', [parser.create('node', 'mtd', conclusions, {})], {});\n    var rule = parser.create('node', 'mtable', rootAtTop ? [lower, upper] : [upper, lower], { align: 'top 2', rowlines: style, framespacing: '0 0' });\n    BussproofsUtil.setProperty(rule, 'inferenceRule', rootAtTop ? 'up' : 'down');\n    var leftLabel, rightLabel;\n    if (left) {\n        leftLabel = parser.create('node', 'mpadded', [left], { height: '+.5em', width: '+.5em', voffset: '-.15em' });\n        BussproofsUtil.setProperty(leftLabel, 'prooflabel', 'left');\n    }\n    if (right) {\n        rightLabel = parser.create('node', 'mpadded', [right], { height: '+.5em', width: '+.5em', voffset: '-.15em' });\n        BussproofsUtil.setProperty(rightLabel, 'prooflabel', 'right');\n    }\n    var children, label;\n    if (left && right) {\n        children = [leftLabel, rule, rightLabel];\n        label = 'both';\n    }\n    else if (left) {\n        children = [leftLabel, rule];\n        label = 'left';\n    }\n    else if (right) {\n        children = [rule, rightLabel];\n        label = 'right';\n    }\n    else {\n        return rule;\n    }\n    rule = parser.create('node', 'mrow', children);\n    BussproofsUtil.setProperty(rule, 'labelledRule', label);\n    return rule;\n}\nBussproofsMethods.Label = function (parser, name, side) {\n    var top = parser.stack.Top();\n    if (top.kind !== 'proofTree') {\n        throw new TexError_js_1.default('IllegalProofCommand', 'Proof commands only allowed in prooftree environment.');\n    }\n    var content = ParseUtil_js_1.default.internalMath(parser, parser.GetArgument(name), 0);\n    var label = (content.length > 1) ?\n        parser.create('node', 'mrow', content, {}) : content[0];\n    top.setProperty(side, label);\n};\nBussproofsMethods.SetLine = function (parser, _name, style, always) {\n    var top = parser.stack.Top();\n    if (top.kind !== 'proofTree') {\n        throw new TexError_js_1.default('IllegalProofCommand', 'Proof commands only allowed in prooftree environment.');\n    }\n    top.setProperty('currentLine', style);\n    if (always) {\n        top.setProperty('line', style);\n    }\n};\nBussproofsMethods.RootAtTop = function (parser, _name, where) {\n    var top = parser.stack.Top();\n    if (top.kind !== 'proofTree') {\n        throw new TexError_js_1.default('IllegalProofCommand', 'Proof commands only allowed in prooftree environment.');\n    }\n    top.setProperty('rootAtTop', where);\n};\nBussproofsMethods.AxiomF = function (parser, name) {\n    var top = parser.stack.Top();\n    if (top.kind !== 'proofTree') {\n        throw new TexError_js_1.default('IllegalProofCommand', 'Proof commands only allowed in prooftree environment.');\n    }\n    var line = parseFCenterLine(parser, name);\n    BussproofsUtil.setProperty(line, 'axiom', true);\n    top.Push(line);\n};\nfunction parseFCenterLine(parser, name) {\n    var dollar = parser.GetNext();\n    if (dollar !== '$') {\n        throw new TexError_js_1.default('IllegalUseOfCommand', 'Use of %1 does not match it\\'s definition.', name);\n    }\n    parser.i++;\n    var axiom = parser.GetUpTo(name, '$');\n    if (axiom.indexOf('\\\\fCenter') === -1) {\n        throw new TexError_js_1.default('IllegalUseOfCommand', 'Missing \\\\fCenter in %1.', name);\n    }\n    var _a = __read(axiom.split('\\\\fCenter'), 2), prem = _a[0], conc = _a[1];\n    var premise = (new TexParser_js_1.default(prem, parser.stack.env, parser.configuration)).mml();\n    var conclusion = (new TexParser_js_1.default(conc, parser.stack.env, parser.configuration)).mml();\n    var fcenter = (new TexParser_js_1.default('\\\\fCenter', parser.stack.env, parser.configuration)).mml();\n    var left = parser.create('node', 'mtd', [premise], {});\n    var middle = parser.create('node', 'mtd', [fcenter], {});\n    var right = parser.create('node', 'mtd', [conclusion], {});\n    var row = parser.create('node', 'mtr', [left, middle, right], {});\n    var table = parser.create('node', 'mtable', [row], { columnspacing: '.5ex', columnalign: 'center 2' });\n    BussproofsUtil.setProperty(table, 'sequent', true);\n    parser.configuration.addNode('sequent', row);\n    return table;\n}\nBussproofsMethods.FCenter = function (_parser, _name) { };\nBussproofsMethods.InferenceF = function (parser, name, n) {\n    var top = parser.stack.Top();\n    if (top.kind !== 'proofTree') {\n        throw new TexError_js_1.default('IllegalProofCommand', 'Proof commands only allowed in prooftree environment.');\n    }\n    if (top.Size() < n) {\n        throw new TexError_js_1.default('BadProofTree', 'Proof tree badly specified.');\n    }\n    var rootAtTop = top.getProperty('rootAtTop');\n    var childCount = (n === 1 && !top.Peek()[0].childNodes.length) ? 0 : n;\n    var children = [];\n    do {\n        if (children.length) {\n            children.unshift(parser.create('node', 'mtd', [], {}));\n        }\n        children.unshift(parser.create('node', 'mtd', [top.Pop()], { 'rowalign': (rootAtTop ? 'top' : 'bottom') }));\n        n--;\n    } while (n > 0);\n    var row = parser.create('node', 'mtr', children, {});\n    var table = parser.create('node', 'mtable', [row], { framespacing: '0 0' });\n    var conclusion = parseFCenterLine(parser, name);\n    var style = top.getProperty('currentLine');\n    if (style !== top.getProperty('line')) {\n        top.setProperty('currentLine', top.getProperty('line'));\n    }\n    var rule = createRule(parser, table, [conclusion], top.getProperty('left'), top.getProperty('right'), style, rootAtTop);\n    top.setProperty('left', null);\n    top.setProperty('right', null);\n    BussproofsUtil.setProperty(rule, 'inference', childCount);\n    parser.configuration.addNode('inference', rule);\n    top.Push(rule);\n};\nexports.default = BussproofsMethods;\n//# sourceMappingURL=BussproofsMethods.js.map", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.clearDocument = exports.saveDocument = exports.makeBsprAttributes = exports.removeProperty = exports.getProperty = exports.setProperty = exports.balanceRules = void 0;\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar doc = null;\nvar item = null;\nvar getBBox = function (node) {\n    item.root = node;\n    var width = doc.outputJax.getBBox(item, doc).w;\n    return width;\n};\nvar getRule = function (node) {\n    var i = 0;\n    while (node && !NodeUtil_js_1.default.isType(node, 'mtable')) {\n        if (NodeUtil_js_1.default.isType(node, 'text')) {\n            return null;\n        }\n        if (NodeUtil_js_1.default.isType(node, 'mrow')) {\n            node = node.childNodes[0];\n            i = 0;\n            continue;\n        }\n        node = node.parent.childNodes[i];\n        i++;\n    }\n    return node;\n};\nvar getPremises = function (rule, direction) {\n    return rule.childNodes[direction === 'up' ? 1 : 0].childNodes[0].\n        childNodes[0].childNodes[0].childNodes[0];\n};\nvar getPremise = function (premises, n) {\n    return premises.childNodes[n].childNodes[0].childNodes[0];\n};\nvar firstPremise = function (premises) {\n    return getPremise(premises, 0);\n};\nvar lastPremise = function (premises) {\n    return getPremise(premises, premises.childNodes.length - 1);\n};\nvar getConclusion = function (rule, direction) {\n    return rule.childNodes[direction === 'up' ? 0 : 1].childNodes[0].childNodes[0].childNodes[0];\n};\nvar getColumn = function (inf) {\n    while (inf && !NodeUtil_js_1.default.isType(inf, 'mtd')) {\n        inf = inf.parent;\n    }\n    return inf;\n};\nvar nextSibling = function (inf) {\n    return inf.parent.childNodes[inf.parent.childNodes.indexOf(inf) + 1];\n};\nvar previousSibling = function (inf) {\n    return inf.parent.childNodes[inf.parent.childNodes.indexOf(inf) - 1];\n};\nvar getParentInf = function (inf) {\n    while (inf && (0, exports.getProperty)(inf, 'inference') == null) {\n        inf = inf.parent;\n    }\n    return inf;\n};\nvar getSpaces = function (inf, rule, right) {\n    if (right === void 0) { right = false; }\n    var result = 0;\n    if (inf === rule) {\n        return result;\n    }\n    if (inf !== rule.parent) {\n        var children_1 = inf.childNodes;\n        var index_1 = right ? children_1.length - 1 : 0;\n        if (NodeUtil_js_1.default.isType(children_1[index_1], 'mspace')) {\n            result += getBBox(children_1[index_1]);\n        }\n        inf = rule.parent;\n    }\n    if (inf === rule) {\n        return result;\n    }\n    var children = inf.childNodes;\n    var index = right ? children.length - 1 : 0;\n    if (children[index] !== rule) {\n        result += getBBox(children[index]);\n    }\n    return result;\n};\nvar adjustValue = function (inf, right) {\n    if (right === void 0) { right = false; }\n    var rule = getRule(inf);\n    var conc = getConclusion(rule, (0, exports.getProperty)(rule, 'inferenceRule'));\n    var w = getSpaces(inf, rule, right);\n    var x = getBBox(rule);\n    var y = getBBox(conc);\n    return w + ((x - y) / 2);\n};\nvar addSpace = function (config, inf, space, right) {\n    if (right === void 0) { right = false; }\n    if ((0, exports.getProperty)(inf, 'inferenceRule') ||\n        (0, exports.getProperty)(inf, 'labelledRule')) {\n        var mrow = config.nodeFactory.create('node', 'mrow');\n        inf.parent.replaceChild(mrow, inf);\n        mrow.setChildren([inf]);\n        moveProperties(inf, mrow);\n        inf = mrow;\n    }\n    var index = right ? inf.childNodes.length - 1 : 0;\n    var mspace = inf.childNodes[index];\n    if (NodeUtil_js_1.default.isType(mspace, 'mspace')) {\n        NodeUtil_js_1.default.setAttribute(mspace, 'width', ParseUtil_js_1.default.Em(ParseUtil_js_1.default.dimen2em(NodeUtil_js_1.default.getAttribute(mspace, 'width')) + space));\n        return;\n    }\n    mspace = config.nodeFactory.create('node', 'mspace', [], { width: ParseUtil_js_1.default.Em(space) });\n    if (right) {\n        inf.appendChild(mspace);\n        return;\n    }\n    mspace.parent = inf;\n    inf.childNodes.unshift(mspace);\n};\nvar moveProperties = function (src, dest) {\n    var props = ['inference', 'proof', 'maxAdjust', 'labelledRule'];\n    props.forEach(function (x) {\n        var value = (0, exports.getProperty)(src, x);\n        if (value != null) {\n            (0, exports.setProperty)(dest, x, value);\n            (0, exports.removeProperty)(src, x);\n        }\n    });\n};\nvar adjustSequents = function (config) {\n    var sequents = config.nodeLists['sequent'];\n    if (!sequents) {\n        return;\n    }\n    for (var i = sequents.length - 1, seq = void 0; seq = sequents[i]; i--) {\n        if ((0, exports.getProperty)(seq, 'sequentProcessed')) {\n            (0, exports.removeProperty)(seq, 'sequentProcessed');\n            continue;\n        }\n        var collect = [];\n        var inf = getParentInf(seq);\n        if ((0, exports.getProperty)(inf, 'inference') !== 1) {\n            continue;\n        }\n        collect.push(seq);\n        while ((0, exports.getProperty)(inf, 'inference') === 1) {\n            inf = getRule(inf);\n            var premise = firstPremise(getPremises(inf, (0, exports.getProperty)(inf, 'inferenceRule')));\n            var sequent = ((0, exports.getProperty)(premise, 'inferenceRule')) ?\n                getConclusion(premise, (0, exports.getProperty)(premise, 'inferenceRule')) :\n                premise;\n            if ((0, exports.getProperty)(sequent, 'sequent')) {\n                seq = sequent.childNodes[0];\n                collect.push(seq);\n                (0, exports.setProperty)(seq, 'sequentProcessed', true);\n            }\n            inf = premise;\n        }\n        adjustSequentPairwise(config, collect);\n    }\n};\nvar addSequentSpace = function (config, sequent, position, direction, width) {\n    var mspace = config.nodeFactory.create('node', 'mspace', [], { width: ParseUtil_js_1.default.Em(width) });\n    if (direction === 'left') {\n        var row = sequent.childNodes[position].childNodes[0];\n        mspace.parent = row;\n        row.childNodes.unshift(mspace);\n    }\n    else {\n        sequent.childNodes[position].appendChild(mspace);\n    }\n    (0, exports.setProperty)(sequent.parent, 'sequentAdjust_' + direction, width);\n};\nvar adjustSequentPairwise = function (config, sequents) {\n    var top = sequents.pop();\n    while (sequents.length) {\n        var bottom = sequents.pop();\n        var _a = __read(compareSequents(top, bottom), 2), left = _a[0], right = _a[1];\n        if ((0, exports.getProperty)(top.parent, 'axiom')) {\n            addSequentSpace(config, left < 0 ? top : bottom, 0, 'left', Math.abs(left));\n            addSequentSpace(config, right < 0 ? top : bottom, 2, 'right', Math.abs(right));\n        }\n        top = bottom;\n    }\n};\nvar compareSequents = function (top, bottom) {\n    var tr = getBBox(top.childNodes[2]);\n    var br = getBBox(bottom.childNodes[2]);\n    var tl = getBBox(top.childNodes[0]);\n    var bl = getBBox(bottom.childNodes[0]);\n    var dl = tl - bl;\n    var dr = tr - br;\n    return [dl, dr];\n};\nvar balanceRules = function (arg) {\n    var e_1, _a;\n    item = new arg.document.options.MathItem('', null, arg.math.display);\n    var config = arg.data;\n    adjustSequents(config);\n    var inferences = config.nodeLists['inference'] || [];\n    try {\n        for (var inferences_1 = __values(inferences), inferences_1_1 = inferences_1.next(); !inferences_1_1.done; inferences_1_1 = inferences_1.next()) {\n            var inf = inferences_1_1.value;\n            var isProof = (0, exports.getProperty)(inf, 'proof');\n            var rule = getRule(inf);\n            var premises = getPremises(rule, (0, exports.getProperty)(rule, 'inferenceRule'));\n            var premiseF = firstPremise(premises);\n            if ((0, exports.getProperty)(premiseF, 'inference')) {\n                var adjust_1 = adjustValue(premiseF);\n                if (adjust_1) {\n                    addSpace(config, premiseF, -adjust_1);\n                    var w_1 = getSpaces(inf, rule, false);\n                    addSpace(config, inf, adjust_1 - w_1);\n                }\n            }\n            var premiseL = lastPremise(premises);\n            if ((0, exports.getProperty)(premiseL, 'inference') == null) {\n                continue;\n            }\n            var adjust = adjustValue(premiseL, true);\n            addSpace(config, premiseL, -adjust, true);\n            var w = getSpaces(inf, rule, true);\n            var maxAdjust = (0, exports.getProperty)(inf, 'maxAdjust');\n            if (maxAdjust != null) {\n                adjust = Math.max(adjust, maxAdjust);\n            }\n            var column = void 0;\n            if (isProof || !(column = getColumn(inf))) {\n                addSpace(config, (0, exports.getProperty)(inf, 'proof') ? inf : inf.parent, adjust - w, true);\n                continue;\n            }\n            var sibling = nextSibling(column);\n            if (sibling) {\n                var pos = config.nodeFactory.create('node', 'mspace', [], { width: adjust - w + 'em' });\n                sibling.appendChild(pos);\n                inf.removeProperty('maxAdjust');\n                continue;\n            }\n            var parentRule = getParentInf(column);\n            if (!parentRule) {\n                continue;\n            }\n            adjust = (0, exports.getProperty)(parentRule, 'maxAdjust') ?\n                Math.max((0, exports.getProperty)(parentRule, 'maxAdjust'), adjust) : adjust;\n            (0, exports.setProperty)(parentRule, 'maxAdjust', adjust);\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (inferences_1_1 && !inferences_1_1.done && (_a = inferences_1.return)) _a.call(inferences_1);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n};\nexports.balanceRules = balanceRules;\nvar property_prefix = 'bspr_';\nvar blacklistedProperties = (_a = {},\n    _a[property_prefix + 'maxAdjust'] = true,\n    _a);\nvar setProperty = function (node, property, value) {\n    NodeUtil_js_1.default.setProperty(node, property_prefix + property, value);\n};\nexports.setProperty = setProperty;\nvar getProperty = function (node, property) {\n    return NodeUtil_js_1.default.getProperty(node, property_prefix + property);\n};\nexports.getProperty = getProperty;\nvar removeProperty = function (node, property) {\n    node.removeProperty(property_prefix + property);\n};\nexports.removeProperty = removeProperty;\nvar makeBsprAttributes = function (arg) {\n    arg.data.root.walkTree(function (mml, _data) {\n        var attr = [];\n        mml.getPropertyNames().forEach(function (x) {\n            if (!blacklistedProperties[x] && x.match(RegExp('^' + property_prefix))) {\n                attr.push(x + ':' + mml.getProperty(x));\n            }\n        });\n        if (attr.length) {\n            NodeUtil_js_1.default.setAttribute(mml, 'semantics', attr.join(';'));\n        }\n    });\n};\nexports.makeBsprAttributes = makeBsprAttributes;\nvar saveDocument = function (arg) {\n    doc = arg.document;\n    if (!('getBBox' in doc.outputJax)) {\n        throw Error('The bussproofs extension requires an output jax with a getBBox() method');\n    }\n};\nexports.saveDocument = saveDocument;\nvar clearDocument = function (_arg) {\n    doc = null;\n};\nexports.clearDocument = clearDocument;\n//# sourceMappingURL=BussproofsUtil.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CancelConfiguration = exports.CancelMethods = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar TexConstants_js_1 = require(\"../TexConstants.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar EncloseConfiguration_js_1 = require(\"../enclose/EncloseConfiguration.js\");\nexports.CancelMethods = {};\nexports.CancelMethods.Cancel = function (parser, name, notation) {\n    var attr = parser.GetBrackets(name, '');\n    var math = parser.ParseArg(name);\n    var def = ParseUtil_js_1.default.keyvalOptions(attr, EncloseConfiguration_js_1.ENCLOSE_OPTIONS);\n    def['notation'] = notation;\n    parser.Push(parser.create('node', 'menclose', [math], def));\n};\nexports.CancelMethods.CancelTo = function (parser, name) {\n    var attr = parser.GetBrackets(name, '');\n    var value = parser.ParseArg(name);\n    var math = parser.ParseArg(name);\n    var def = ParseUtil_js_1.default.keyvalOptions(attr, EncloseConfiguration_js_1.ENCLOSE_OPTIONS);\n    def['notation'] = [TexConstants_js_1.TexConstant.Notation.UPDIAGONALSTRIKE,\n        TexConstants_js_1.TexConstant.Notation.UPDIAGONALARROW,\n        TexConstants_js_1.TexConstant.Notation.NORTHEASTARROW].join(' ');\n    value = parser.create('node', 'mpadded', [value], { depth: '-.1em', height: '+.1em', voffset: '.1em' });\n    parser.Push(parser.create('node', 'msup', [parser.create('node', 'menclose', [math], def), value]));\n};\nnew SymbolMap_js_1.CommandMap('cancel', {\n    cancel: ['Cancel', TexConstants_js_1.TexConstant.Notation.UPDIAGONALSTRIKE],\n    bcancel: ['Cancel', TexConstants_js_1.TexConstant.Notation.DOWNDIAGONALSTRIKE],\n    xcancel: ['Cancel', TexConstants_js_1.TexConstant.Notation.UPDIAGONALSTRIKE + ' ' +\n            TexConstants_js_1.TexConstant.Notation.DOWNDIAGONALSTRIKE],\n    cancelto: 'CancelTo'\n}, exports.CancelMethods);\nexports.CancelConfiguration = Configuration_js_1.Configuration.create('cancel', { handler: { macro: ['cancel'] } });\n//# sourceMappingURL=CancelConfiguration.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CasesConfiguration = exports.CasesMethods = exports.CasesTags = exports.CasesBeginItem = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar BaseMethods_js_1 = __importDefault(require(\"../base/BaseMethods.js\"));\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar BaseItems_js_1 = require(\"../base/BaseItems.js\");\nvar AmsConfiguration_js_1 = require(\"../ams/AmsConfiguration.js\");\nvar EmpheqUtil_js_1 = require(\"../empheq/EmpheqUtil.js\");\nvar CasesBeginItem = (function (_super) {\n    __extends(CasesBeginItem, _super);\n    function CasesBeginItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(CasesBeginItem.prototype, \"kind\", {\n        get: function () {\n            return 'cases-begin';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    CasesBeginItem.prototype.checkItem = function (item) {\n        if (item.isKind('end') && item.getName() === this.getName()) {\n            if (this.getProperty('end')) {\n                this.setProperty('end', false);\n                return [[], true];\n            }\n        }\n        return _super.prototype.checkItem.call(this, item);\n    };\n    return CasesBeginItem;\n}(BaseItems_js_1.BeginItem));\nexports.CasesBeginItem = CasesBeginItem;\nvar CasesTags = (function (_super) {\n    __extends(CasesTags, _super);\n    function CasesTags() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.subcounter = 0;\n        return _this;\n    }\n    CasesTags.prototype.start = function (env, taggable, defaultTags) {\n        this.subcounter = 0;\n        _super.prototype.start.call(this, env, taggable, defaultTags);\n    };\n    CasesTags.prototype.autoTag = function () {\n        if (this.currentTag.tag != null)\n            return;\n        if (this.currentTag.env === 'subnumcases') {\n            if (this.subcounter === 0)\n                this.counter++;\n            this.subcounter++;\n            this.tag(this.formatNumber(this.counter, this.subcounter), false);\n        }\n        else {\n            if (this.subcounter === 0 || this.currentTag.env !== 'numcases-left')\n                this.counter++;\n            this.tag(this.formatNumber(this.counter), false);\n        }\n    };\n    CasesTags.prototype.formatNumber = function (n, m) {\n        if (m === void 0) { m = null; }\n        return n.toString() + (m === null ? '' : String.fromCharCode(0x60 + m));\n    };\n    return CasesTags;\n}(AmsConfiguration_js_1.AmsTags));\nexports.CasesTags = CasesTags;\nexports.CasesMethods = {\n    NumCases: function (parser, begin) {\n        if (parser.stack.env.closing === begin.getName()) {\n            delete parser.stack.env.closing;\n            parser.Push(parser.itemFactory.create('end').setProperty('name', begin.getName()));\n            var cases = parser.stack.Top();\n            var table = cases.Last;\n            var original = ParseUtil_js_1.default.copyNode(table, parser);\n            var left = cases.getProperty('left');\n            EmpheqUtil_js_1.EmpheqUtil.left(table, original, left + '\\\\empheqlbrace\\\\,', parser, 'numcases-left');\n            parser.Push(parser.itemFactory.create('end').setProperty('name', begin.getName()));\n            return null;\n        }\n        else {\n            var left = parser.GetArgument('\\\\begin{' + begin.getName() + '}');\n            begin.setProperty('left', left);\n            var array = BaseMethods_js_1.default.EqnArray(parser, begin, true, true, 'll');\n            array.arraydef.displaystyle = false;\n            array.arraydef.rowspacing = '.2em';\n            array.setProperty('numCases', true);\n            parser.Push(begin);\n            return array;\n        }\n    },\n    Entry: function (parser, name) {\n        if (!parser.stack.Top().getProperty('numCases')) {\n            return BaseMethods_js_1.default.Entry(parser, name);\n        }\n        parser.Push(parser.itemFactory.create('cell').setProperties({ isEntry: true, name: name }));\n        var tex = parser.string;\n        var braces = 0, i = parser.i, m = tex.length;\n        while (i < m) {\n            var c = tex.charAt(i);\n            if (c === '{') {\n                braces++;\n                i++;\n            }\n            else if (c === '}') {\n                if (braces === 0) {\n                    break;\n                }\n                else {\n                    braces--;\n                    i++;\n                }\n            }\n            else if (c === '&' && braces === 0) {\n                throw new TexError_js_1.default('ExtraCasesAlignTab', 'Extra alignment tab in text for numcase environment');\n            }\n            else if (c === '\\\\' && braces === 0) {\n                var cs = (tex.slice(i + 1).match(/^[a-z]+|./i) || [])[0];\n                if (cs === '\\\\' || cs === 'cr' || cs === 'end' || cs === 'label') {\n                    break;\n                }\n                else {\n                    i += cs.length;\n                }\n            }\n            else {\n                i++;\n            }\n        }\n        var text = tex.substr(parser.i, i - parser.i).replace(/^\\s*/, '');\n        parser.PushAll(ParseUtil_js_1.default.internalMath(parser, text, 0));\n        parser.i = i;\n    }\n};\nnew SymbolMap_js_1.EnvironmentMap('cases-env', EmpheqUtil_js_1.EmpheqUtil.environment, {\n    numcases: ['NumCases', 'cases'],\n    subnumcases: ['NumCases', 'cases']\n}, exports.CasesMethods);\nnew SymbolMap_js_1.MacroMap('cases-macros', {\n    '&': 'Entry'\n}, exports.CasesMethods);\nexports.CasesConfiguration = Configuration_js_1.Configuration.create('cases', {\n    handler: {\n        environment: ['cases-env'],\n        character: ['cases-macros']\n    },\n    items: (_a = {},\n        _a[CasesBeginItem.prototype.kind] = CasesBeginItem,\n        _a),\n    tags: { 'cases': CasesTags }\n});\n//# sourceMappingURL=CasesConfiguration.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CenternotConfiguration = exports.filterCenterOver = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar TexParser_js_1 = __importDefault(require(\"../TexParser.js\"));\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar BaseMethods_js_1 = __importDefault(require(\"../base/BaseMethods.js\"));\nnew SymbolMap_js_1.CommandMap('centernot', {\n    centerOver: 'CenterOver',\n    centernot: ['Macro', '\\\\centerOver{#1}{{\\u29F8}}', 1]\n}, {\n    CenterOver: function (parser, name) {\n        var arg = '{' + parser.GetArgument(name) + '}';\n        var over = parser.ParseArg(name);\n        var base = new TexParser_js_1.default(arg, parser.stack.env, parser.configuration).mml();\n        var mml = parser.create('node', 'TeXAtom', [\n            new TexParser_js_1.default(arg, parser.stack.env, parser.configuration).mml(),\n            parser.create('node', 'mpadded', [\n                parser.create('node', 'mpadded', [over], { width: 0, lspace: '-.5width' }),\n                parser.create('node', 'mphantom', [base])\n            ], { width: 0, lspace: '-.5width' })\n        ]);\n        parser.configuration.addNode('centerOver', base);\n        parser.Push(mml);\n    },\n    Macro: BaseMethods_js_1.default.Macro\n});\nfunction filterCenterOver(_a) {\n    var e_1, _b;\n    var data = _a.data;\n    try {\n        for (var _c = __values(data.getList('centerOver')), _d = _c.next(); !_d.done; _d = _c.next()) {\n            var base = _d.value;\n            var texClass = NodeUtil_js_1.default.getTexClass(base.childNodes[0].childNodes[0]);\n            if (texClass !== null) {\n                NodeUtil_js_1.default.setProperties(base.parent.parent.parent.parent.parent.parent, { texClass: texClass });\n            }\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (_d && !_d.done && (_b = _c.return)) _b.call(_c);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n}\nexports.filterCenterOver = filterCenterOver;\nexports.CenternotConfiguration = Configuration_js_1.Configuration.create('centernot', {\n    handler: { macro: ['centernot'] },\n    postprocessors: [filterCenterOver]\n});\n//# sourceMappingURL=CenternotConfiguration.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ColorConfiguration = void 0;\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar ColorMethods_js_1 = require(\"./ColorMethods.js\");\nvar ColorUtil_js_1 = require(\"./ColorUtil.js\");\nnew SymbolMap_js_1.CommandMap('color', {\n    color: 'Color',\n    textcolor: 'TextColor',\n    definecolor: 'DefineColor',\n    colorbox: 'ColorBox',\n    fcolorbox: 'FColorBox'\n}, ColorMethods_js_1.ColorMethods);\nvar config = function (_config, jax) {\n    jax.parseOptions.packageData.set('color', { model: new ColorUtil_js_1.ColorModel() });\n};\nexports.ColorConfiguration = Configuration_js_1.Configuration.create('color', {\n    handler: {\n        macro: ['color'],\n    },\n    options: {\n        color: {\n            padding: '5px',\n            borderWidth: '2px'\n        }\n    },\n    config: config\n});\n//# sourceMappingURL=ColorConfiguration.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.COLORS = void 0;\nexports.COLORS = new Map([\n    ['Apricot', '#FBB982'],\n    ['Aquamarine', '#00B5BE'],\n    ['Bittersweet', '#C04F17'],\n    ['Black', '#221E1F'],\n    ['Blue', '#2D2F92'],\n    ['BlueGreen', '#00B3B8'],\n    ['BlueViolet', '#473992'],\n    ['BrickRed', '#B6321C'],\n    ['Brown', '#792500'],\n    ['BurntOrange', '#F7921D'],\n    ['CadetBlue', '#74729A'],\n    ['CarnationPink', '#F282B4'],\n    ['Cerulean', '#00A2E3'],\n    ['CornflowerBlue', '#41B0E4'],\n    ['Cyan', '#00AEEF'],\n    ['Dandelion', '#FDBC42'],\n    ['DarkOrchid', '#A4538A'],\n    ['Emerald', '#00A99D'],\n    ['ForestGreen', '#009B55'],\n    ['Fuchsia', '#8C368C'],\n    ['Goldenrod', '#FFDF42'],\n    ['Gray', '#949698'],\n    ['Green', '#00A64F'],\n    ['GreenYellow', '#DFE674'],\n    ['JungleGreen', '#00A99A'],\n    ['Lavender', '#F49EC4'],\n    ['LimeGreen', '#8DC73E'],\n    ['Magenta', '#EC008C'],\n    ['Mahogany', '#A9341F'],\n    ['Maroon', '#AF3235'],\n    ['Melon', '#F89E7B'],\n    ['MidnightBlue', '#006795'],\n    ['Mulberry', '#A93C93'],\n    ['NavyBlue', '#006EB8'],\n    ['OliveGreen', '#3C8031'],\n    ['Orange', '#F58137'],\n    ['OrangeRed', '#ED135A'],\n    ['Orchid', '#AF72B0'],\n    ['Peach', '#F7965A'],\n    ['Periwinkle', '#7977B8'],\n    ['PineGreen', '#008B72'],\n    ['Plum', '#92268F'],\n    ['ProcessBlue', '#00B0F0'],\n    ['Purple', '#99479B'],\n    ['RawSienna', '#974006'],\n    ['Red', '#ED1B23'],\n    ['RedOrange', '#F26035'],\n    ['RedViolet', '#A1246B'],\n    ['Rhodamine', '#EF559F'],\n    ['RoyalBlue', '#0071BC'],\n    ['RoyalPurple', '#613F99'],\n    ['RubineRed', '#ED017D'],\n    ['Salmon', '#F69289'],\n    ['SeaGreen', '#3FBC9D'],\n    ['Sepia', '#671800'],\n    ['SkyBlue', '#46C5DD'],\n    ['SpringGreen', '#C6DC67'],\n    ['Tan', '#DA9D76'],\n    ['TealBlue', '#00AEB3'],\n    ['Thistle', '#D883B7'],\n    ['Turquoise', '#00B4CE'],\n    ['Violet', '#58429B'],\n    ['VioletRed', '#EF58A0'],\n    ['White', '#FFFFFF'],\n    ['WildStrawberry', '#EE2967'],\n    ['Yellow', '#FFF200'],\n    ['YellowGreen', '#98CC70'],\n    ['YellowOrange', '#FAA21A'],\n]);\n//# sourceMappingURL=ColorConstants.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ColorMethods = void 0;\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nfunction padding(colorPadding) {\n    var pad = \"+\".concat(colorPadding);\n    var unit = colorPadding.replace(/^.*?([a-z]*)$/, '$1');\n    var pad2 = 2 * parseFloat(pad);\n    return {\n        width: \"+\".concat(pad2).concat(unit),\n        height: pad,\n        depth: pad,\n        lspace: colorPadding,\n    };\n}\nexports.ColorMethods = {};\nexports.ColorMethods.Color = function (parser, name) {\n    var model = parser.GetBrackets(name, '');\n    var colorDef = parser.GetArgument(name);\n    var colorModel = parser.configuration.packageData.get('color').model;\n    var color = colorModel.getColor(model, colorDef);\n    var style = parser.itemFactory.create('style')\n        .setProperties({ styles: { mathcolor: color } });\n    parser.stack.env['color'] = color;\n    parser.Push(style);\n};\nexports.ColorMethods.TextColor = function (parser, name) {\n    var model = parser.GetBrackets(name, '');\n    var colorDef = parser.GetArgument(name);\n    var colorModel = parser.configuration.packageData.get('color').model;\n    var color = colorModel.getColor(model, colorDef);\n    var old = parser.stack.env['color'];\n    parser.stack.env['color'] = color;\n    var math = parser.ParseArg(name);\n    if (old) {\n        parser.stack.env['color'] = old;\n    }\n    else {\n        delete parser.stack.env['color'];\n    }\n    var node = parser.create('node', 'mstyle', [math], { mathcolor: color });\n    parser.Push(node);\n};\nexports.ColorMethods.DefineColor = function (parser, name) {\n    var cname = parser.GetArgument(name);\n    var model = parser.GetArgument(name);\n    var def = parser.GetArgument(name);\n    var colorModel = parser.configuration.packageData.get('color').model;\n    colorModel.defineColor(model, cname, def);\n};\nexports.ColorMethods.ColorBox = function (parser, name) {\n    var cname = parser.GetArgument(name);\n    var math = ParseUtil_js_1.default.internalMath(parser, parser.GetArgument(name));\n    var colorModel = parser.configuration.packageData.get('color').model;\n    var node = parser.create('node', 'mpadded', math, {\n        mathbackground: colorModel.getColor('named', cname)\n    });\n    NodeUtil_js_1.default.setProperties(node, padding(parser.options.color.padding));\n    parser.Push(node);\n};\nexports.ColorMethods.FColorBox = function (parser, name) {\n    var fname = parser.GetArgument(name);\n    var cname = parser.GetArgument(name);\n    var math = ParseUtil_js_1.default.internalMath(parser, parser.GetArgument(name));\n    var options = parser.options.color;\n    var colorModel = parser.configuration.packageData.get('color').model;\n    var node = parser.create('node', 'mpadded', math, {\n        mathbackground: colorModel.getColor('named', cname),\n        style: \"border: \".concat(options.borderWidth, \" solid \").concat(colorModel.getColor('named', fname))\n    });\n    NodeUtil_js_1.default.setProperties(node, padding(options.padding));\n    parser.Push(node);\n};\n//# sourceMappingURL=ColorMethods.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ColorModel = void 0;\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar ColorConstants_js_1 = require(\"./ColorConstants.js\");\nvar ColorModelProcessors = new Map();\nvar ColorModel = (function () {\n    function ColorModel() {\n        this.userColors = new Map();\n    }\n    ColorModel.prototype.normalizeColor = function (model, def) {\n        if (!model || model === 'named') {\n            return def;\n        }\n        if (ColorModelProcessors.has(model)) {\n            var modelProcessor = ColorModelProcessors.get(model);\n            return modelProcessor(def);\n        }\n        throw new TexError_js_1.default('UndefinedColorModel', 'Color model \\'%1\\' not defined', model);\n    };\n    ColorModel.prototype.getColor = function (model, def) {\n        if (!model || model === 'named') {\n            return this.getColorByName(def);\n        }\n        return this.normalizeColor(model, def);\n    };\n    ColorModel.prototype.getColorByName = function (name) {\n        if (this.userColors.has(name)) {\n            return this.userColors.get(name);\n        }\n        if (ColorConstants_js_1.COLORS.has(name)) {\n            return ColorConstants_js_1.COLORS.get(name);\n        }\n        return name;\n    };\n    ColorModel.prototype.defineColor = function (model, name, def) {\n        var normalized = this.normalizeColor(model, def);\n        this.userColors.set(name, normalized);\n    };\n    return ColorModel;\n}());\nexports.ColorModel = ColorModel;\nColorModelProcessors.set('rgb', function (rgb) {\n    var e_1, _a;\n    var rgbParts = rgb.trim().split(/\\s*,\\s*/);\n    var RGB = '#';\n    if (rgbParts.length !== 3) {\n        throw new TexError_js_1.default('ModelArg1', 'Color values for the %1 model require 3 numbers', 'rgb');\n    }\n    try {\n        for (var rgbParts_1 = __values(rgbParts), rgbParts_1_1 = rgbParts_1.next(); !rgbParts_1_1.done; rgbParts_1_1 = rgbParts_1.next()) {\n            var rgbPart = rgbParts_1_1.value;\n            if (!rgbPart.match(/^(\\d+(\\.\\d*)?|\\.\\d+)$/)) {\n                throw new TexError_js_1.default('InvalidDecimalNumber', 'Invalid decimal number');\n            }\n            var n = parseFloat(rgbPart);\n            if (n < 0 || n > 1) {\n                throw new TexError_js_1.default('ModelArg2', 'Color values for the %1 model must be between %2 and %3', 'rgb', '0', '1');\n            }\n            var pn = Math.floor(n * 255).toString(16);\n            if (pn.length < 2) {\n                pn = '0' + pn;\n            }\n            RGB += pn;\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (rgbParts_1_1 && !rgbParts_1_1.done && (_a = rgbParts_1.return)) _a.call(rgbParts_1);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n    return RGB;\n});\nColorModelProcessors.set('RGB', function (rgb) {\n    var e_2, _a;\n    var rgbParts = rgb.trim().split(/\\s*,\\s*/);\n    var RGB = '#';\n    if (rgbParts.length !== 3) {\n        throw new TexError_js_1.default('ModelArg1', 'Color values for the %1 model require 3 numbers', 'RGB');\n    }\n    try {\n        for (var rgbParts_2 = __values(rgbParts), rgbParts_2_1 = rgbParts_2.next(); !rgbParts_2_1.done; rgbParts_2_1 = rgbParts_2.next()) {\n            var rgbPart = rgbParts_2_1.value;\n            if (!rgbPart.match(/^\\d+$/)) {\n                throw new TexError_js_1.default('InvalidNumber', 'Invalid number');\n            }\n            var n = parseInt(rgbPart);\n            if (n > 255) {\n                throw new TexError_js_1.default('ModelArg2', 'Color values for the %1 model must be between %2 and %3', 'RGB', '0', '255');\n            }\n            var pn = n.toString(16);\n            if (pn.length < 2) {\n                pn = '0' + pn;\n            }\n            RGB += pn;\n        }\n    }\n    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n    finally {\n        try {\n            if (rgbParts_2_1 && !rgbParts_2_1.done && (_a = rgbParts_2.return)) _a.call(rgbParts_2);\n        }\n        finally { if (e_2) throw e_2.error; }\n    }\n    return RGB;\n});\nColorModelProcessors.set('gray', function (gray) {\n    if (!gray.match(/^\\s*(\\d+(\\.\\d*)?|\\.\\d+)\\s*$/)) {\n        throw new TexError_js_1.default('InvalidDecimalNumber', 'Invalid decimal number');\n    }\n    var n = parseFloat(gray);\n    if (n < 0 || n > 1) {\n        throw new TexError_js_1.default('ModelArg2', 'Color values for the %1 model must be between %2 and %3', 'gray', '0', '1');\n    }\n    var pn = Math.floor(n * 255).toString(16);\n    if (pn.length < 2) {\n        pn = '0' + pn;\n    }\n    return \"#\".concat(pn).concat(pn).concat(pn);\n});\n//# sourceMappingURL=ColorUtil.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ColortblConfiguration = exports.ColorArrayItem = void 0;\nvar BaseItems_js_1 = require(\"../base/BaseItems.js\");\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar ColorArrayItem = (function (_super) {\n    __extends(ColorArrayItem, _super);\n    function ColorArrayItem() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.color = {\n            cell: '',\n            row: '',\n            col: []\n        };\n        _this.hasColor = false;\n        return _this;\n    }\n    ColorArrayItem.prototype.EndEntry = function () {\n        _super.prototype.EndEntry.call(this);\n        var cell = this.row[this.row.length - 1];\n        var color = this.color.cell || this.color.row || this.color.col[this.row.length - 1];\n        if (color) {\n            cell.attributes.set('mathbackground', color);\n            this.color.cell = '';\n            this.hasColor = true;\n        }\n    };\n    ColorArrayItem.prototype.EndRow = function () {\n        _super.prototype.EndRow.call(this);\n        this.color.row = '';\n    };\n    ColorArrayItem.prototype.createMml = function () {\n        var mml = _super.prototype.createMml.call(this);\n        var table = (mml.isKind('mrow') ? mml.childNodes[1] : mml);\n        if (table.isKind('menclose')) {\n            table = table.childNodes[0].childNodes[0];\n        }\n        if (this.hasColor && table.attributes.get('frame') === 'none') {\n            table.attributes.set('frame', '');\n        }\n        return mml;\n    };\n    return ColorArrayItem;\n}(BaseItems_js_1.ArrayItem));\nexports.ColorArrayItem = ColorArrayItem;\nnew SymbolMap_js_1.CommandMap('colortbl', {\n    cellcolor: ['TableColor', 'cell'],\n    rowcolor: ['TableColor', 'row'],\n    columncolor: ['TableColor', 'col']\n}, {\n    TableColor: function (parser, name, type) {\n        var lookup = parser.configuration.packageData.get('color').model;\n        var model = parser.GetBrackets(name, '');\n        var color = lookup.getColor(model, parser.GetArgument(name));\n        var top = parser.stack.Top();\n        if (!(top instanceof ColorArrayItem)) {\n            throw new TexError_js_1.default('UnsupportedTableColor', 'Unsupported use of %1', parser.currentCS);\n        }\n        if (type === 'col') {\n            if (top.table.length) {\n                throw new TexError_js_1.default('ColumnColorNotTop', '%1 must be in the top row', name);\n            }\n            top.color.col[top.row.length] = color;\n            if (parser.GetBrackets(name, '')) {\n                parser.GetBrackets(name, '');\n            }\n        }\n        else {\n            top.color[type] = color;\n            if (type === 'row' && (top.Size() || top.row.length)) {\n                throw new TexError_js_1.default('RowColorNotFirst', '%1 must be at the beginning of a row', name);\n            }\n        }\n    }\n});\nvar config = function (config, jax) {\n    if (!jax.parseOptions.packageData.has('color')) {\n        Configuration_js_1.ConfigurationHandler.get('color').config(config, jax);\n    }\n};\nexports.ColortblConfiguration = Configuration_js_1.Configuration.create('colortbl', {\n    handler: { macro: ['colortbl'] },\n    items: { 'array': ColorArrayItem },\n    priority: 10,\n    config: [config, 10]\n});\n//# sourceMappingURL=ColortblConfiguration.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ColorConfiguration = exports.ColorV2Methods = void 0;\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar Configuration_js_1 = require(\"../Configuration.js\");\nexports.ColorV2Methods = {\n    Color: function (parser, name) {\n        var color = parser.GetArgument(name);\n        var old = parser.stack.env['color'];\n        parser.stack.env['color'] = color;\n        var math = parser.ParseArg(name);\n        if (old) {\n            parser.stack.env['color'] = old;\n        }\n        else {\n            delete parser.stack.env['color'];\n        }\n        var node = parser.create('node', 'mstyle', [math], { mathcolor: color });\n        parser.Push(node);\n    }\n};\nnew SymbolMap_js_1.CommandMap('colorv2', { color: 'Color' }, exports.ColorV2Methods);\nexports.ColorConfiguration = Configuration_js_1.Configuration.create('colorv2', { handler: { macro: ['colorv2'] } });\n//# sourceMappingURL=ColorV2Configuration.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ConfigMacrosConfiguration = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar Options_js_1 = require(\"../../../util/Options.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar ParseMethods_js_1 = __importDefault(require(\"../ParseMethods.js\"));\nvar Symbol_js_1 = require(\"../Symbol.js\");\nvar NewcommandMethods_js_1 = __importDefault(require(\"../newcommand/NewcommandMethods.js\"));\nvar NewcommandItems_js_1 = require(\"../newcommand/NewcommandItems.js\");\nvar MACROSMAP = 'configmacros-map';\nvar ENVIRONMENTMAP = 'configmacros-env-map';\nfunction configmacrosInit(config) {\n    new SymbolMap_js_1.CommandMap(MACROSMAP, {}, {});\n    new SymbolMap_js_1.EnvironmentMap(ENVIRONMENTMAP, ParseMethods_js_1.default.environment, {}, {});\n    config.append(Configuration_js_1.Configuration.local({\n        handler: {\n            macro: [MACROSMAP],\n            environment: [ENVIRONMENTMAP]\n        },\n        priority: 3\n    }));\n}\nfunction configmacrosConfig(_config, jax) {\n    configMacros(jax);\n    configEnvironments(jax);\n}\nfunction configMacros(jax) {\n    var e_1, _a;\n    var handler = jax.parseOptions.handlers.retrieve(MACROSMAP);\n    var macros = jax.parseOptions.options.macros;\n    try {\n        for (var _b = __values(Object.keys(macros)), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var cs = _c.value;\n            var def = (typeof macros[cs] === 'string' ? [macros[cs]] : macros[cs]);\n            var macro = Array.isArray(def[2]) ?\n                new Symbol_js_1.Macro(cs, NewcommandMethods_js_1.default.MacroWithTemplate, def.slice(0, 2).concat(def[2])) :\n                new Symbol_js_1.Macro(cs, NewcommandMethods_js_1.default.Macro, def);\n            handler.add(cs, macro);\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n}\nfunction configEnvironments(jax) {\n    var e_2, _a;\n    var handler = jax.parseOptions.handlers.retrieve(ENVIRONMENTMAP);\n    var environments = jax.parseOptions.options.environments;\n    try {\n        for (var _b = __values(Object.keys(environments)), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var env = _c.value;\n            handler.add(env, new Symbol_js_1.Macro(env, NewcommandMethods_js_1.default.BeginEnv, [true].concat(environments[env])));\n        }\n    }\n    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_2) throw e_2.error; }\n    }\n}\nexports.ConfigMacrosConfiguration = Configuration_js_1.Configuration.create('configmacros', {\n    init: configmacrosInit,\n    config: configmacrosConfig,\n    items: (_a = {},\n        _a[NewcommandItems_js_1.BeginEnvItem.prototype.kind] = NewcommandItems_js_1.BeginEnvItem,\n        _a),\n    options: {\n        macros: (0, Options_js_1.expandable)({}),\n        environments: (0, Options_js_1.expandable)({})\n    }\n});\n//# sourceMappingURL=ConfigMacrosConfiguration.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.EmpheqConfiguration = exports.EmpheqMethods = exports.EmpheqBeginItem = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar BaseItems_js_1 = require(\"../base/BaseItems.js\");\nvar EmpheqUtil_js_1 = require(\"./EmpheqUtil.js\");\nvar EmpheqBeginItem = (function (_super) {\n    __extends(EmpheqBeginItem, _super);\n    function EmpheqBeginItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(EmpheqBeginItem.prototype, \"kind\", {\n        get: function () {\n            return 'empheq-begin';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    EmpheqBeginItem.prototype.checkItem = function (item) {\n        if (item.isKind('end') && item.getName() === this.getName()) {\n            this.setProperty('end', false);\n        }\n        return _super.prototype.checkItem.call(this, item);\n    };\n    return EmpheqBeginItem;\n}(BaseItems_js_1.BeginItem));\nexports.EmpheqBeginItem = EmpheqBeginItem;\nexports.EmpheqMethods = {\n    Empheq: function (parser, begin) {\n        if (parser.stack.env.closing === begin.getName()) {\n            delete parser.stack.env.closing;\n            parser.Push(parser.itemFactory.create('end').setProperty('name', parser.stack.global.empheq));\n            parser.stack.global.empheq = '';\n            var empheq = parser.stack.Top();\n            EmpheqUtil_js_1.EmpheqUtil.adjustTable(empheq, parser);\n            parser.Push(parser.itemFactory.create('end').setProperty('name', 'empheq'));\n        }\n        else {\n            ParseUtil_js_1.default.checkEqnEnv(parser);\n            delete parser.stack.global.eqnenv;\n            var opts = parser.GetBrackets('\\\\begin{' + begin.getName() + '}') || '';\n            var _a = __read((parser.GetArgument('\\\\begin{' + begin.getName() + '}') || '').split(/=/), 2), env = _a[0], n = _a[1];\n            if (!EmpheqUtil_js_1.EmpheqUtil.checkEnv(env)) {\n                throw new TexError_js_1.default('UnknownEnv', 'Unknown environment \"%1\"', env);\n            }\n            if (opts) {\n                begin.setProperties(EmpheqUtil_js_1.EmpheqUtil.splitOptions(opts, { left: 1, right: 1 }));\n            }\n            parser.stack.global.empheq = env;\n            parser.string = '\\\\begin{' + env + '}' + (n ? '{' + n + '}' : '') + parser.string.slice(parser.i);\n            parser.i = 0;\n            parser.Push(begin);\n        }\n    },\n    EmpheqMO: function (parser, _name, c) {\n        parser.Push(parser.create('token', 'mo', {}, c));\n    },\n    EmpheqDelim: function (parser, name) {\n        var c = parser.GetDelimiter(name);\n        parser.Push(parser.create('token', 'mo', { stretchy: true, symmetric: true }, c));\n    }\n};\nnew SymbolMap_js_1.EnvironmentMap('empheq-env', EmpheqUtil_js_1.EmpheqUtil.environment, {\n    empheq: ['Empheq', 'empheq'],\n}, exports.EmpheqMethods);\nnew SymbolMap_js_1.CommandMap('empheq-macros', {\n    empheqlbrace: ['EmpheqMO', '{'],\n    empheqrbrace: ['EmpheqMO', '}'],\n    empheqlbrack: ['EmpheqMO', '['],\n    empheqrbrack: ['EmpheqMO', ']'],\n    empheqlangle: ['EmpheqMO', '\\u27E8'],\n    empheqrangle: ['EmpheqMO', '\\u27E9'],\n    empheqlparen: ['EmpheqMO', '('],\n    empheqrparen: ['EmpheqMO', ')'],\n    empheqlvert: ['EmpheqMO', '|'],\n    empheqrvert: ['EmpheqMO', '|'],\n    empheqlVert: ['EmpheqMO', '\\u2016'],\n    empheqrVert: ['EmpheqMO', '\\u2016'],\n    empheqlfloor: ['EmpheqMO', '\\u230A'],\n    empheqrfloor: ['EmpheqMO', '\\u230B'],\n    empheqlceil: ['EmpheqMO', '\\u2308'],\n    empheqrceil: ['EmpheqMO', '\\u2309'],\n    empheqbiglbrace: ['EmpheqMO', '{'],\n    empheqbigrbrace: ['EmpheqMO', '}'],\n    empheqbiglbrack: ['EmpheqMO', '['],\n    empheqbigrbrack: ['EmpheqMO', ']'],\n    empheqbiglangle: ['EmpheqMO', '\\u27E8'],\n    empheqbigrangle: ['EmpheqMO', '\\u27E9'],\n    empheqbiglparen: ['EmpheqMO', '('],\n    empheqbigrparen: ['EmpheqMO', ')'],\n    empheqbiglvert: ['EmpheqMO', '|'],\n    empheqbigrvert: ['EmpheqMO', '|'],\n    empheqbiglVert: ['EmpheqMO', '\\u2016'],\n    empheqbigrVert: ['EmpheqMO', '\\u2016'],\n    empheqbiglfloor: ['EmpheqMO', '\\u230A'],\n    empheqbigrfloor: ['EmpheqMO', '\\u230B'],\n    empheqbiglceil: ['EmpheqMO', '\\u2308'],\n    empheqbigrceil: ['EmpheqMO', '\\u2309'],\n    empheql: 'EmpheqDelim',\n    empheqr: 'EmpheqDelim',\n    empheqbigl: 'EmpheqDelim',\n    empheqbigr: 'EmpheqDelim'\n}, exports.EmpheqMethods);\nexports.EmpheqConfiguration = Configuration_js_1.Configuration.create('empheq', {\n    handler: {\n        macro: ['empheq-macros'],\n        environment: ['empheq-env'],\n    },\n    items: (_a = {},\n        _a[EmpheqBeginItem.prototype.kind] = EmpheqBeginItem,\n        _a)\n});\n//# sourceMappingURL=EmpheqConfiguration.js.map", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.EmpheqUtil = void 0;\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar TexParser_js_1 = __importDefault(require(\"../TexParser.js\"));\nexports.EmpheqUtil = {\n    environment: function (parser, env, func, args) {\n        var name = args[0];\n        var item = parser.itemFactory.create(name + '-begin').setProperties({ name: env, end: name });\n        parser.Push(func.apply(void 0, __spreadArray([parser, item], __read(args.slice(1)), false)));\n    },\n    splitOptions: function (text, allowed) {\n        if (allowed === void 0) { allowed = null; }\n        return ParseUtil_js_1.default.keyvalOptions(text, allowed, true);\n    },\n    columnCount: function (table) {\n        var e_1, _a;\n        var m = 0;\n        try {\n            for (var _b = __values(table.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var row = _c.value;\n                var n = row.childNodes.length - (row.isKind('mlabeledtr') ? 1 : 0);\n                if (n > m)\n                    m = n;\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return m;\n    },\n    cellBlock: function (tex, table, parser, env) {\n        var e_2, _a;\n        var mpadded = parser.create('node', 'mpadded', [], { height: 0, depth: 0, voffset: '-1height' });\n        var result = new TexParser_js_1.default(tex, parser.stack.env, parser.configuration);\n        var mml = result.mml();\n        if (env && result.configuration.tags.label) {\n            result.configuration.tags.currentTag.env = env;\n            result.configuration.tags.getTag(true);\n        }\n        try {\n            for (var _b = __values((mml.isInferred ? mml.childNodes : [mml])), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                mpadded.appendChild(child);\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        mpadded.appendChild(parser.create('node', 'mphantom', [\n            parser.create('node', 'mpadded', [table], { width: 0 })\n        ]));\n        return mpadded;\n    },\n    topRowTable: function (original, parser) {\n        var table = ParseUtil_js_1.default.copyNode(original, parser);\n        table.setChildren(table.childNodes.slice(0, 1));\n        table.attributes.set('align', 'baseline 1');\n        return original.factory.create('mphantom', {}, [parser.create('node', 'mpadded', [table], { width: 0 })]);\n    },\n    rowspanCell: function (mtd, tex, table, parser, env) {\n        mtd.appendChild(parser.create('node', 'mpadded', [\n            this.cellBlock(tex, ParseUtil_js_1.default.copyNode(table, parser), parser, env),\n            this.topRowTable(table, parser)\n        ], { height: 0, depth: 0, voffset: 'height' }));\n    },\n    left: function (table, original, left, parser, env) {\n        var e_3, _a;\n        if (env === void 0) { env = ''; }\n        table.attributes.set('columnalign', 'right ' + (table.attributes.get('columnalign') || ''));\n        table.attributes.set('columnspacing', '0em ' + (table.attributes.get('columnspacing') || ''));\n        var mtd;\n        try {\n            for (var _b = __values(table.childNodes.slice(0).reverse()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var row = _c.value;\n                mtd = parser.create('node', 'mtd');\n                row.childNodes.unshift(mtd);\n                mtd.parent = row;\n                if (row.isKind('mlabeledtr')) {\n                    row.childNodes[0] = row.childNodes[1];\n                    row.childNodes[1] = mtd;\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        this.rowspanCell(mtd, left, original, parser, env);\n    },\n    right: function (table, original, right, parser, env) {\n        if (env === void 0) { env = ''; }\n        if (table.childNodes.length === 0) {\n            table.appendChild(parser.create('node', 'mtr'));\n        }\n        var m = exports.EmpheqUtil.columnCount(table);\n        var row = table.childNodes[0];\n        while (row.childNodes.length < m)\n            row.appendChild(parser.create('node', 'mtd'));\n        var mtd = row.appendChild(parser.create('node', 'mtd'));\n        exports.EmpheqUtil.rowspanCell(mtd, right, original, parser, env);\n        table.attributes.set('columnalign', (table.attributes.get('columnalign') || '').split(/ /).slice(0, m).join(' ') + ' left');\n        table.attributes.set('columnspacing', (table.attributes.get('columnspacing') || '').split(/ /).slice(0, m - 1).join(' ') + ' 0em');\n    },\n    adjustTable: function (empheq, parser) {\n        var left = empheq.getProperty('left');\n        var right = empheq.getProperty('right');\n        if (left || right) {\n            var table = empheq.Last;\n            var original = ParseUtil_js_1.default.copyNode(table, parser);\n            if (left)\n                this.left(table, original, left, parser);\n            if (right)\n                this.right(table, original, right, parser);\n        }\n    },\n    allowEnv: {\n        equation: true,\n        align: true,\n        gather: true,\n        flalign: true,\n        alignat: true,\n        multline: true\n    },\n    checkEnv: function (env) {\n        return this.allowEnv.hasOwnProperty(env.replace(/\\*$/, '')) || false;\n    }\n};\n//# sourceMappingURL=EmpheqUtil.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.EncloseConfiguration = exports.EncloseMethods = exports.ENCLOSE_OPTIONS = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nexports.ENCLOSE_OPTIONS = {\n    'data-arrowhead': 1,\n    color: 1,\n    mathcolor: 1,\n    background: 1,\n    mathbackground: 1,\n    'data-padding': 1,\n    'data-thickness': 1\n};\nexports.EncloseMethods = {};\nexports.EncloseMethods.Enclose = function (parser, name) {\n    var notation = parser.GetArgument(name).replace(/,/g, ' ');\n    var attr = parser.GetBrackets(name, '');\n    var math = parser.ParseArg(name);\n    var def = ParseUtil_js_1.default.keyvalOptions(attr, exports.ENCLOSE_OPTIONS);\n    def.notation = notation;\n    parser.Push(parser.create('node', 'menclose', [math], def));\n};\nnew SymbolMap_js_1.CommandMap('enclose', { enclose: 'Enclose' }, exports.EncloseMethods);\nexports.EncloseConfiguration = Configuration_js_1.Configuration.create('enclose', { handler: { macro: ['enclose'] } });\n//# sourceMappingURL=EncloseConfiguration.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ExtpfeilConfiguration = exports.ExtpfeilMethods = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar AmsMethods_js_1 = require(\"../ams/AmsMethods.js\");\nvar NewcommandUtil_js_1 = __importDefault(require(\"../newcommand/NewcommandUtil.js\"));\nvar NewcommandConfiguration_js_1 = require(\"../newcommand/NewcommandConfiguration.js\");\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nexports.ExtpfeilMethods = {};\nexports.ExtpfeilMethods.xArrow = AmsMethods_js_1.AmsMethods.xArrow;\nexports.ExtpfeilMethods.NewExtArrow = function (parser, name) {\n    var cs = parser.GetArgument(name);\n    var space = parser.GetArgument(name);\n    var chr = parser.GetArgument(name);\n    if (!cs.match(/^\\\\([a-z]+|.)$/i)) {\n        throw new TexError_js_1.default('NewextarrowArg1', 'First argument to %1 must be a control sequence name', name);\n    }\n    if (!space.match(/^(\\d+),(\\d+)$/)) {\n        throw new TexError_js_1.default('NewextarrowArg2', 'Second argument to %1 must be two integers separated by a comma', name);\n    }\n    if (!chr.match(/^(\\d+|0x[0-9A-F]+)$/i)) {\n        throw new TexError_js_1.default('NewextarrowArg3', 'Third argument to %1 must be a unicode character number', name);\n    }\n    cs = cs.substr(1);\n    var spaces = space.split(',');\n    NewcommandUtil_js_1.default.addMacro(parser, cs, exports.ExtpfeilMethods.xArrow, [parseInt(chr), parseInt(spaces[0]), parseInt(spaces[1])]);\n};\nnew SymbolMap_js_1.CommandMap('extpfeil', {\n    xtwoheadrightarrow: ['xArrow', 0x21A0, 12, 16],\n    xtwoheadleftarrow: ['xArrow', 0x219E, 17, 13],\n    xmapsto: ['xArrow', 0x21A6, 6, 7],\n    xlongequal: ['xArrow', 0x003D, 7, 7],\n    xtofrom: ['xArrow', 0x21C4, 12, 12],\n    Newextarrow: 'NewExtArrow'\n}, exports.ExtpfeilMethods);\nvar init = function (config) {\n    NewcommandConfiguration_js_1.NewcommandConfiguration.init(config);\n};\nexports.ExtpfeilConfiguration = Configuration_js_1.Configuration.create('extpfeil', {\n    handler: { macro: ['extpfeil'] },\n    init: init\n});\n//# sourceMappingURL=ExtpfeilConfiguration.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.GensymbConfiguration = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar TexConstants_js_1 = require(\"../TexConstants.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nfunction mathcharUnit(parser, mchar) {\n    var def = mchar.attributes || {};\n    def.mathvariant = TexConstants_js_1.TexConstant.Variant.NORMAL;\n    def.class = 'MathML-Unit';\n    var node = parser.create('token', 'mi', def, mchar.char);\n    parser.Push(node);\n}\nnew SymbolMap_js_1.CharacterMap('gensymb-symbols', mathcharUnit, {\n    ohm: '\\u2126',\n    degree: '\\u00B0',\n    celsius: '\\u2103',\n    perthousand: '\\u2030',\n    micro: '\\u00B5'\n});\nexports.GensymbConfiguration = Configuration_js_1.Configuration.create('gensymb', {\n    handler: { macro: ['gensymb-symbols'] },\n});\n//# sourceMappingURL=GensymbConfiguration.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.HtmlConfiguration = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar HtmlMethods_js_1 = __importDefault(require(\"./HtmlMethods.js\"));\nnew SymbolMap_js_1.CommandMap('html_macros', {\n    href: 'Href',\n    'class': 'Class',\n    style: 'Style',\n    cssId: 'Id'\n}, HtmlMethods_js_1.default);\nexports.HtmlConfiguration = Configuration_js_1.Configuration.create('html', { handler: { macro: ['html_macros'] } });\n//# sourceMappingURL=HtmlConfiguration.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar HtmlMethods = {};\nHtmlMethods.Href = function (parser, name) {\n    var url = parser.GetArgument(name);\n    var arg = GetArgumentMML(parser, name);\n    NodeUtil_js_1.default.setAttribute(arg, 'href', url);\n    parser.Push(arg);\n};\nHtmlMethods.Class = function (parser, name) {\n    var CLASS = parser.GetArgument(name);\n    var arg = GetArgumentMML(parser, name);\n    var oldClass = NodeUtil_js_1.default.getAttribute(arg, 'class');\n    if (oldClass) {\n        CLASS = oldClass + ' ' + CLASS;\n    }\n    NodeUtil_js_1.default.setAttribute(arg, 'class', CLASS);\n    parser.Push(arg);\n};\nHtmlMethods.Style = function (parser, name) {\n    var style = parser.GetArgument(name);\n    var arg = GetArgumentMML(parser, name);\n    var oldStyle = NodeUtil_js_1.default.getAttribute(arg, 'style');\n    if (oldStyle) {\n        if (style.charAt(style.length - 1) !== ';') {\n            style += ';';\n        }\n        style = oldStyle + ' ' + style;\n    }\n    NodeUtil_js_1.default.setAttribute(arg, 'style', style);\n    parser.Push(arg);\n};\nHtmlMethods.Id = function (parser, name) {\n    var ID = parser.GetArgument(name);\n    var arg = GetArgumentMML(parser, name);\n    NodeUtil_js_1.default.setAttribute(arg, 'id', ID);\n    parser.Push(arg);\n};\nvar GetArgumentMML = function (parser, name) {\n    var arg = parser.ParseArg(name);\n    if (!NodeUtil_js_1.default.isInferred(arg)) {\n        return arg;\n    }\n    var children = NodeUtil_js_1.default.getChildren(arg);\n    if (children.length === 1) {\n        return children[0];\n    }\n    var mrow = parser.create('node', 'mrow');\n    NodeUtil_js_1.default.copyChildren(arg, mrow);\n    NodeUtil_js_1.default.copyAttributes(arg, mrow);\n    return mrow;\n};\nexports.default = HtmlMethods;\n//# sourceMappingURL=HtmlMethods.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MathtoolsConfiguration = exports.fixPrescripts = exports.PAIREDDELIMS = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar Options_js_1 = require(\"../../../util/Options.js\");\nrequire(\"./MathtoolsMappings.js\");\nvar MathtoolsUtil_js_1 = require(\"./MathtoolsUtil.js\");\nvar MathtoolsTags_js_1 = require(\"./MathtoolsTags.js\");\nvar MathtoolsItems_js_1 = require(\"./MathtoolsItems.js\");\nexports.PAIREDDELIMS = 'mathtools-paired-delims';\nfunction initMathtools(config) {\n    new SymbolMap_js_1.CommandMap(exports.PAIREDDELIMS, {}, {});\n    config.append(Configuration_js_1.Configuration.local({ handler: { macro: [exports.PAIREDDELIMS] }, priority: -5 }));\n}\nfunction configMathtools(config, jax) {\n    var e_1, _a;\n    var parser = jax.parseOptions;\n    var pairedDelims = parser.options.mathtools.pairedDelimiters;\n    try {\n        for (var _b = __values(Object.keys(pairedDelims)), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var cs = _c.value;\n            MathtoolsUtil_js_1.MathtoolsUtil.addPairedDelims(parser, cs, pairedDelims[cs]);\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n    (0, MathtoolsTags_js_1.MathtoolsTagFormat)(config, jax);\n}\nfunction fixPrescripts(_a) {\n    var e_2, _b, e_3, _c, e_4, _d;\n    var data = _a.data;\n    try {\n        for (var _e = __values(data.getList('mmultiscripts')), _f = _e.next(); !_f.done; _f = _e.next()) {\n            var node = _f.value;\n            if (!node.getProperty('fixPrescript'))\n                continue;\n            var childNodes = NodeUtil_js_1.default.getChildren(node);\n            var n = 0;\n            try {\n                for (var _g = (e_3 = void 0, __values([1, 2])), _h = _g.next(); !_h.done; _h = _g.next()) {\n                    var i = _h.value;\n                    if (!childNodes[i]) {\n                        NodeUtil_js_1.default.setChild(node, i, data.nodeFactory.create('node', 'none'));\n                        n++;\n                    }\n                }\n            }\n            catch (e_3_1) { e_3 = { error: e_3_1 }; }\n            finally {\n                try {\n                    if (_h && !_h.done && (_c = _g.return)) _c.call(_g);\n                }\n                finally { if (e_3) throw e_3.error; }\n            }\n            try {\n                for (var _j = (e_4 = void 0, __values([4, 5])), _k = _j.next(); !_k.done; _k = _j.next()) {\n                    var i = _k.value;\n                    if (NodeUtil_js_1.default.isType(childNodes[i], 'mrow') && NodeUtil_js_1.default.getChildren(childNodes[i]).length === 0) {\n                        NodeUtil_js_1.default.setChild(node, i, data.nodeFactory.create('node', 'none'));\n                    }\n                }\n            }\n            catch (e_4_1) { e_4 = { error: e_4_1 }; }\n            finally {\n                try {\n                    if (_k && !_k.done && (_d = _j.return)) _d.call(_j);\n                }\n                finally { if (e_4) throw e_4.error; }\n            }\n            if (n === 2) {\n                childNodes.splice(1, 2);\n            }\n        }\n    }\n    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n    finally {\n        try {\n            if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n        }\n        finally { if (e_2) throw e_2.error; }\n    }\n}\nexports.fixPrescripts = fixPrescripts;\nexports.MathtoolsConfiguration = Configuration_js_1.Configuration.create('mathtools', {\n    handler: {\n        macro: ['mathtools-macros', 'mathtools-delimiters'],\n        environment: ['mathtools-environments'],\n        delimiter: ['mathtools-delimiters'],\n        character: ['mathtools-characters']\n    },\n    items: (_a = {},\n        _a[MathtoolsItems_js_1.MultlinedItem.prototype.kind] = MathtoolsItems_js_1.MultlinedItem,\n        _a),\n    init: initMathtools,\n    config: configMathtools,\n    postprocessors: [[fixPrescripts, -6]],\n    options: {\n        mathtools: {\n            'multlinegap': '1em',\n            'multlined-pos': 'c',\n            'firstline-afterskip': '',\n            'lastline-preskip': '',\n            'smallmatrix-align': 'c',\n            'shortvdotsadjustabove': '.2em',\n            'shortvdotsadjustbelow': '.2em',\n            'centercolon': false,\n            'centercolon-offset': '.04em',\n            'thincolon-dx': '-.04em',\n            'thincolon-dw': '-.08em',\n            'use-unicode': false,\n            'prescript-sub-format': '',\n            'prescript-sup-format': '',\n            'prescript-arg-format': '',\n            'allow-mathtoolsset': true,\n            pairedDelimiters: (0, Options_js_1.expandable)({}),\n            tagforms: (0, Options_js_1.expandable)({}),\n        }\n    }\n});\n//# sourceMappingURL=MathtoolsConfiguration.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MultlinedItem = void 0;\nvar AmsItems_js_1 = require(\"../ams/AmsItems.js\");\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar TexConstants_js_1 = require(\"../TexConstants.js\");\nvar MultlinedItem = (function (_super) {\n    __extends(MultlinedItem, _super);\n    function MultlinedItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MultlinedItem.prototype, \"kind\", {\n        get: function () {\n            return 'multlined';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MultlinedItem.prototype.EndTable = function () {\n        if (this.Size() || this.row.length) {\n            this.EndEntry();\n            this.EndRow();\n        }\n        if (this.table.length > 1) {\n            var options = this.factory.configuration.options.mathtools;\n            var gap = options.multlinegap;\n            var firstskip = options['firstline-afterskip'] || gap;\n            var lastskip = options['lastline-preskip'] || gap;\n            var first = NodeUtil_js_1.default.getChildren(this.table[0])[0];\n            if (NodeUtil_js_1.default.getAttribute(first, 'columnalign') !== TexConstants_js_1.TexConstant.Align.RIGHT) {\n                first.appendChild(this.create('node', 'mspace', [], { width: firstskip }));\n            }\n            var last = NodeUtil_js_1.default.getChildren(this.table[this.table.length - 1])[0];\n            if (NodeUtil_js_1.default.getAttribute(last, 'columnalign') !== TexConstants_js_1.TexConstant.Align.LEFT) {\n                var top_1 = NodeUtil_js_1.default.getChildren(last)[0];\n                top_1.childNodes.unshift(null);\n                var space = this.create('node', 'mspace', [], { width: lastskip });\n                NodeUtil_js_1.default.setChild(top_1, 0, space);\n            }\n        }\n        _super.prototype.EndTable.call(this);\n    };\n    return MultlinedItem;\n}(AmsItems_js_1.MultlineItem));\nexports.MultlinedItem = MultlinedItem;\n//# sourceMappingURL=MathtoolsItems.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar ParseMethods_js_1 = __importDefault(require(\"../ParseMethods.js\"));\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar TexConstants_js_1 = require(\"../TexConstants.js\");\nvar MathtoolsMethods_js_1 = require(\"./MathtoolsMethods.js\");\nnew SymbolMap_js_1.CommandMap('mathtools-macros', {\n    shoveleft: ['HandleShove', TexConstants_js_1.TexConstant.Align.LEFT],\n    shoveright: ['HandleShove', TexConstants_js_1.TexConstant.Align.RIGHT],\n    xleftrightarrow: ['xArrow', 0x2194, 10, 10],\n    xLeftarrow: ['xArrow', 0x21D0, 12, 7],\n    xRightarrow: ['xArrow', 0x21D2, 7, 12],\n    xLeftrightarrow: ['xArrow', 0x21D4, 12, 12],\n    xhookleftarrow: ['xArrow', 0x21A9, 10, 5],\n    xhookrightarrow: ['xArrow', 0x21AA, 5, 10],\n    xmapsto: ['xArrow', 0x21A6, 10, 10],\n    xrightharpoondown: ['xArrow', 0x21C1, 5, 10],\n    xleftharpoondown: ['xArrow', 0x21BD, 10, 5],\n    xrightleftharpoons: ['xArrow', 0x21CC, 10, 10],\n    xrightharpoonup: ['xArrow', 0x21C0, 5, 10],\n    xleftharpoonup: ['xArrow', 0x21BC, 10, 5],\n    xleftrightharpoons: ['xArrow', 0x21CB, 10, 10],\n    mathllap: ['MathLap', 'l', false],\n    mathrlap: ['MathLap', 'r', false],\n    mathclap: ['MathLap', 'c', false],\n    clap: ['MtLap', 'c'],\n    textllap: ['MtLap', 'l'],\n    textrlap: ['MtLap', 'r'],\n    textclap: ['MtLap', 'c'],\n    cramped: 'Cramped',\n    crampedllap: ['MathLap', 'l', true],\n    crampedrlap: ['MathLap', 'r', true],\n    crampedclap: ['MathLap', 'c', true],\n    crampedsubstack: ['Macro', '\\\\begin{crampedsubarray}{c}#1\\\\end{crampedsubarray}', 1],\n    mathmbox: 'MathMBox',\n    mathmakebox: 'MathMakeBox',\n    overbracket: 'UnderOverBracket',\n    underbracket: 'UnderOverBracket',\n    refeq: 'HandleRef',\n    MoveEqLeft: ['Macro', '\\\\hspace{#1em}&\\\\hspace{-#1em}', 1, '2'],\n    Aboxed: 'Aboxed',\n    ArrowBetweenLines: 'ArrowBetweenLines',\n    vdotswithin: 'VDotsWithin',\n    shortvdotswithin: 'ShortVDotsWithin',\n    MTFlushSpaceAbove: 'FlushSpaceAbove',\n    MTFlushSpaceBelow: 'FlushSpaceBelow',\n    DeclarePairedDelimiter: 'DeclarePairedDelimiter',\n    DeclarePairedDelimiterX: 'DeclarePairedDelimiterX',\n    DeclarePairedDelimiterXPP: 'DeclarePairedDelimiterXPP',\n    DeclarePairedDelimiters: 'DeclarePairedDelimiter',\n    DeclarePairedDelimitersX: 'DeclarePairedDelimiterX',\n    DeclarePairedDelimitersXPP: 'DeclarePairedDelimiterXPP',\n    centercolon: ['CenterColon', true, true],\n    ordinarycolon: ['CenterColon', false],\n    MTThinColon: ['CenterColon', true, true, true],\n    coloneqq: ['Relation', ':=', '\\u2254'],\n    Coloneqq: ['Relation', '::=', '\\u2A74'],\n    coloneq: ['Relation', ':-'],\n    Coloneq: ['Relation', '::-'],\n    eqqcolon: ['Relation', '=:', '\\u2255'],\n    Eqqcolon: ['Relation', '=::'],\n    eqcolon: ['Relation', '-:', '\\u2239'],\n    Eqcolon: ['Relation', '-::'],\n    colonapprox: ['Relation', ':\\\\approx'],\n    Colonapprox: ['Relation', '::\\\\approx'],\n    colonsim: ['Relation', ':\\\\sim'],\n    Colonsim: ['Relation', '::\\\\sim'],\n    dblcolon: ['Relation', '::', '\\u2237'],\n    nuparrow: ['NArrow', '\\u2191', '.06em'],\n    ndownarrow: ['NArrow', '\\u2193', '.25em'],\n    bigtimes: ['Macro', '\\\\mathop{\\\\Large\\\\kern-.1em\\\\boldsymbol{\\\\times}\\\\kern-.1em}'],\n    splitfrac: ['SplitFrac', false],\n    splitdfrac: ['SplitFrac', true],\n    xmathstrut: 'XMathStrut',\n    prescript: 'Prescript',\n    newtagform: ['NewTagForm', false],\n    renewtagform: ['NewTagForm', true],\n    usetagform: 'UseTagForm',\n    adjustlimits: [\n        'MacroWithTemplate',\n        '\\\\mathop{{#1}\\\\vphantom{{#3}}}_{{#2}\\\\vphantom{{#4}}}\\\\mathop{{#3}\\\\vphantom{{#1}}}_{{#4}\\\\vphantom{{#2}}}',\n        4, , '_', , '_'\n    ],\n    mathtoolsset: 'SetOptions'\n}, MathtoolsMethods_js_1.MathtoolsMethods);\nnew SymbolMap_js_1.EnvironmentMap('mathtools-environments', ParseMethods_js_1.default.environment, {\n    dcases: ['Array', null, '\\\\{', '', 'll', null, '.2em', 'D'],\n    rcases: ['Array', null, '', '\\\\}', 'll', null, '.2em'],\n    drcases: ['Array', null, '', '\\\\}', 'll', null, '.2em', 'D'],\n    'dcases*': ['Cases', null, '{', '', 'D'],\n    'rcases*': ['Cases', null, '', '}'],\n    'drcases*': ['Cases', null, '', '}', 'D'],\n    'cases*': ['Cases', null, '{', ''],\n    'matrix*': ['MtMatrix', null, null, null],\n    'pmatrix*': ['MtMatrix', null, '(', ')'],\n    'bmatrix*': ['MtMatrix', null, '[', ']'],\n    'Bmatrix*': ['MtMatrix', null, '\\\\{', '\\\\}'],\n    'vmatrix*': ['MtMatrix', null, '\\\\vert', '\\\\vert'],\n    'Vmatrix*': ['MtMatrix', null, '\\\\Vert', '\\\\Vert'],\n    'smallmatrix*': ['MtSmallMatrix', null, null, null],\n    psmallmatrix: ['MtSmallMatrix', null, '(', ')', 'c'],\n    'psmallmatrix*': ['MtSmallMatrix', null, '(', ')'],\n    bsmallmatrix: ['MtSmallMatrix', null, '[', ']', 'c'],\n    'bsmallmatrix*': ['MtSmallMatrix', null, '[', ']'],\n    Bsmallmatrix: ['MtSmallMatrix', null, '\\\\{', '\\\\}', 'c'],\n    'Bsmallmatrix*': ['MtSmallMatrix', null, '\\\\{', '\\\\}'],\n    vsmallmatrix: ['MtSmallMatrix', null, '\\\\vert', '\\\\vert', 'c'],\n    'vsmallmatrix*': ['MtSmallMatrix', null, '\\\\vert', '\\\\vert'],\n    Vsmallmatrix: ['MtSmallMatrix', null, '\\\\Vert', '\\\\Vert', 'c'],\n    'Vsmallmatrix*': ['MtSmallMatrix', null, '\\\\Vert', '\\\\Vert'],\n    crampedsubarray: ['Array', null, null, null, null, '0em', '0.1em', 'S\\'', 1],\n    multlined: 'MtMultlined',\n    spreadlines: ['SpreadLines', true],\n    lgathered: ['AmsEqnArray', null, null, null, 'l', null, '.5em', 'D'],\n    rgathered: ['AmsEqnArray', null, null, null, 'r', null, '.5em', 'D'],\n}, MathtoolsMethods_js_1.MathtoolsMethods);\nnew SymbolMap_js_1.DelimiterMap('mathtools-delimiters', ParseMethods_js_1.default.delimiter, {\n    '\\\\lparen': '(',\n    '\\\\rparen': ')'\n});\nnew SymbolMap_js_1.CommandMap('mathtools-characters', {\n    ':': ['CenterColon', true]\n}, MathtoolsMethods_js_1.MathtoolsMethods);\n//# sourceMappingURL=MathtoolsMappings.js.map", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MathtoolsMethods = void 0;\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar AmsMethods_js_1 = require(\"../ams/AmsMethods.js\");\nvar BaseMethods_js_1 = __importDefault(require(\"../base/BaseMethods.js\"));\nvar TexParser_js_1 = __importDefault(require(\"../TexParser.js\"));\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nvar lengths_js_1 = require(\"../../../util/lengths.js\");\nvar Options_js_1 = require(\"../../../util/Options.js\");\nvar NewcommandUtil_js_1 = __importDefault(require(\"../newcommand/NewcommandUtil.js\"));\nvar NewcommandMethods_js_1 = __importDefault(require(\"../newcommand/NewcommandMethods.js\"));\nvar MathtoolsUtil_js_1 = require(\"./MathtoolsUtil.js\");\nexports.MathtoolsMethods = {\n    MtMatrix: function (parser, begin, open, close) {\n        var align = parser.GetBrackets(\"\\\\begin{\".concat(begin.getName(), \"}\"), 'c');\n        return exports.MathtoolsMethods.Array(parser, begin, open, close, align);\n    },\n    MtSmallMatrix: function (parser, begin, open, close, align) {\n        if (!align) {\n            align = parser.GetBrackets(\"\\\\begin{\".concat(begin.getName(), \"}\"), parser.options.mathtools['smallmatrix-align']);\n        }\n        return exports.MathtoolsMethods.Array(parser, begin, open, close, align, ParseUtil_js_1.default.Em(1 / 3), '.2em', 'S', 1);\n    },\n    MtMultlined: function (parser, begin) {\n        var _a;\n        var name = \"\\\\begin{\".concat(begin.getName(), \"}\");\n        var pos = parser.GetBrackets(name, parser.options.mathtools['multlined-pos'] || 'c');\n        var width = pos ? parser.GetBrackets(name, '') : '';\n        if (pos && !pos.match(/^[cbt]$/)) {\n            _a = __read([pos, width], 2), width = _a[0], pos = _a[1];\n        }\n        parser.Push(begin);\n        var item = parser.itemFactory.create('multlined', parser, begin);\n        item.arraydef = {\n            displaystyle: true,\n            rowspacing: '.5em',\n            width: width || 'auto',\n            columnwidth: '100%',\n        };\n        return ParseUtil_js_1.default.setArrayAlign(item, pos || 'c');\n    },\n    HandleShove: function (parser, name, shove) {\n        var top = parser.stack.Top();\n        if (top.kind !== 'multline' && top.kind !== 'multlined') {\n            throw new TexError_js_1.default('CommandInMultlined', '%1 can only appear within the multline or multlined environments', name);\n        }\n        if (top.Size()) {\n            throw new TexError_js_1.default('CommandAtTheBeginingOfLine', '%1 must come at the beginning of the line', name);\n        }\n        top.setProperty('shove', shove);\n        var shift = parser.GetBrackets(name);\n        var mml = parser.ParseArg(name);\n        if (shift) {\n            var mrow = parser.create('node', 'mrow', []);\n            var mspace = parser.create('node', 'mspace', [], { width: shift });\n            if (shove === 'left') {\n                mrow.appendChild(mspace);\n                mrow.appendChild(mml);\n            }\n            else {\n                mrow.appendChild(mml);\n                mrow.appendChild(mspace);\n            }\n            mml = mrow;\n        }\n        parser.Push(mml);\n    },\n    SpreadLines: function (parser, begin) {\n        var e_1, _a;\n        if (parser.stack.env.closing === begin.getName()) {\n            delete parser.stack.env.closing;\n            var top_1 = parser.stack.Pop();\n            var mml = top_1.toMml();\n            var spread = top_1.getProperty('spread');\n            if (mml.isInferred) {\n                try {\n                    for (var _b = __values(NodeUtil_js_1.default.getChildren(mml)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                        var child = _c.value;\n                        MathtoolsUtil_js_1.MathtoolsUtil.spreadLines(child, spread);\n                    }\n                }\n                catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                finally {\n                    try {\n                        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                    }\n                    finally { if (e_1) throw e_1.error; }\n                }\n            }\n            else {\n                MathtoolsUtil_js_1.MathtoolsUtil.spreadLines(mml, spread);\n            }\n            parser.Push(mml);\n        }\n        else {\n            var spread = parser.GetDimen(\"\\\\begin{\".concat(begin.getName(), \"}\"));\n            begin.setProperty('spread', spread);\n            parser.Push(begin);\n        }\n    },\n    Cases: function (parser, begin, open, close, style) {\n        var array = parser.itemFactory.create('array').setProperty('casesEnv', begin.getName());\n        array.arraydef = {\n            rowspacing: '.2em',\n            columnspacing: '1em',\n            columnalign: 'left'\n        };\n        if (style === 'D') {\n            array.arraydef.displaystyle = true;\n        }\n        array.setProperties({ open: open, close: close });\n        parser.Push(begin);\n        return array;\n    },\n    MathLap: function (parser, name, pos, cramped) {\n        var style = parser.GetBrackets(name, '').trim();\n        var mml = parser.create('node', 'mstyle', [\n            parser.create('node', 'mpadded', [parser.ParseArg(name)], __assign({ width: 0 }, (pos === 'r' ? {} : { lspace: (pos === 'l' ? '-1width' : '-.5width') })))\n        ], { 'data-cramped': cramped });\n        MathtoolsUtil_js_1.MathtoolsUtil.setDisplayLevel(mml, style);\n        parser.Push(parser.create('node', 'TeXAtom', [mml]));\n    },\n    Cramped: function (parser, name) {\n        var style = parser.GetBrackets(name, '').trim();\n        var arg = parser.ParseArg(name);\n        var mml = parser.create('node', 'mstyle', [arg], { 'data-cramped': true });\n        MathtoolsUtil_js_1.MathtoolsUtil.setDisplayLevel(mml, style);\n        parser.Push(mml);\n    },\n    MtLap: function (parser, name, pos) {\n        var content = ParseUtil_js_1.default.internalMath(parser, parser.GetArgument(name), 0);\n        var mml = parser.create('node', 'mpadded', content, { width: 0 });\n        if (pos !== 'r') {\n            NodeUtil_js_1.default.setAttribute(mml, 'lspace', pos === 'l' ? '-1width' : '-.5width');\n        }\n        parser.Push(mml);\n    },\n    MathMakeBox: function (parser, name) {\n        var width = parser.GetBrackets(name);\n        var pos = parser.GetBrackets(name, 'c');\n        var mml = parser.create('node', 'mpadded', [parser.ParseArg(name)]);\n        if (width) {\n            NodeUtil_js_1.default.setAttribute(mml, 'width', width);\n        }\n        var align = (0, Options_js_1.lookup)(pos, { c: 'center', r: 'right' }, '');\n        if (align) {\n            NodeUtil_js_1.default.setAttribute(mml, 'data-align', align);\n        }\n        parser.Push(mml);\n    },\n    MathMBox: function (parser, name) {\n        parser.Push(parser.create('node', 'mrow', [parser.ParseArg(name)]));\n    },\n    UnderOverBracket: function (parser, name) {\n        var thickness = (0, lengths_js_1.length2em)(parser.GetBrackets(name, '.1em'), .1);\n        var height = parser.GetBrackets(name, '.2em');\n        var arg = parser.GetArgument(name);\n        var _a = __read((name.charAt(1) === 'o' ?\n            ['over', 'accent', 'bottom'] :\n            ['under', 'accentunder', 'top']), 3), pos = _a[0], accent = _a[1], border = _a[2];\n        var t = (0, lengths_js_1.em)(thickness);\n        var base = new TexParser_js_1.default(arg, parser.stack.env, parser.configuration).mml();\n        var copy = new TexParser_js_1.default(arg, parser.stack.env, parser.configuration).mml();\n        var script = parser.create('node', 'mpadded', [\n            parser.create('node', 'mphantom', [copy])\n        ], {\n            style: \"border: \".concat(t, \" solid; border-\").concat(border, \": none\"),\n            height: height,\n            depth: 0\n        });\n        var node = ParseUtil_js_1.default.underOver(parser, base, script, pos, true);\n        var munderover = NodeUtil_js_1.default.getChildAt(NodeUtil_js_1.default.getChildAt(node, 0), 0);\n        NodeUtil_js_1.default.setAttribute(munderover, accent, true);\n        parser.Push(node);\n    },\n    Aboxed: function (parser, name) {\n        var top = MathtoolsUtil_js_1.MathtoolsUtil.checkAlignment(parser, name);\n        if (top.row.length % 2 === 1) {\n            top.row.push(parser.create('node', 'mtd', []));\n        }\n        var arg = parser.GetArgument(name);\n        var rest = parser.string.substr(parser.i);\n        parser.string = arg + '&&\\\\endAboxed';\n        parser.i = 0;\n        var left = parser.GetUpTo(name, '&');\n        var right = parser.GetUpTo(name, '&');\n        parser.GetUpTo(name, '\\\\endAboxed');\n        var tex = ParseUtil_js_1.default.substituteArgs(parser, [left, right], '\\\\rlap{\\\\boxed{#1{}#2}}\\\\kern.267em\\\\phantom{#1}&\\\\phantom{{}#2}\\\\kern.267em');\n        parser.string = tex + rest;\n        parser.i = 0;\n    },\n    ArrowBetweenLines: function (parser, name) {\n        var top = MathtoolsUtil_js_1.MathtoolsUtil.checkAlignment(parser, name);\n        if (top.Size() || top.row.length) {\n            throw new TexError_js_1.default('BetweenLines', '%1 must be on a row by itself', name);\n        }\n        var star = parser.GetStar();\n        var symbol = parser.GetBrackets(name, '\\\\Updownarrow');\n        if (star) {\n            top.EndEntry();\n            top.EndEntry();\n        }\n        var tex = (star ? '\\\\quad' + symbol : symbol + '\\\\quad');\n        var mml = new TexParser_js_1.default(tex, parser.stack.env, parser.configuration).mml();\n        parser.Push(mml);\n        top.EndEntry();\n        top.EndRow();\n    },\n    VDotsWithin: function (parser, name) {\n        var top = parser.stack.Top();\n        var isFlush = (top.getProperty('flushspaceabove') === top.table.length);\n        var arg = '\\\\mmlToken{mi}{}' + parser.GetArgument(name) + '\\\\mmlToken{mi}{}';\n        var base = new TexParser_js_1.default(arg, parser.stack.env, parser.configuration).mml();\n        var mml = parser.create('node', 'mpadded', [\n            parser.create('node', 'mpadded', [\n                parser.create('node', 'mo', [\n                    parser.create('text', '\\u22EE')\n                ])\n            ], __assign({ width: 0, lspace: '-.5width' }, (isFlush ? { height: '-.6em', voffset: '-.18em' } : {}))),\n            parser.create('node', 'mphantom', [base])\n        ], {\n            lspace: '.5width'\n        });\n        parser.Push(mml);\n    },\n    ShortVDotsWithin: function (parser, _name) {\n        var top = parser.stack.Top();\n        var star = parser.GetStar();\n        exports.MathtoolsMethods.FlushSpaceAbove(parser, '\\\\MTFlushSpaceAbove');\n        !star && top.EndEntry();\n        exports.MathtoolsMethods.VDotsWithin(parser, '\\\\vdotswithin');\n        star && top.EndEntry();\n        exports.MathtoolsMethods.FlushSpaceBelow(parser, '\\\\MTFlushSpaceBelow');\n    },\n    FlushSpaceAbove: function (parser, name) {\n        var top = MathtoolsUtil_js_1.MathtoolsUtil.checkAlignment(parser, name);\n        top.setProperty('flushspaceabove', top.table.length);\n        top.addRowSpacing('-' + parser.options.mathtools['shortvdotsadjustabove']);\n    },\n    FlushSpaceBelow: function (parser, name) {\n        var top = MathtoolsUtil_js_1.MathtoolsUtil.checkAlignment(parser, name);\n        top.Size() && top.EndEntry();\n        top.EndRow();\n        top.addRowSpacing('-' + parser.options.mathtools['shortvdotsadjustbelow']);\n    },\n    PairedDelimiters: function (parser, name, open, close, body, n, pre, post) {\n        if (body === void 0) { body = '#1'; }\n        if (n === void 0) { n = 1; }\n        if (pre === void 0) { pre = ''; }\n        if (post === void 0) { post = ''; }\n        var star = parser.GetStar();\n        var size = (star ? '' : parser.GetBrackets(name));\n        var _a = __read((star ? ['\\\\left', '\\\\right'] : size ? [size + 'l', size + 'r'] : ['', '']), 2), left = _a[0], right = _a[1];\n        var delim = (star ? '\\\\middle' : size || '');\n        if (n) {\n            var args = [];\n            for (var i = args.length; i < n; i++) {\n                args.push(parser.GetArgument(name));\n            }\n            pre = ParseUtil_js_1.default.substituteArgs(parser, args, pre);\n            body = ParseUtil_js_1.default.substituteArgs(parser, args, body);\n            post = ParseUtil_js_1.default.substituteArgs(parser, args, post);\n        }\n        body = body.replace(/\\\\delimsize/g, delim);\n        parser.string = [pre, left, open, body, right, close, post, parser.string.substr(parser.i)]\n            .reduce(function (s, part) { return ParseUtil_js_1.default.addArgs(parser, s, part); }, '');\n        parser.i = 0;\n        ParseUtil_js_1.default.checkMaxMacros(parser);\n    },\n    DeclarePairedDelimiter: function (parser, name) {\n        var cs = NewcommandUtil_js_1.default.GetCsNameArgument(parser, name);\n        var open = parser.GetArgument(name);\n        var close = parser.GetArgument(name);\n        MathtoolsUtil_js_1.MathtoolsUtil.addPairedDelims(parser.configuration, cs, [open, close]);\n    },\n    DeclarePairedDelimiterX: function (parser, name) {\n        var cs = NewcommandUtil_js_1.default.GetCsNameArgument(parser, name);\n        var n = NewcommandUtil_js_1.default.GetArgCount(parser, name);\n        var open = parser.GetArgument(name);\n        var close = parser.GetArgument(name);\n        var body = parser.GetArgument(name);\n        MathtoolsUtil_js_1.MathtoolsUtil.addPairedDelims(parser.configuration, cs, [open, close, body, n]);\n    },\n    DeclarePairedDelimiterXPP: function (parser, name) {\n        var cs = NewcommandUtil_js_1.default.GetCsNameArgument(parser, name);\n        var n = NewcommandUtil_js_1.default.GetArgCount(parser, name);\n        var pre = parser.GetArgument(name);\n        var open = parser.GetArgument(name);\n        var close = parser.GetArgument(name);\n        var post = parser.GetArgument(name);\n        var body = parser.GetArgument(name);\n        MathtoolsUtil_js_1.MathtoolsUtil.addPairedDelims(parser.configuration, cs, [open, close, body, n, pre, post]);\n    },\n    CenterColon: function (parser, _name, center, force, thin) {\n        if (force === void 0) { force = false; }\n        if (thin === void 0) { thin = false; }\n        var options = parser.options.mathtools;\n        var mml = parser.create('token', 'mo', {}, ':');\n        if (center && (options['centercolon'] || force)) {\n            var dy = options['centercolon-offset'];\n            mml = parser.create('node', 'mpadded', [mml], __assign({ voffset: dy, height: \"+\".concat(dy), depth: \"-\".concat(dy) }, (thin ? { width: options['thincolon-dw'], lspace: options['thincolon-dx'] } : {})));\n        }\n        parser.Push(mml);\n    },\n    Relation: function (parser, _name, tex, unicode) {\n        var options = parser.options.mathtools;\n        if (options['use-unicode'] && unicode) {\n            parser.Push(parser.create('token', 'mo', { texClass: MmlNode_js_1.TEXCLASS.REL }, unicode));\n        }\n        else {\n            tex = '\\\\mathrel{' + tex.replace(/:/g, '\\\\MTThinColon').replace(/-/g, '\\\\mathrel{-}') + '}';\n            parser.string = ParseUtil_js_1.default.addArgs(parser, tex, parser.string.substr(parser.i));\n            parser.i = 0;\n        }\n    },\n    NArrow: function (parser, _name, c, dy) {\n        parser.Push(parser.create('node', 'TeXAtom', [\n            parser.create('token', 'mtext', {}, c),\n            parser.create('node', 'mpadded', [\n                parser.create('node', 'mpadded', [\n                    parser.create('node', 'menclose', [\n                        parser.create('node', 'mspace', [], { height: '.2em', depth: 0, width: '.4em' })\n                    ], { notation: 'updiagonalstrike', 'data-thickness': '.05em', 'data-padding': 0 })\n                ], { width: 0, lspace: '-.5width', voffset: dy }),\n                parser.create('node', 'mphantom', [\n                    parser.create('token', 'mtext', {}, c)\n                ])\n            ], { width: 0, lspace: '-.5width' })\n        ], { texClass: MmlNode_js_1.TEXCLASS.REL }));\n    },\n    SplitFrac: function (parser, name, display) {\n        var num = parser.ParseArg(name);\n        var den = parser.ParseArg(name);\n        parser.Push(parser.create('node', 'mstyle', [\n            parser.create('node', 'mfrac', [\n                parser.create('node', 'mstyle', [\n                    num,\n                    parser.create('token', 'mi'),\n                    parser.create('token', 'mspace', { width: '1em' })\n                ], { scriptlevel: 0 }),\n                parser.create('node', 'mstyle', [\n                    parser.create('token', 'mspace', { width: '1em' }),\n                    parser.create('token', 'mi'),\n                    den\n                ], { scriptlevel: 0 })\n            ], { linethickness: 0, numalign: 'left', denomalign: 'right' })\n        ], { displaystyle: display, scriptlevel: 0 }));\n    },\n    XMathStrut: function (parser, name) {\n        var dd = parser.GetBrackets(name);\n        var dh = parser.GetArgument(name);\n        dh = MathtoolsUtil_js_1.MathtoolsUtil.plusOrMinus(name, dh);\n        dd = MathtoolsUtil_js_1.MathtoolsUtil.plusOrMinus(name, dd || dh);\n        parser.Push(parser.create('node', 'TeXAtom', [\n            parser.create('node', 'mpadded', [\n                parser.create('node', 'mphantom', [\n                    parser.create('token', 'mo', { stretchy: false }, '(')\n                ])\n            ], { width: 0, height: dh + 'height', depth: dd + 'depth' })\n        ], { texClass: MmlNode_js_1.TEXCLASS.ORD }));\n    },\n    Prescript: function (parser, name) {\n        var sup = MathtoolsUtil_js_1.MathtoolsUtil.getScript(parser, name, 'sup');\n        var sub = MathtoolsUtil_js_1.MathtoolsUtil.getScript(parser, name, 'sub');\n        var base = MathtoolsUtil_js_1.MathtoolsUtil.getScript(parser, name, 'arg');\n        if (NodeUtil_js_1.default.isType(sup, 'none') && NodeUtil_js_1.default.isType(sub, 'none')) {\n            parser.Push(base);\n            return;\n        }\n        var mml = parser.create('node', 'mmultiscripts', [base]);\n        NodeUtil_js_1.default.getChildren(mml).push(null, null);\n        NodeUtil_js_1.default.appendChildren(mml, [parser.create('node', 'mprescripts'), sub, sup]);\n        mml.setProperty('fixPrescript', true);\n        parser.Push(mml);\n    },\n    NewTagForm: function (parser, name, renew) {\n        if (renew === void 0) { renew = false; }\n        var tags = parser.tags;\n        if (!('mtFormats' in tags)) {\n            throw new TexError_js_1.default('TagsNotMT', '%1 can only be used with ams or mathtools tags', name);\n        }\n        var id = parser.GetArgument(name).trim();\n        if (!id) {\n            throw new TexError_js_1.default('InvalidTagFormID', 'Tag form name can\\'t be empty');\n        }\n        var format = parser.GetBrackets(name, '');\n        var left = parser.GetArgument(name);\n        var right = parser.GetArgument(name);\n        if (!renew && tags.mtFormats.has(id)) {\n            throw new TexError_js_1.default('DuplicateTagForm', 'Duplicate tag form: %1', id);\n        }\n        tags.mtFormats.set(id, [left, right, format]);\n    },\n    UseTagForm: function (parser, name) {\n        var tags = parser.tags;\n        if (!('mtFormats' in tags)) {\n            throw new TexError_js_1.default('TagsNotMT', '%1 can only be used with ams or mathtools tags', name);\n        }\n        var id = parser.GetArgument(name).trim();\n        if (!id) {\n            tags.mtCurrent = null;\n            return;\n        }\n        if (!tags.mtFormats.has(id)) {\n            throw new TexError_js_1.default('UndefinedTagForm', 'Undefined tag form: %1', id);\n        }\n        tags.mtCurrent = tags.mtFormats.get(id);\n    },\n    SetOptions: function (parser, name) {\n        var e_2, _a;\n        var options = parser.options.mathtools;\n        if (!options['allow-mathtoolsset']) {\n            throw new TexError_js_1.default('ForbiddenMathtoolsSet', '%1 is disabled', name);\n        }\n        var allowed = {};\n        Object.keys(options).forEach(function (id) {\n            if (id !== 'pariedDelimiters' && id !== 'tagforms' && id !== 'allow-mathtoolsset') {\n                allowed[id] = 1;\n            }\n        });\n        var args = parser.GetArgument(name);\n        var keys = ParseUtil_js_1.default.keyvalOptions(args, allowed, true);\n        try {\n            for (var _b = __values(Object.keys(keys)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var id = _c.value;\n                options[id] = keys[id];\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    },\n    Array: BaseMethods_js_1.default.Array,\n    Macro: BaseMethods_js_1.default.Macro,\n    xArrow: AmsMethods_js_1.AmsMethods.xArrow,\n    HandleRef: AmsMethods_js_1.AmsMethods.HandleRef,\n    AmsEqnArray: AmsMethods_js_1.AmsMethods.AmsEqnArray,\n    MacroWithTemplate: NewcommandMethods_js_1.default.MacroWithTemplate,\n};\n//# sourceMappingURL=MathtoolsMethods.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MathtoolsTagFormat = void 0;\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar Tags_js_1 = require(\"../Tags.js\");\nvar tagID = 0;\nfunction MathtoolsTagFormat(config, jax) {\n    var tags = jax.parseOptions.options.tags;\n    if (tags !== 'base' && config.tags.hasOwnProperty(tags)) {\n        Tags_js_1.TagsFactory.add(tags, config.tags[tags]);\n    }\n    var TagClass = Tags_js_1.TagsFactory.create(jax.parseOptions.options.tags).constructor;\n    var TagFormat = (function (_super) {\n        __extends(TagFormat, _super);\n        function TagFormat() {\n            var e_1, _a;\n            var _this = _super.call(this) || this;\n            _this.mtFormats = new Map();\n            _this.mtCurrent = null;\n            var forms = jax.parseOptions.options.mathtools.tagforms;\n            try {\n                for (var _b = __values(Object.keys(forms)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var form = _c.value;\n                    if (!Array.isArray(forms[form]) || forms[form].length !== 3) {\n                        throw new TexError_js_1.default('InvalidTagFormDef', 'The tag form definition for \"%1\" should be an array fo three strings', form);\n                    }\n                    _this.mtFormats.set(form, forms[form]);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            return _this;\n        }\n        TagFormat.prototype.formatTag = function (tag) {\n            if (this.mtCurrent) {\n                var _a = __read(this.mtCurrent, 3), left = _a[0], right = _a[1], format = _a[2];\n                return (format ? \"\".concat(left).concat(format, \"{\").concat(tag, \"}\").concat(right) : \"\".concat(left).concat(tag).concat(right));\n            }\n            return _super.prototype.formatTag.call(this, tag);\n        };\n        return TagFormat;\n    }(TagClass));\n    tagID++;\n    var tagName = 'MathtoolsTags-' + tagID;\n    Tags_js_1.TagsFactory.add(tagName, TagFormat);\n    jax.parseOptions.options.tags = tagName;\n}\nexports.MathtoolsTagFormat = MathtoolsTagFormat;\n//# sourceMappingURL=MathtoolsTags.js.map", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MathtoolsUtil = void 0;\nvar BaseItems_js_1 = require(\"../base/BaseItems.js\");\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar TexParser_js_1 = __importDefault(require(\"../TexParser.js\"));\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar Symbol_js_1 = require(\"../Symbol.js\");\nvar Options_js_1 = require(\"../../../util/Options.js\");\nvar MathtoolsMethods_js_1 = require(\"./MathtoolsMethods.js\");\nvar MathtoolsConfiguration_js_1 = require(\"./MathtoolsConfiguration.js\");\nexports.MathtoolsUtil = {\n    setDisplayLevel: function (mml, style) {\n        if (!style)\n            return;\n        var _a = __read((0, Options_js_1.lookup)(style, {\n            '\\\\displaystyle': [true, 0],\n            '\\\\textstyle': [false, 0],\n            '\\\\scriptstyle': [false, 1],\n            '\\\\scriptscriptstyle': [false, 2]\n        }, [null, null]), 2), display = _a[0], script = _a[1];\n        if (display !== null) {\n            mml.attributes.set('displaystyle', display);\n            mml.attributes.set('scriptlevel', script);\n        }\n    },\n    checkAlignment: function (parser, name) {\n        var top = parser.stack.Top();\n        if (top.kind !== BaseItems_js_1.EqnArrayItem.prototype.kind) {\n            throw new TexError_js_1.default('NotInAlignment', '%1 can only be used in aligment environments', name);\n        }\n        return top;\n    },\n    addPairedDelims: function (config, cs, args) {\n        var delims = config.handlers.retrieve(MathtoolsConfiguration_js_1.PAIREDDELIMS);\n        delims.add(cs, new Symbol_js_1.Macro(cs, MathtoolsMethods_js_1.MathtoolsMethods.PairedDelimiters, args));\n    },\n    spreadLines: function (mtable, spread) {\n        if (!mtable.isKind('mtable'))\n            return;\n        var rowspacing = mtable.attributes.get('rowspacing');\n        if (rowspacing) {\n            var add_1 = ParseUtil_js_1.default.dimen2em(spread);\n            rowspacing = rowspacing\n                .split(/ /)\n                .map(function (s) { return ParseUtil_js_1.default.Em(Math.max(0, ParseUtil_js_1.default.dimen2em(s) + add_1)); })\n                .join(' ');\n        }\n        else {\n            rowspacing = spread;\n        }\n        mtable.attributes.set('rowspacing', rowspacing);\n    },\n    plusOrMinus: function (name, n) {\n        n = n.trim();\n        if (!n.match(/^[-+]?(?:\\d+(?:\\.\\d*)?|\\.\\d+)$/)) {\n            throw new TexError_js_1.default('NotANumber', 'Argument to %1 is not a number', name);\n        }\n        return (n.match(/^[-+]/) ? n : '+' + n);\n    },\n    getScript: function (parser, name, pos) {\n        var arg = ParseUtil_js_1.default.trimSpaces(parser.GetArgument(name));\n        if (arg === '') {\n            return parser.create('node', 'none');\n        }\n        var format = parser.options.mathtools[\"prescript-\".concat(pos, \"-format\")];\n        format && (arg = \"\".concat(format, \"{\").concat(arg, \"}\"));\n        return new TexParser_js_1.default(arg, parser.stack.env, parser.configuration).mml();\n    }\n};\n//# sourceMappingURL=MathtoolsUtil.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MhchemConfiguration = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar BaseMethods_js_1 = __importDefault(require(\"../base/BaseMethods.js\"));\nvar AmsMethods_js_1 = require(\"../ams/AmsMethods.js\");\nvar mhchemParser_js_1 = require(\"mhchemparser/dist/mhchemParser.js\");\nvar MhchemMethods = {};\nMhchemMethods.Macro = BaseMethods_js_1.default.Macro;\nMhchemMethods.xArrow = AmsMethods_js_1.AmsMethods.xArrow;\nMhchemMethods.Machine = function (parser, name, machine) {\n    var arg = parser.GetArgument(name);\n    var tex;\n    try {\n        tex = mhchemParser_js_1.mhchemParser.toTex(arg, machine);\n    }\n    catch (err) {\n        throw new TexError_js_1.default(err[0], err[1]);\n    }\n    parser.string = tex + parser.string.substr(parser.i);\n    parser.i = 0;\n};\nnew SymbolMap_js_1.CommandMap('mhchem', {\n    ce: ['Machine', 'ce'],\n    pu: ['Machine', 'pu'],\n    longrightleftharpoons: [\n        'Macro',\n        '\\\\stackrel{\\\\textstyle{-}\\\\!\\\\!{\\\\rightharpoonup}}{\\\\smash{{\\\\leftharpoondown}\\\\!\\\\!{-}}}'\n    ],\n    longRightleftharpoons: [\n        'Macro',\n        '\\\\stackrel{\\\\textstyle{-}\\\\!\\\\!{\\\\rightharpoonup}}{\\\\smash{\\\\leftharpoondown}}'\n    ],\n    longLeftrightharpoons: [\n        'Macro',\n        '\\\\stackrel{\\\\textstyle\\\\vphantom{{-}}{\\\\rightharpoonup}}{\\\\smash{{\\\\leftharpoondown}\\\\!\\\\!{-}}}'\n    ],\n    longleftrightarrows: [\n        'Macro',\n        '\\\\stackrel{\\\\longrightarrow}{\\\\smash{\\\\longleftarrow}\\\\Rule{0px}{.25em}{0px}}'\n    ],\n    tripledash: [\n        'Macro',\n        '\\\\vphantom{-}\\\\raise2mu{\\\\kern2mu\\\\tiny\\\\text{-}\\\\kern1mu\\\\text{-}\\\\kern1mu\\\\text{-}\\\\kern2mu}'\n    ],\n    xleftrightarrow: ['xArrow', 0x2194, 6, 6],\n    xrightleftharpoons: ['xArrow', 0x21CC, 5, 7],\n    xRightleftharpoons: ['xArrow', 0x21CC, 5, 7],\n    xLeftrightharpoons: ['xArrow', 0x21CC, 5, 7]\n}, MhchemMethods);\nexports.MhchemConfiguration = Configuration_js_1.Configuration.create('mhchem', { handler: { macro: ['mhchem'] } });\n//# sourceMappingURL=MhchemConfiguration.js.map", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.NewcommandConfiguration = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar NewcommandItems_js_1 = require(\"./NewcommandItems.js\");\nvar NewcommandUtil_js_1 = __importDefault(require(\"./NewcommandUtil.js\"));\nrequire(\"./NewcommandMappings.js\");\nvar ParseMethods_js_1 = __importDefault(require(\"../ParseMethods.js\"));\nvar sm = __importStar(require(\"../SymbolMap.js\"));\nvar init = function (config) {\n    new sm.DelimiterMap(NewcommandUtil_js_1.default.NEW_DELIMITER, ParseMethods_js_1.default.delimiter, {});\n    new sm.CommandMap(NewcommandUtil_js_1.default.NEW_COMMAND, {}, {});\n    new sm.EnvironmentMap(NewcommandUtil_js_1.default.NEW_ENVIRONMENT, ParseMethods_js_1.default.environment, {}, {});\n    config.append(Configuration_js_1.Configuration.local({ handler: { character: [],\n            delimiter: [NewcommandUtil_js_1.default.NEW_DELIMITER],\n            macro: [NewcommandUtil_js_1.default.NEW_DELIMITER,\n                NewcommandUtil_js_1.default.NEW_COMMAND],\n            environment: [NewcommandUtil_js_1.default.NEW_ENVIRONMENT]\n        },\n        priority: -1 }));\n};\nexports.NewcommandConfiguration = Configuration_js_1.Configuration.create('newcommand', {\n    handler: {\n        macro: ['Newcommand-macros']\n    },\n    items: (_a = {},\n        _a[NewcommandItems_js_1.BeginEnvItem.prototype.kind] = NewcommandItems_js_1.BeginEnvItem,\n        _a),\n    options: { maxMacros: 1000 },\n    init: init\n});\n//# sourceMappingURL=NewcommandConfiguration.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BeginEnvItem = void 0;\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar StackItem_js_1 = require(\"../StackItem.js\");\nvar BeginEnvItem = (function (_super) {\n    __extends(BeginEnvItem, _super);\n    function BeginEnvItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(BeginEnvItem.prototype, \"kind\", {\n        get: function () {\n            return 'beginEnv';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BeginEnvItem.prototype, \"isOpen\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    BeginEnvItem.prototype.checkItem = function (item) {\n        if (item.isKind('end')) {\n            if (item.getName() !== this.getName()) {\n                throw new TexError_js_1.default('EnvBadEnd', '\\\\begin{%1} ended with \\\\end{%2}', this.getName(), item.getName());\n            }\n            return [[this.factory.create('mml', this.toMml())], true];\n        }\n        if (item.isKind('stop')) {\n            throw new TexError_js_1.default('EnvMissingEnd', 'Missing \\\\end{%1}', this.getName());\n        }\n        return _super.prototype.checkItem.call(this, item);\n    };\n    return BeginEnvItem;\n}(StackItem_js_1.BaseItem));\nexports.BeginEnvItem = BeginEnvItem;\n//# sourceMappingURL=NewcommandItems.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar NewcommandMethods_js_1 = __importDefault(require(\"./NewcommandMethods.js\"));\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nnew SymbolMap_js_1.CommandMap('Newcommand-macros', {\n    newcommand: 'NewCommand',\n    renewcommand: 'NewCommand',\n    newenvironment: 'NewEnvironment',\n    renewenvironment: 'NewEnvironment',\n    def: 'MacroDef',\n    'let': 'Let'\n}, NewcommandMethods_js_1.default);\n//# sourceMappingURL=NewcommandMappings.js.map", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar sm = __importStar(require(\"../SymbolMap.js\"));\nvar BaseMethods_js_1 = __importDefault(require(\"../base/BaseMethods.js\"));\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar NewcommandUtil_js_1 = __importDefault(require(\"./NewcommandUtil.js\"));\nvar NewcommandMethods = {};\nNewcommandMethods.NewCommand = function (parser, name) {\n    var cs = NewcommandUtil_js_1.default.GetCsNameArgument(parser, name);\n    var n = NewcommandUtil_js_1.default.GetArgCount(parser, name);\n    var opt = parser.GetBrackets(name);\n    var def = parser.GetArgument(name);\n    NewcommandUtil_js_1.default.addMacro(parser, cs, NewcommandMethods.Macro, [def, n, opt]);\n};\nNewcommandMethods.NewEnvironment = function (parser, name) {\n    var env = ParseUtil_js_1.default.trimSpaces(parser.GetArgument(name));\n    var n = NewcommandUtil_js_1.default.GetArgCount(parser, name);\n    var opt = parser.GetBrackets(name);\n    var bdef = parser.GetArgument(name);\n    var edef = parser.GetArgument(name);\n    NewcommandUtil_js_1.default.addEnvironment(parser, env, NewcommandMethods.BeginEnv, [true, bdef, edef, n, opt]);\n};\nNewcommandMethods.MacroDef = function (parser, name) {\n    var cs = NewcommandUtil_js_1.default.GetCSname(parser, name);\n    var params = NewcommandUtil_js_1.default.GetTemplate(parser, name, '\\\\' + cs);\n    var def = parser.GetArgument(name);\n    !(params instanceof Array) ?\n        NewcommandUtil_js_1.default.addMacro(parser, cs, NewcommandMethods.Macro, [def, params]) :\n        NewcommandUtil_js_1.default.addMacro(parser, cs, NewcommandMethods.MacroWithTemplate, [def].concat(params));\n};\nNewcommandMethods.Let = function (parser, name) {\n    var cs = NewcommandUtil_js_1.default.GetCSname(parser, name);\n    var c = parser.GetNext();\n    if (c === '=') {\n        parser.i++;\n        c = parser.GetNext();\n    }\n    var handlers = parser.configuration.handlers;\n    if (c === '\\\\') {\n        name = NewcommandUtil_js_1.default.GetCSname(parser, name);\n        var macro_1 = handlers.get('delimiter').lookup('\\\\' + name);\n        if (macro_1) {\n            NewcommandUtil_js_1.default.addDelimiter(parser, '\\\\' + cs, macro_1.char, macro_1.attributes);\n            return;\n        }\n        var map_1 = handlers.get('macro').applicable(name);\n        if (!map_1) {\n            return;\n        }\n        if (map_1 instanceof sm.MacroMap) {\n            var macro_2 = map_1.lookup(name);\n            NewcommandUtil_js_1.default.addMacro(parser, cs, macro_2.func, macro_2.args, macro_2.symbol);\n            return;\n        }\n        macro_1 = map_1.lookup(name);\n        var newArgs = NewcommandUtil_js_1.default.disassembleSymbol(cs, macro_1);\n        var method = function (p, _cs) {\n            var rest = [];\n            for (var _i = 2; _i < arguments.length; _i++) {\n                rest[_i - 2] = arguments[_i];\n            }\n            var symb = NewcommandUtil_js_1.default.assembleSymbol(rest);\n            return map_1.parser(p, symb);\n        };\n        NewcommandUtil_js_1.default.addMacro(parser, cs, method, newArgs);\n        return;\n    }\n    parser.i++;\n    var macro = handlers.get('delimiter').lookup(c);\n    if (macro) {\n        NewcommandUtil_js_1.default.addDelimiter(parser, '\\\\' + cs, macro.char, macro.attributes);\n        return;\n    }\n    NewcommandUtil_js_1.default.addMacro(parser, cs, NewcommandMethods.Macro, [c]);\n};\nNewcommandMethods.MacroWithTemplate = function (parser, name, text, n) {\n    var params = [];\n    for (var _i = 4; _i < arguments.length; _i++) {\n        params[_i - 4] = arguments[_i];\n    }\n    var argCount = parseInt(n, 10);\n    if (argCount) {\n        var args = [];\n        parser.GetNext();\n        if (params[0] && !NewcommandUtil_js_1.default.MatchParam(parser, params[0])) {\n            throw new TexError_js_1.default('MismatchUseDef', 'Use of %1 doesn\\'t match its definition', name);\n        }\n        for (var i = 0; i < argCount; i++) {\n            args.push(NewcommandUtil_js_1.default.GetParameter(parser, name, params[i + 1]));\n        }\n        text = ParseUtil_js_1.default.substituteArgs(parser, args, text);\n    }\n    parser.string = ParseUtil_js_1.default.addArgs(parser, text, parser.string.slice(parser.i));\n    parser.i = 0;\n    ParseUtil_js_1.default.checkMaxMacros(parser);\n};\nNewcommandMethods.BeginEnv = function (parser, begin, bdef, edef, n, def) {\n    if (begin.getProperty('end') && parser.stack.env['closing'] === begin.getName()) {\n        delete parser.stack.env['closing'];\n        var rest = parser.string.slice(parser.i);\n        parser.string = edef;\n        parser.i = 0;\n        parser.Parse();\n        parser.string = rest;\n        parser.i = 0;\n        return parser.itemFactory.create('end').setProperty('name', begin.getName());\n    }\n    if (n) {\n        var args = [];\n        if (def != null) {\n            var optional = parser.GetBrackets('\\\\begin{' + begin.getName() + '}');\n            args.push(optional == null ? def : optional);\n        }\n        for (var i = args.length; i < n; i++) {\n            args.push(parser.GetArgument('\\\\begin{' + begin.getName() + '}'));\n        }\n        bdef = ParseUtil_js_1.default.substituteArgs(parser, args, bdef);\n        edef = ParseUtil_js_1.default.substituteArgs(parser, [], edef);\n    }\n    parser.string = ParseUtil_js_1.default.addArgs(parser, bdef, parser.string.slice(parser.i));\n    parser.i = 0;\n    return parser.itemFactory.create('beginEnv').setProperty('name', begin.getName());\n};\nNewcommandMethods.Macro = BaseMethods_js_1.default.Macro;\nexports.default = NewcommandMethods;\n//# sourceMappingURL=NewcommandMethods.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar Symbol_js_1 = require(\"../Symbol.js\");\nvar NewcommandUtil;\n(function (NewcommandUtil) {\n    function disassembleSymbol(name, symbol) {\n        var newArgs = [name, symbol.char];\n        if (symbol.attributes) {\n            for (var key in symbol.attributes) {\n                newArgs.push(key);\n                newArgs.push(symbol.attributes[key]);\n            }\n        }\n        return newArgs;\n    }\n    NewcommandUtil.disassembleSymbol = disassembleSymbol;\n    function assembleSymbol(args) {\n        var name = args[0];\n        var char = args[1];\n        var attrs = {};\n        for (var i = 2; i < args.length; i = i + 2) {\n            attrs[args[i]] = args[i + 1];\n        }\n        return new Symbol_js_1.Symbol(name, char, attrs);\n    }\n    NewcommandUtil.assembleSymbol = assembleSymbol;\n    function GetCSname(parser, cmd) {\n        var c = parser.GetNext();\n        if (c !== '\\\\') {\n            throw new TexError_js_1.default('MissingCS', '%1 must be followed by a control sequence', cmd);\n        }\n        var cs = ParseUtil_js_1.default.trimSpaces(parser.GetArgument(cmd));\n        return cs.substr(1);\n    }\n    NewcommandUtil.GetCSname = GetCSname;\n    function GetCsNameArgument(parser, name) {\n        var cs = ParseUtil_js_1.default.trimSpaces(parser.GetArgument(name));\n        if (cs.charAt(0) === '\\\\') {\n            cs = cs.substr(1);\n        }\n        if (!cs.match(/^(.|[a-z]+)$/i)) {\n            throw new TexError_js_1.default('IllegalControlSequenceName', 'Illegal control sequence name for %1', name);\n        }\n        return cs;\n    }\n    NewcommandUtil.GetCsNameArgument = GetCsNameArgument;\n    function GetArgCount(parser, name) {\n        var n = parser.GetBrackets(name);\n        if (n) {\n            n = ParseUtil_js_1.default.trimSpaces(n);\n            if (!n.match(/^[0-9]+$/)) {\n                throw new TexError_js_1.default('IllegalParamNumber', 'Illegal number of parameters specified in %1', name);\n            }\n        }\n        return n;\n    }\n    NewcommandUtil.GetArgCount = GetArgCount;\n    function GetTemplate(parser, cmd, cs) {\n        var c = parser.GetNext();\n        var params = [];\n        var n = 0;\n        var i = parser.i;\n        while (parser.i < parser.string.length) {\n            c = parser.GetNext();\n            if (c === '#') {\n                if (i !== parser.i) {\n                    params[n] = parser.string.substr(i, parser.i - i);\n                }\n                c = parser.string.charAt(++parser.i);\n                if (!c.match(/^[1-9]$/)) {\n                    throw new TexError_js_1.default('CantUseHash2', 'Illegal use of # in template for %1', cs);\n                }\n                if (parseInt(c) !== ++n) {\n                    throw new TexError_js_1.default('SequentialParam', 'Parameters for %1 must be numbered sequentially', cs);\n                }\n                i = parser.i + 1;\n            }\n            else if (c === '{') {\n                if (i !== parser.i) {\n                    params[n] = parser.string.substr(i, parser.i - i);\n                }\n                if (params.length > 0) {\n                    return [n.toString()].concat(params);\n                }\n                else {\n                    return n;\n                }\n            }\n            parser.i++;\n        }\n        throw new TexError_js_1.default('MissingReplacementString', 'Missing replacement string for definition of %1', cmd);\n    }\n    NewcommandUtil.GetTemplate = GetTemplate;\n    function GetParameter(parser, name, param) {\n        if (param == null) {\n            return parser.GetArgument(name);\n        }\n        var i = parser.i;\n        var j = 0;\n        var hasBraces = 0;\n        while (parser.i < parser.string.length) {\n            var c = parser.string.charAt(parser.i);\n            if (c === '{') {\n                if (parser.i === i) {\n                    hasBraces = 1;\n                }\n                parser.GetArgument(name);\n                j = parser.i - i;\n            }\n            else if (MatchParam(parser, param)) {\n                if (hasBraces) {\n                    i++;\n                    j -= 2;\n                }\n                return parser.string.substr(i, j);\n            }\n            else if (c === '\\\\') {\n                parser.i++;\n                j++;\n                hasBraces = 0;\n                var match = parser.string.substr(parser.i).match(/[a-z]+|./i);\n                if (match) {\n                    parser.i += match[0].length;\n                    j = parser.i - i;\n                }\n            }\n            else {\n                parser.i++;\n                j++;\n                hasBraces = 0;\n            }\n        }\n        throw new TexError_js_1.default('RunawayArgument', 'Runaway argument for %1?', name);\n    }\n    NewcommandUtil.GetParameter = GetParameter;\n    function MatchParam(parser, param) {\n        if (parser.string.substr(parser.i, param.length) !== param) {\n            return 0;\n        }\n        if (param.match(/\\\\[a-z]+$/i) &&\n            parser.string.charAt(parser.i + param.length).match(/[a-z]/i)) {\n            return 0;\n        }\n        parser.i += param.length;\n        return 1;\n    }\n    NewcommandUtil.MatchParam = MatchParam;\n    function addDelimiter(parser, cs, char, attr) {\n        var handlers = parser.configuration.handlers;\n        var handler = handlers.retrieve(NewcommandUtil.NEW_DELIMITER);\n        handler.add(cs, new Symbol_js_1.Symbol(cs, char, attr));\n    }\n    NewcommandUtil.addDelimiter = addDelimiter;\n    function addMacro(parser, cs, func, attr, symbol) {\n        if (symbol === void 0) { symbol = ''; }\n        var handlers = parser.configuration.handlers;\n        var handler = handlers.retrieve(NewcommandUtil.NEW_COMMAND);\n        handler.add(cs, new Symbol_js_1.Macro(symbol ? symbol : cs, func, attr));\n    }\n    NewcommandUtil.addMacro = addMacro;\n    function addEnvironment(parser, env, func, attr) {\n        var handlers = parser.configuration.handlers;\n        var handler = handlers.retrieve(NewcommandUtil.NEW_ENVIRONMENT);\n        handler.add(env, new Symbol_js_1.Macro(env, func, attr));\n    }\n    NewcommandUtil.addEnvironment = addEnvironment;\n    NewcommandUtil.NEW_DELIMITER = 'new-Delimiter';\n    NewcommandUtil.NEW_COMMAND = 'new-Command';\n    NewcommandUtil.NEW_ENVIRONMENT = 'new-Environment';\n})(NewcommandUtil || (NewcommandUtil = {}));\nexports.default = NewcommandUtil;\n//# sourceMappingURL=NewcommandUtil.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.NoErrorsConfiguration = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nfunction noErrors(factory, message, _id, expr) {\n    var mtext = factory.create('token', 'mtext', {}, expr.replace(/\\n/g, ' '));\n    var error = factory.create('node', 'merror', [mtext], { 'data-mjx-error': message, title: message });\n    return error;\n}\nexports.NoErrorsConfiguration = Configuration_js_1.Configuration.create('noerrors', { nodes: { 'error': noErrors } });\n//# sourceMappingURL=NoErrorsConfiguration.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.NoUndefinedConfiguration = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nfunction noUndefined(parser, name) {\n    var e_1, _a;\n    var textNode = parser.create('text', '\\\\' + name);\n    var options = parser.options.noundefined || {};\n    var def = {};\n    try {\n        for (var _b = __values(['color', 'background', 'size']), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var id = _c.value;\n            if (options[id]) {\n                def['math' + id] = options[id];\n            }\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n    parser.Push(parser.create('node', 'mtext', [], def, textNode));\n}\nexports.NoUndefinedConfiguration = Configuration_js_1.Configuration.create('noundefined', {\n    fallback: { macro: noUndefined },\n    options: {\n        noundefined: {\n            color: 'red',\n            background: '',\n            size: ''\n        }\n    },\n    priority: 3\n});\n//# sourceMappingURL=NoUndefinedConfiguration.js.map", "\"use strict\";\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.PhysicsConfiguration = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar PhysicsItems_js_1 = require(\"./PhysicsItems.js\");\nrequire(\"./PhysicsMappings.js\");\nexports.PhysicsConfiguration = Configuration_js_1.Configuration.create('physics', {\n    handler: {\n        macro: [\n            'Physics-automatic-bracing-macros',\n            'Physics-vector-macros',\n            'Physics-vector-mo',\n            'Physics-vector-mi',\n            'Physics-derivative-macros',\n            'Physics-expressions-macros',\n            'Physics-quick-quad-macros',\n            'Physics-bra-ket-macros',\n            'Physics-matrix-macros'\n        ],\n        character: ['Physics-characters'],\n        environment: ['Physics-aux-envs']\n    },\n    items: (_a = {},\n        _a[PhysicsItems_js_1.AutoOpen.prototype.kind] = PhysicsItems_js_1.AutoOpen,\n        _a),\n    options: {\n        physics: {\n            italicdiff: false,\n            arrowdel: false\n        }\n    }\n});\n//# sourceMappingURL=PhysicsConfiguration.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AutoOpen = void 0;\nvar StackItem_js_1 = require(\"../StackItem.js\");\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar TexParser_js_1 = __importDefault(require(\"../TexParser.js\"));\nvar AutoOpen = (function (_super) {\n    __extends(AutoOpen, _super);\n    function AutoOpen() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.openCount = 0;\n        return _this;\n    }\n    Object.defineProperty(AutoOpen.prototype, \"kind\", {\n        get: function () {\n            return 'auto open';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AutoOpen.prototype, \"isOpen\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AutoOpen.prototype.toMml = function () {\n        var parser = this.factory.configuration.parser;\n        var right = this.getProperty('right');\n        if (this.getProperty('smash')) {\n            var mml_1 = _super.prototype.toMml.call(this);\n            var smash = parser.create('node', 'mpadded', [mml_1], { height: 0, depth: 0 });\n            this.Clear();\n            this.Push(parser.create('node', 'TeXAtom', [smash]));\n        }\n        if (right) {\n            this.Push(new TexParser_js_1.default(right, parser.stack.env, parser.configuration).mml());\n        }\n        var mml = ParseUtil_js_1.default.fenced(this.factory.configuration, this.getProperty('open'), _super.prototype.toMml.call(this), this.getProperty('close'), this.getProperty('big'));\n        NodeUtil_js_1.default.removeProperties(mml, 'open', 'close', 'texClass');\n        return mml;\n    };\n    AutoOpen.prototype.checkItem = function (item) {\n        if (item.isKind('mml') && item.Size() === 1) {\n            var mml = item.toMml();\n            if (mml.isKind('mo') && mml.getText() === this.getProperty('open')) {\n                this.openCount++;\n            }\n        }\n        var close = item.getProperty('autoclose');\n        if (close && close === this.getProperty('close') && !this.openCount--) {\n            if (this.getProperty('ignore')) {\n                this.Clear();\n                return [[], true];\n            }\n            return [[this.toMml()], true];\n        }\n        return _super.prototype.checkItem.call(this, item);\n    };\n    AutoOpen.errors = Object.assign(Object.create(StackItem_js_1.BaseItem.errors), {\n        'stop': ['ExtraOrMissingDelims', 'Extra open or missing close delimiter']\n    });\n    return AutoOpen;\n}(StackItem_js_1.BaseItem));\nexports.AutoOpen = AutoOpen;\n//# sourceMappingURL=PhysicsItems.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar PhysicsMethods_js_1 = __importDefault(require(\"./PhysicsMethods.js\"));\nvar TexConstants_js_1 = require(\"../TexConstants.js\");\nvar ParseMethods_js_1 = __importDefault(require(\"../ParseMethods.js\"));\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nnew SymbolMap_js_1.CommandMap('Physics-automatic-bracing-macros', {\n    'quantity': 'Quantity',\n    'qty': 'Quantity',\n    'pqty': ['Quantity', '(', ')', true],\n    'bqty': ['Quantity', '[', ']', true],\n    'vqty': ['Quantity', '|', '|', true],\n    'Bqty': ['Quantity', '\\\\{', '\\\\}', true],\n    'absolutevalue': ['Quantity', '|', '|', true],\n    'abs': ['Quantity', '|', '|', true],\n    'norm': ['Quantity', '\\\\|', '\\\\|', true],\n    'evaluated': 'Eval',\n    'eval': 'Eval',\n    'order': ['Quantity', '(', ')', true, 'O',\n        TexConstants_js_1.TexConstant.Variant.CALLIGRAPHIC],\n    'commutator': 'Commutator',\n    'comm': 'Commutator',\n    'anticommutator': ['Commutator', '\\\\{', '\\\\}'],\n    'acomm': ['Commutator', '\\\\{', '\\\\}'],\n    'poissonbracket': ['Commutator', '\\\\{', '\\\\}'],\n    'pb': ['Commutator', '\\\\{', '\\\\}']\n}, PhysicsMethods_js_1.default);\nnew SymbolMap_js_1.CharacterMap('Physics-vector-mo', ParseMethods_js_1.default.mathchar0mo, {\n    dotproduct: ['\\u22C5', { mathvariant: TexConstants_js_1.TexConstant.Variant.BOLD }],\n    vdot: ['\\u22C5', { mathvariant: TexConstants_js_1.TexConstant.Variant.BOLD }],\n    crossproduct: '\\u00D7',\n    cross: '\\u00D7',\n    cp: '\\u00D7',\n    gradientnabla: ['\\u2207', { mathvariant: TexConstants_js_1.TexConstant.Variant.BOLD }]\n});\nnew SymbolMap_js_1.CharacterMap('Physics-vector-mi', ParseMethods_js_1.default.mathchar0mi, {\n    real: ['\\u211C', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    imaginary: ['\\u2111', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }]\n});\nnew SymbolMap_js_1.CommandMap('Physics-vector-macros', {\n    'vnabla': 'Vnabla',\n    'vectorbold': 'VectorBold',\n    'vb': 'VectorBold',\n    'vectorarrow': ['StarMacro', 1, '\\\\vec{\\\\vb', '{#1}}'],\n    'va': ['StarMacro', 1, '\\\\vec{\\\\vb', '{#1}}'],\n    'vectorunit': ['StarMacro', 1, '\\\\hat{\\\\vb', '{#1}}'],\n    'vu': ['StarMacro', 1, '\\\\hat{\\\\vb', '{#1}}'],\n    'gradient': ['OperatorApplication', '\\\\vnabla', '(', '['],\n    'grad': ['OperatorApplication', '\\\\vnabla', '(', '['],\n    'divergence': ['VectorOperator', '\\\\vnabla\\\\vdot', '(', '['],\n    'div': ['VectorOperator', '\\\\vnabla\\\\vdot', '(', '['],\n    'curl': ['VectorOperator', '\\\\vnabla\\\\crossproduct', '(', '['],\n    'laplacian': ['OperatorApplication', '\\\\nabla^2', '(', '['],\n}, PhysicsMethods_js_1.default);\nnew SymbolMap_js_1.CommandMap('Physics-expressions-macros', {\n    'sin': 'Expression',\n    'sinh': 'Expression',\n    'arcsin': 'Expression',\n    'asin': 'Expression',\n    'cos': 'Expression',\n    'cosh': 'Expression',\n    'arccos': 'Expression',\n    'acos': 'Expression',\n    'tan': 'Expression',\n    'tanh': 'Expression',\n    'arctan': 'Expression',\n    'atan': 'Expression',\n    'csc': 'Expression',\n    'csch': 'Expression',\n    'arccsc': 'Expression',\n    'acsc': 'Expression',\n    'sec': 'Expression',\n    'sech': 'Expression',\n    'arcsec': 'Expression',\n    'asec': 'Expression',\n    'cot': 'Expression',\n    'coth': 'Expression',\n    'arccot': 'Expression',\n    'acot': 'Expression',\n    'exp': ['Expression', false],\n    'log': 'Expression',\n    'ln': 'Expression',\n    'det': ['Expression', false],\n    'Pr': ['Expression', false],\n    'tr': ['Expression', false],\n    'trace': ['Expression', false, 'tr'],\n    'Tr': ['Expression', false],\n    'Trace': ['Expression', false, 'Tr'],\n    'rank': 'NamedFn',\n    'erf': ['Expression', false],\n    'Residue': ['Macro', '\\\\mathrm{Res}'],\n    'Res': ['OperatorApplication', '\\\\Residue', '(', '[', '{'],\n    'principalvalue': ['OperatorApplication', '{\\\\cal P}'],\n    'pv': ['OperatorApplication', '{\\\\cal P}'],\n    'PV': ['OperatorApplication', '{\\\\rm P.V.}'],\n    'Re': ['OperatorApplication', '\\\\mathrm{Re}', '{'],\n    'Im': ['OperatorApplication', '\\\\mathrm{Im}', '{'],\n    'sine': ['NamedFn', 'sin'],\n    'hypsine': ['NamedFn', 'sinh'],\n    'arcsine': ['NamedFn', 'arcsin'],\n    'asine': ['NamedFn', 'asin'],\n    'cosine': ['NamedFn', 'cos'],\n    'hypcosine': ['NamedFn', 'cosh'],\n    'arccosine': ['NamedFn', 'arccos'],\n    'acosine': ['NamedFn', 'acos'],\n    'tangent': ['NamedFn', 'tan'],\n    'hyptangent': ['NamedFn', 'tanh'],\n    'arctangent': ['NamedFn', 'arctan'],\n    'atangent': ['NamedFn', 'atan'],\n    'cosecant': ['NamedFn', 'csc'],\n    'hypcosecant': ['NamedFn', 'csch'],\n    'arccosecant': ['NamedFn', 'arccsc'],\n    'acosecant': ['NamedFn', 'acsc'],\n    'secant': ['NamedFn', 'sec'],\n    'hypsecant': ['NamedFn', 'sech'],\n    'arcsecant': ['NamedFn', 'arcsec'],\n    'asecant': ['NamedFn', 'asec'],\n    'cotangent': ['NamedFn', 'cot'],\n    'hypcotangent': ['NamedFn', 'coth'],\n    'arccotangent': ['NamedFn', 'arccot'],\n    'acotangent': ['NamedFn', 'acot'],\n    'exponential': ['NamedFn', 'exp'],\n    'logarithm': ['NamedFn', 'log'],\n    'naturallogarithm': ['NamedFn', 'ln'],\n    'determinant': ['NamedFn', 'det'],\n    'Probability': ['NamedFn', 'Pr'],\n}, PhysicsMethods_js_1.default);\nnew SymbolMap_js_1.CommandMap('Physics-quick-quad-macros', {\n    'qqtext': 'Qqtext',\n    'qq': 'Qqtext',\n    'qcomma': ['Macro', '\\\\qqtext*{,}'],\n    'qc': ['Macro', '\\\\qqtext*{,}'],\n    'qcc': ['Qqtext', 'c.c.'],\n    'qif': ['Qqtext', 'if'],\n    'qthen': ['Qqtext', 'then'],\n    'qelse': ['Qqtext', 'else'],\n    'qotherwise': ['Qqtext', 'otherwise'],\n    'qunless': ['Qqtext', 'unless'],\n    'qgiven': ['Qqtext', 'given'],\n    'qusing': ['Qqtext', 'using'],\n    'qassume': ['Qqtext', 'assume'],\n    'qsince': ['Qqtext', 'since'],\n    'qlet': ['Qqtext', 'let'],\n    'qfor': ['Qqtext', 'for'],\n    'qall': ['Qqtext', 'all'],\n    'qeven': ['Qqtext', 'even'],\n    'qodd': ['Qqtext', 'odd'],\n    'qinteger': ['Qqtext', 'integer'],\n    'qand': ['Qqtext', 'and'],\n    'qor': ['Qqtext', 'or'],\n    'qas': ['Qqtext', 'as'],\n    'qin': ['Qqtext', 'in'],\n}, PhysicsMethods_js_1.default);\nnew SymbolMap_js_1.CommandMap('Physics-derivative-macros', {\n    'diffd': 'DiffD',\n    'flatfrac': ['Macro', '\\\\left.#1\\\\middle/#2\\\\right.', 2],\n    'differential': ['Differential', '\\\\diffd'],\n    'dd': ['Differential', '\\\\diffd'],\n    'variation': ['Differential', '\\\\delta'],\n    'var': ['Differential', '\\\\delta'],\n    'derivative': ['Derivative', 2, '\\\\diffd'],\n    'dv': ['Derivative', 2, '\\\\diffd'],\n    'partialderivative': ['Derivative', 3, '\\\\partial'],\n    'pderivative': ['Derivative', 3, '\\\\partial'],\n    'pdv': ['Derivative', 3, '\\\\partial'],\n    'functionalderivative': ['Derivative', 2, '\\\\delta'],\n    'fderivative': ['Derivative', 2, '\\\\delta'],\n    'fdv': ['Derivative', 2, '\\\\delta'],\n}, PhysicsMethods_js_1.default);\nnew SymbolMap_js_1.CommandMap('Physics-bra-ket-macros', {\n    'bra': 'Bra',\n    'ket': 'Ket',\n    'innerproduct': 'BraKet',\n    'ip': 'BraKet',\n    'braket': 'BraKet',\n    'outerproduct': 'KetBra',\n    'dyad': 'KetBra',\n    'ketbra': 'KetBra',\n    'op': 'KetBra',\n    'expectationvalue': 'Expectation',\n    'expval': 'Expectation',\n    'ev': 'Expectation',\n    'matrixelement': 'MatrixElement',\n    'matrixel': 'MatrixElement',\n    'mel': 'MatrixElement',\n}, PhysicsMethods_js_1.default);\nnew SymbolMap_js_1.CommandMap('Physics-matrix-macros', {\n    'matrixquantity': 'MatrixQuantity',\n    'mqty': 'MatrixQuantity',\n    'pmqty': ['Macro', '\\\\mqty(#1)', 1],\n    'Pmqty': ['Macro', '\\\\mqty*(#1)', 1],\n    'bmqty': ['Macro', '\\\\mqty[#1]', 1],\n    'vmqty': ['Macro', '\\\\mqty|#1|', 1],\n    'smallmatrixquantity': ['MatrixQuantity', true],\n    'smqty': ['MatrixQuantity', true],\n    'spmqty': ['Macro', '\\\\smqty(#1)', 1],\n    'sPmqty': ['Macro', '\\\\smqty*(#1)', 1],\n    'sbmqty': ['Macro', '\\\\smqty[#1]', 1],\n    'svmqty': ['Macro', '\\\\smqty|#1|', 1],\n    'matrixdeterminant': ['Macro', '\\\\vmqty{#1}', 1],\n    'mdet': ['Macro', '\\\\vmqty{#1}', 1],\n    'smdet': ['Macro', '\\\\svmqty{#1}', 1],\n    'identitymatrix': 'IdentityMatrix',\n    'imat': 'IdentityMatrix',\n    'xmatrix': 'XMatrix',\n    'xmat': 'XMatrix',\n    'zeromatrix': ['Macro', '\\\\xmat{0}{#1}{#2}', 2],\n    'zmat': ['Macro', '\\\\xmat{0}{#1}{#2}', 2],\n    'paulimatrix': 'PauliMatrix',\n    'pmat': 'PauliMatrix',\n    'diagonalmatrix': 'DiagonalMatrix',\n    'dmat': 'DiagonalMatrix',\n    'antidiagonalmatrix': ['DiagonalMatrix', true],\n    'admat': ['DiagonalMatrix', true]\n}, PhysicsMethods_js_1.default);\nnew SymbolMap_js_1.EnvironmentMap('Physics-aux-envs', ParseMethods_js_1.default.environment, {\n    smallmatrix: ['Array', null, null, null, 'c', '0.333em', '.2em', 'S', 1]\n}, PhysicsMethods_js_1.default);\nnew SymbolMap_js_1.MacroMap('Physics-characters', {\n    '|': ['AutoClose', MmlNode_js_1.TEXCLASS.ORD],\n    ')': 'AutoClose',\n    ']': 'AutoClose'\n}, PhysicsMethods_js_1.default);\n//# sourceMappingURL=PhysicsMappings.js.map", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar BaseMethods_js_1 = __importDefault(require(\"../base/BaseMethods.js\"));\nvar TexParser_js_1 = __importDefault(require(\"../TexParser.js\"));\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar NodeFactory_js_1 = require(\"../NodeFactory.js\");\nvar PhysicsMethods = {};\nvar pairs = {\n    '(': ')',\n    '[': ']',\n    '{': '}',\n    '|': '|',\n};\nvar biggs = /^(b|B)i(g{1,2})$/;\nPhysicsMethods.Quantity = function (parser, name, open, close, arg, named, variant) {\n    if (open === void 0) { open = '('; }\n    if (close === void 0) { close = ')'; }\n    if (arg === void 0) { arg = false; }\n    if (named === void 0) { named = ''; }\n    if (variant === void 0) { variant = ''; }\n    var star = arg ? parser.GetStar() : false;\n    var next = parser.GetNext();\n    var position = parser.i;\n    var big = null;\n    if (next === '\\\\') {\n        parser.i++;\n        big = parser.GetCS();\n        if (!big.match(biggs)) {\n            var empty = parser.create('node', 'mrow');\n            parser.Push(ParseUtil_js_1.default.fenced(parser.configuration, open, empty, close));\n            parser.i = position;\n            return;\n        }\n        next = parser.GetNext();\n    }\n    var right = pairs[next];\n    if (arg && next !== '{') {\n        throw new TexError_js_1.default('MissingArgFor', 'Missing argument for %1', parser.currentCS);\n    }\n    if (!right) {\n        var empty = parser.create('node', 'mrow');\n        parser.Push(ParseUtil_js_1.default.fenced(parser.configuration, open, empty, close));\n        parser.i = position;\n        return;\n    }\n    if (named) {\n        var mml = parser.create('token', 'mi', { texClass: MmlNode_js_1.TEXCLASS.OP }, named);\n        if (variant) {\n            NodeUtil_js_1.default.setAttribute(mml, 'mathvariant', variant);\n        }\n        parser.Push(parser.itemFactory.create('fn', mml));\n    }\n    if (next === '{') {\n        var argument = parser.GetArgument(name);\n        next = arg ? open : '\\\\{';\n        right = arg ? close : '\\\\}';\n        argument = star ? next + ' ' + argument + ' ' + right :\n            (big ?\n                '\\\\' + big + 'l' + next + ' ' + argument + ' ' + '\\\\' + big + 'r' + right :\n                '\\\\left' + next + ' ' + argument + ' ' + '\\\\right' + right);\n        parser.Push(new TexParser_js_1.default(argument, parser.stack.env, parser.configuration).mml());\n        return;\n    }\n    if (arg) {\n        next = open;\n        right = close;\n    }\n    parser.i++;\n    parser.Push(parser.itemFactory.create('auto open')\n        .setProperties({ open: next, close: right, big: big }));\n};\nPhysicsMethods.Eval = function (parser, name) {\n    var star = parser.GetStar();\n    var next = parser.GetNext();\n    if (next === '{') {\n        var arg = parser.GetArgument(name);\n        var replace = '\\\\left. ' +\n            (star ? '\\\\smash{' + arg + '}' : arg) +\n            ' ' + '\\\\vphantom{\\\\int}\\\\right|';\n        parser.string = parser.string.slice(0, parser.i) + replace +\n            parser.string.slice(parser.i);\n        return;\n    }\n    if (next === '(' || next === '[') {\n        parser.i++;\n        parser.Push(parser.itemFactory.create('auto open')\n            .setProperties({ open: next, close: '|',\n            smash: star, right: '\\\\vphantom{\\\\int}' }));\n        return;\n    }\n    throw new TexError_js_1.default('MissingArgFor', 'Missing argument for %1', parser.currentCS);\n};\nPhysicsMethods.Commutator = function (parser, name, open, close) {\n    if (open === void 0) { open = '['; }\n    if (close === void 0) { close = ']'; }\n    var star = parser.GetStar();\n    var next = parser.GetNext();\n    var big = null;\n    if (next === '\\\\') {\n        parser.i++;\n        big = parser.GetCS();\n        if (!big.match(biggs)) {\n            throw new TexError_js_1.default('MissingArgFor', 'Missing argument for %1', parser.currentCS);\n        }\n        next = parser.GetNext();\n    }\n    if (next !== '{') {\n        throw new TexError_js_1.default('MissingArgFor', 'Missing argument for %1', parser.currentCS);\n    }\n    var arg1 = parser.GetArgument(name);\n    var arg2 = parser.GetArgument(name);\n    var argument = arg1 + ',' + arg2;\n    argument = star ? open + ' ' + argument + ' ' + close :\n        (big ?\n            '\\\\' + big + 'l' + open + ' ' + argument + ' ' + '\\\\' + big + 'r' + close :\n            '\\\\left' + open + ' ' + argument + ' ' + '\\\\right' + close);\n    parser.Push(new TexParser_js_1.default(argument, parser.stack.env, parser.configuration).mml());\n};\nvar latinCap = [0x41, 0x5A];\nvar latinSmall = [0x61, 0x7A];\nvar greekCap = [0x391, 0x3A9];\nvar greekSmall = [0x3B1, 0x3C9];\nvar digits = [0x30, 0x39];\nfunction inRange(value, range) {\n    return (value >= range[0] && value <= range[1]);\n}\nfunction createVectorToken(factory, kind, def, text) {\n    var parser = factory.configuration.parser;\n    var token = NodeFactory_js_1.NodeFactory.createToken(factory, kind, def, text);\n    var code = text.codePointAt(0);\n    if (text.length === 1 && !parser.stack.env.font &&\n        parser.stack.env.vectorFont &&\n        (inRange(code, latinCap) || inRange(code, latinSmall) ||\n            inRange(code, greekCap) || inRange(code, digits) ||\n            (inRange(code, greekSmall) && parser.stack.env.vectorStar) ||\n            NodeUtil_js_1.default.getAttribute(token, 'accent'))) {\n        NodeUtil_js_1.default.setAttribute(token, 'mathvariant', parser.stack.env.vectorFont);\n    }\n    return token;\n}\nPhysicsMethods.VectorBold = function (parser, name) {\n    var star = parser.GetStar();\n    var arg = parser.GetArgument(name);\n    var oldToken = parser.configuration.nodeFactory.get('token');\n    var oldFont = parser.stack.env.font;\n    delete parser.stack.env.font;\n    parser.configuration.nodeFactory.set('token', createVectorToken);\n    parser.stack.env.vectorFont = star ? 'bold-italic' : 'bold';\n    parser.stack.env.vectorStar = star;\n    var node = new TexParser_js_1.default(arg, parser.stack.env, parser.configuration).mml();\n    if (oldFont) {\n        parser.stack.env.font = oldFont;\n    }\n    delete parser.stack.env.vectorFont;\n    delete parser.stack.env.vectorStar;\n    parser.configuration.nodeFactory.set('token', oldToken);\n    parser.Push(node);\n};\nPhysicsMethods.StarMacro = function (parser, name, argcount) {\n    var parts = [];\n    for (var _i = 3; _i < arguments.length; _i++) {\n        parts[_i - 3] = arguments[_i];\n    }\n    var star = parser.GetStar();\n    var args = [];\n    if (argcount) {\n        for (var i = args.length; i < argcount; i++) {\n            args.push(parser.GetArgument(name));\n        }\n    }\n    var macro = parts.join(star ? '*' : '');\n    macro = ParseUtil_js_1.default.substituteArgs(parser, args, macro);\n    parser.string = ParseUtil_js_1.default.addArgs(parser, macro, parser.string.slice(parser.i));\n    parser.i = 0;\n    ParseUtil_js_1.default.checkMaxMacros(parser);\n};\nvar vectorApplication = function (parser, kind, name, operator, fences) {\n    var op = new TexParser_js_1.default(operator, parser.stack.env, parser.configuration).mml();\n    parser.Push(parser.itemFactory.create(kind, op));\n    var left = parser.GetNext();\n    var right = pairs[left];\n    if (!right) {\n        return;\n    }\n    var lfence = '', rfence = '', arg = '';\n    var enlarge = fences.indexOf(left) !== -1;\n    if (left === '{') {\n        arg = parser.GetArgument(name);\n        lfence = enlarge ? '\\\\left\\\\{' : '';\n        rfence = enlarge ? '\\\\right\\\\}' : '';\n        var macro = lfence + ' ' + arg + ' ' + rfence;\n        parser.string = macro + parser.string.slice(parser.i);\n        parser.i = 0;\n        return;\n    }\n    if (!enlarge) {\n        return;\n    }\n    parser.i++;\n    parser.Push(parser.itemFactory.create('auto open')\n        .setProperties({ open: left, close: right }));\n};\nPhysicsMethods.OperatorApplication = function (parser, name, operator) {\n    var fences = [];\n    for (var _i = 3; _i < arguments.length; _i++) {\n        fences[_i - 3] = arguments[_i];\n    }\n    vectorApplication(parser, 'fn', name, operator, fences);\n};\nPhysicsMethods.VectorOperator = function (parser, name, operator) {\n    var fences = [];\n    for (var _i = 3; _i < arguments.length; _i++) {\n        fences[_i - 3] = arguments[_i];\n    }\n    vectorApplication(parser, 'mml', name, operator, fences);\n};\nPhysicsMethods.Expression = function (parser, name, opt, id) {\n    if (opt === void 0) { opt = true; }\n    if (id === void 0) { id = ''; }\n    id = id || name.slice(1);\n    var exp = opt ? parser.GetBrackets(name) : null;\n    var mml = parser.create('token', 'mi', { texClass: MmlNode_js_1.TEXCLASS.OP }, id);\n    if (exp) {\n        var sup = new TexParser_js_1.default(exp, parser.stack.env, parser.configuration).mml();\n        mml = parser.create('node', 'msup', [mml, sup]);\n    }\n    parser.Push(parser.itemFactory.create('fn', mml));\n    if (parser.GetNext() !== '(') {\n        return;\n    }\n    parser.i++;\n    parser.Push(parser.itemFactory.create('auto open')\n        .setProperties({ open: '(', close: ')' }));\n};\nPhysicsMethods.Qqtext = function (parser, name, text) {\n    var star = parser.GetStar();\n    var arg = text ? text : parser.GetArgument(name);\n    var replace = (star ? '' : '\\\\quad') + '\\\\text{' + arg + '}\\\\quad ';\n    parser.string = parser.string.slice(0, parser.i) + replace +\n        parser.string.slice(parser.i);\n};\nPhysicsMethods.Differential = function (parser, name, op) {\n    var optArg = parser.GetBrackets(name);\n    var power = optArg != null ? '^{' + optArg + '}' : ' ';\n    var parens = parser.GetNext() === '(';\n    var braces = parser.GetNext() === '{';\n    var macro = op + power;\n    if (!(parens || braces)) {\n        macro += parser.GetArgument(name, true) || '';\n        var mml = new TexParser_js_1.default(macro, parser.stack.env, parser.configuration).mml();\n        parser.Push(mml);\n        return;\n    }\n    if (braces) {\n        macro += parser.GetArgument(name);\n        var mml = new TexParser_js_1.default(macro, parser.stack.env, parser.configuration).mml();\n        parser.Push(parser.create('node', 'TeXAtom', [mml], { texClass: MmlNode_js_1.TEXCLASS.OP }));\n        return;\n    }\n    parser.Push(new TexParser_js_1.default(macro, parser.stack.env, parser.configuration).mml());\n    parser.i++;\n    parser.Push(parser.itemFactory.create('auto open')\n        .setProperties({ open: '(', close: ')' }));\n};\nPhysicsMethods.Derivative = function (parser, name, argMax, op) {\n    var star = parser.GetStar();\n    var optArg = parser.GetBrackets(name);\n    var argCounter = 1;\n    var args = [];\n    args.push(parser.GetArgument(name));\n    while (parser.GetNext() === '{' && argCounter < argMax) {\n        args.push(parser.GetArgument(name));\n        argCounter++;\n    }\n    var ignore = false;\n    var power1 = ' ';\n    var power2 = ' ';\n    if (argMax > 2 && args.length > 2) {\n        power1 = '^{' + (args.length - 1) + '}';\n        ignore = true;\n    }\n    else if (optArg != null) {\n        if (argMax > 2 && args.length > 1) {\n            ignore = true;\n        }\n        power1 = '^{' + optArg + '}';\n        power2 = power1;\n    }\n    var frac = star ? '\\\\flatfrac' : '\\\\frac';\n    var first = args.length > 1 ? args[0] : '';\n    var second = args.length > 1 ? args[1] : args[0];\n    var rest = '';\n    for (var i = 2, arg = void 0; arg = args[i]; i++) {\n        rest += op + ' ' + arg;\n    }\n    var macro = frac + '{' + op + power1 + first + '}' +\n        '{' + op + ' ' + second + power2 + ' ' + rest + '}';\n    parser.Push(new TexParser_js_1.default(macro, parser.stack.env, parser.configuration).mml());\n    if (parser.GetNext() === '(') {\n        parser.i++;\n        parser.Push(parser.itemFactory.create('auto open')\n            .setProperties({ open: '(', close: ')', ignore: ignore }));\n    }\n};\nPhysicsMethods.Bra = function (parser, name) {\n    var starBra = parser.GetStar();\n    var bra = parser.GetArgument(name);\n    var ket = '';\n    var hasKet = false;\n    var starKet = false;\n    if (parser.GetNext() === '\\\\') {\n        var saveI = parser.i;\n        parser.i++;\n        var cs = parser.GetCS();\n        var symbol = parser.lookup('macro', cs);\n        if (symbol && symbol.symbol === 'ket') {\n            hasKet = true;\n            saveI = parser.i;\n            starKet = parser.GetStar();\n            if (parser.GetNext() === '{') {\n                ket = parser.GetArgument(cs, true);\n            }\n            else {\n                parser.i = saveI;\n                starKet = false;\n            }\n        }\n        else {\n            parser.i = saveI;\n        }\n    }\n    var macro = '';\n    if (hasKet) {\n        macro = (starBra || starKet) ?\n            \"\\\\langle{\".concat(bra, \"}\\\\vert{\").concat(ket, \"}\\\\rangle\") :\n            \"\\\\left\\\\langle{\".concat(bra, \"}\\\\middle\\\\vert{\").concat(ket, \"}\\\\right\\\\rangle\");\n    }\n    else {\n        macro = (starBra || starKet) ?\n            \"\\\\langle{\".concat(bra, \"}\\\\vert\") : \"\\\\left\\\\langle{\".concat(bra, \"}\\\\right\\\\vert{\").concat(ket, \"}\");\n    }\n    parser.Push(new TexParser_js_1.default(macro, parser.stack.env, parser.configuration).mml());\n};\nPhysicsMethods.Ket = function (parser, name) {\n    var star = parser.GetStar();\n    var ket = parser.GetArgument(name);\n    var macro = star ? \"\\\\vert{\".concat(ket, \"}\\\\rangle\") :\n        \"\\\\left\\\\vert{\".concat(ket, \"}\\\\right\\\\rangle\");\n    parser.Push(new TexParser_js_1.default(macro, parser.stack.env, parser.configuration).mml());\n};\nPhysicsMethods.BraKet = function (parser, name) {\n    var star = parser.GetStar();\n    var bra = parser.GetArgument(name);\n    var ket = null;\n    if (parser.GetNext() === '{') {\n        ket = parser.GetArgument(name, true);\n    }\n    var macro = '';\n    if (ket == null) {\n        macro = star ?\n            \"\\\\langle{\".concat(bra, \"}\\\\vert{\").concat(bra, \"}\\\\rangle\") :\n            \"\\\\left\\\\langle{\".concat(bra, \"}\\\\middle\\\\vert{\").concat(bra, \"}\\\\right\\\\rangle\");\n    }\n    else {\n        macro = star ?\n            \"\\\\langle{\".concat(bra, \"}\\\\vert{\").concat(ket, \"}\\\\rangle\") :\n            \"\\\\left\\\\langle{\".concat(bra, \"}\\\\middle\\\\vert{\").concat(ket, \"}\\\\right\\\\rangle\");\n    }\n    parser.Push(new TexParser_js_1.default(macro, parser.stack.env, parser.configuration).mml());\n};\nPhysicsMethods.KetBra = function (parser, name) {\n    var star = parser.GetStar();\n    var ket = parser.GetArgument(name);\n    var bra = null;\n    if (parser.GetNext() === '{') {\n        bra = parser.GetArgument(name, true);\n    }\n    var macro = '';\n    if (bra == null) {\n        macro = star ?\n            \"\\\\vert{\".concat(ket, \"}\\\\rangle\\\\!\\\\langle{\").concat(ket, \"}\\\\vert\") :\n            \"\\\\left\\\\vert{\".concat(ket, \"}\\\\middle\\\\rangle\\\\!\\\\middle\\\\langle{\").concat(ket, \"}\\\\right\\\\vert\");\n    }\n    else {\n        macro = star ?\n            \"\\\\vert{\".concat(ket, \"}\\\\rangle\\\\!\\\\langle{\").concat(bra, \"}\\\\vert\") :\n            \"\\\\left\\\\vert{\".concat(ket, \"}\\\\middle\\\\rangle\\\\!\\\\middle\\\\langle{\").concat(bra, \"}\\\\right\\\\vert\");\n    }\n    parser.Push(new TexParser_js_1.default(macro, parser.stack.env, parser.configuration).mml());\n};\nfunction outputBraket(_a, star1, star2) {\n    var _b = __read(_a, 3), arg1 = _b[0], arg2 = _b[1], arg3 = _b[2];\n    return (star1 && star2) ?\n        \"\\\\left\\\\langle{\".concat(arg1, \"}\\\\middle\\\\vert{\").concat(arg2, \"}\\\\middle\\\\vert{\").concat(arg3, \"}\\\\right\\\\rangle\") :\n        (star1 ? \"\\\\langle{\".concat(arg1, \"}\\\\vert{\").concat(arg2, \"}\\\\vert{\").concat(arg3, \"}\\\\rangle\") :\n            \"\\\\left\\\\langle{\".concat(arg1, \"}\\\\right\\\\vert{\").concat(arg2, \"}\\\\left\\\\vert{\").concat(arg3, \"}\\\\right\\\\rangle\"));\n}\nPhysicsMethods.Expectation = function (parser, name) {\n    var star1 = parser.GetStar();\n    var star2 = star1 && parser.GetStar();\n    var arg1 = parser.GetArgument(name);\n    var arg2 = null;\n    if (parser.GetNext() === '{') {\n        arg2 = parser.GetArgument(name, true);\n    }\n    var macro = (arg1 && arg2) ?\n        outputBraket([arg2, arg1, arg2], star1, star2) :\n        (star1 ? \"\\\\langle {\".concat(arg1, \"} \\\\rangle\") :\n            \"\\\\left\\\\langle {\".concat(arg1, \"} \\\\right\\\\rangle\"));\n    parser.Push(new TexParser_js_1.default(macro, parser.stack.env, parser.configuration).mml());\n};\nPhysicsMethods.MatrixElement = function (parser, name) {\n    var star1 = parser.GetStar();\n    var star2 = star1 && parser.GetStar();\n    var arg1 = parser.GetArgument(name);\n    var arg2 = parser.GetArgument(name);\n    var arg3 = parser.GetArgument(name);\n    var macro = outputBraket([arg1, arg2, arg3], star1, star2);\n    parser.Push(new TexParser_js_1.default(macro, parser.stack.env, parser.configuration).mml());\n};\nPhysicsMethods.MatrixQuantity = function (parser, name, small) {\n    var star = parser.GetStar();\n    var next = parser.GetNext();\n    var array = small ? 'smallmatrix' : 'array';\n    var arg = '';\n    var open = '';\n    var close = '';\n    switch (next) {\n        case '{':\n            arg = parser.GetArgument(name);\n            break;\n        case '(':\n            parser.i++;\n            open = star ? '\\\\lgroup' : '(';\n            close = star ? '\\\\rgroup' : ')';\n            arg = parser.GetUpTo(name, ')');\n            break;\n        case '[':\n            parser.i++;\n            open = '[';\n            close = ']';\n            arg = parser.GetUpTo(name, ']');\n            break;\n        case '|':\n            parser.i++;\n            open = '|';\n            close = '|';\n            arg = parser.GetUpTo(name, '|');\n            break;\n        default:\n            open = '(';\n            close = ')';\n            break;\n    }\n    var macro = (open ? '\\\\left' : '') + open +\n        '\\\\begin{' + array + '}{} ' + arg + '\\\\end{' + array + '}' +\n        (open ? '\\\\right' : '') + close;\n    parser.Push(new TexParser_js_1.default(macro, parser.stack.env, parser.configuration).mml());\n};\nPhysicsMethods.IdentityMatrix = function (parser, name) {\n    var arg = parser.GetArgument(name);\n    var size = parseInt(arg, 10);\n    if (isNaN(size)) {\n        throw new TexError_js_1.default('InvalidNumber', 'Invalid number');\n    }\n    if (size <= 1) {\n        parser.string = '1' + parser.string.slice(parser.i);\n        parser.i = 0;\n        return;\n    }\n    var zeros = Array(size).fill('0');\n    var columns = [];\n    for (var i = 0; i < size; i++) {\n        var row = zeros.slice();\n        row[i] = '1';\n        columns.push(row.join(' & '));\n    }\n    parser.string = columns.join('\\\\\\\\ ') + parser.string.slice(parser.i);\n    parser.i = 0;\n};\nPhysicsMethods.XMatrix = function (parser, name) {\n    var star = parser.GetStar();\n    var arg1 = parser.GetArgument(name);\n    var arg2 = parser.GetArgument(name);\n    var arg3 = parser.GetArgument(name);\n    var n = parseInt(arg2, 10);\n    var m = parseInt(arg3, 10);\n    if (isNaN(n) || isNaN(m) || m.toString() !== arg3 || n.toString() !== arg2) {\n        throw new TexError_js_1.default('InvalidNumber', 'Invalid number');\n    }\n    n = n < 1 ? 1 : n;\n    m = m < 1 ? 1 : m;\n    if (!star) {\n        var row = Array(m).fill(arg1).join(' & ');\n        var matrix_1 = Array(n).fill(row).join('\\\\\\\\ ');\n        parser.string = matrix_1 + parser.string.slice(parser.i);\n        parser.i = 0;\n        return;\n    }\n    var matrix = '';\n    if (n === 1 && m === 1) {\n        matrix = arg1;\n    }\n    else if (n === 1) {\n        var row = [];\n        for (var i = 1; i <= m; i++) {\n            row.push(\"\".concat(arg1, \"_{\").concat(i, \"}\"));\n        }\n        matrix = row.join(' & ');\n    }\n    else if (m === 1) {\n        var row = [];\n        for (var i = 1; i <= n; i++) {\n            row.push(\"\".concat(arg1, \"_{\").concat(i, \"}\"));\n        }\n        matrix = row.join('\\\\\\\\ ');\n    }\n    else {\n        var rows = [];\n        for (var i = 1; i <= n; i++) {\n            var row = [];\n            for (var j = 1; j <= m; j++) {\n                row.push(\"\".concat(arg1, \"_{{\").concat(i, \"}{\").concat(j, \"}}\"));\n            }\n            rows.push(row.join(' & '));\n        }\n        matrix = rows.join('\\\\\\\\ ');\n    }\n    parser.string = matrix + parser.string.slice(parser.i);\n    parser.i = 0;\n    return;\n};\nPhysicsMethods.PauliMatrix = function (parser, name) {\n    var arg = parser.GetArgument(name);\n    var matrix = arg.slice(1);\n    switch (arg[0]) {\n        case '0':\n            matrix += ' 1 & 0\\\\\\\\ 0 & 1';\n            break;\n        case '1':\n        case 'x':\n            matrix += ' 0 & 1\\\\\\\\ 1 & 0';\n            break;\n        case '2':\n        case 'y':\n            matrix += ' 0 & -i\\\\\\\\ i & 0';\n            break;\n        case '3':\n        case 'z':\n            matrix += ' 1 & 0\\\\\\\\ 0 & -1';\n            break;\n        default:\n    }\n    parser.string = matrix + parser.string.slice(parser.i);\n    parser.i = 0;\n};\nPhysicsMethods.DiagonalMatrix = function (parser, name, anti) {\n    if (parser.GetNext() !== '{') {\n        return;\n    }\n    var startI = parser.i;\n    parser.GetArgument(name);\n    var endI = parser.i;\n    parser.i = startI + 1;\n    var elements = [];\n    var element = '';\n    var currentI = parser.i;\n    while (currentI < endI) {\n        try {\n            element = parser.GetUpTo(name, ',');\n        }\n        catch (e) {\n            parser.i = endI;\n            elements.push(parser.string.slice(currentI, endI - 1));\n            break;\n        }\n        if (parser.i >= endI) {\n            elements.push(parser.string.slice(currentI, endI));\n            break;\n        }\n        currentI = parser.i;\n        elements.push(element);\n    }\n    parser.string = makeDiagMatrix(elements, anti) + parser.string.slice(endI);\n    parser.i = 0;\n};\nfunction makeDiagMatrix(elements, anti) {\n    var length = elements.length;\n    var matrix = [];\n    for (var i = 0; i < length; i++) {\n        matrix.push(Array(anti ? length - i : i + 1).join('&') +\n            '\\\\mqty{' + elements[i] + '}');\n    }\n    return matrix.join('\\\\\\\\ ');\n}\nPhysicsMethods.AutoClose = function (parser, fence, _texclass) {\n    var mo = parser.create('token', 'mo', { stretchy: false }, fence);\n    var item = parser.itemFactory.create('mml', mo).\n        setProperties({ autoclose: fence });\n    parser.Push(item);\n};\nPhysicsMethods.Vnabla = function (parser, _name) {\n    var argument = parser.options.physics.arrowdel ?\n        '\\\\vec{\\\\gradientnabla}' : '{\\\\gradientnabla}';\n    return parser.Push(new TexParser_js_1.default(argument, parser.stack.env, parser.configuration).mml());\n};\nPhysicsMethods.DiffD = function (parser, _name) {\n    var argument = parser.options.physics.italicdiff ? 'd' : '{\\\\rm d}';\n    return parser.Push(new TexParser_js_1.default(argument, parser.stack.env, parser.configuration).mml());\n};\nPhysicsMethods.Macro = BaseMethods_js_1.default.Macro;\nPhysicsMethods.NamedFn = BaseMethods_js_1.default.NamedFn;\nPhysicsMethods.Array = BaseMethods_js_1.default.Array;\nexports.default = PhysicsMethods;\n//# sourceMappingURL=PhysicsMethods.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SetOptionsConfiguration = exports.SetOptionsUtil = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar Symbol_js_1 = require(\"../Symbol.js\");\nvar BaseMethods_js_1 = __importDefault(require(\"../base/BaseMethods.js\"));\nvar Options_js_1 = require(\"../../../util/Options.js\");\nexports.SetOptionsUtil = {\n    filterPackage: function (parser, extension) {\n        if (extension !== 'tex' && !Configuration_js_1.ConfigurationHandler.get(extension)) {\n            throw new TexError_js_1.default('NotAPackage', 'Not a defined package: %1', extension);\n        }\n        var config = parser.options.setoptions;\n        var options = config.allowOptions[extension];\n        if ((options === undefined && !config.allowPackageDefault) || options === false) {\n            throw new TexError_js_1.default('PackageNotSettable', 'Options can\\'t be set for package \"%1\"', extension);\n        }\n        return true;\n    },\n    filterOption: function (parser, extension, option) {\n        var _a;\n        var config = parser.options.setoptions;\n        var options = config.allowOptions[extension] || {};\n        var allow = (options.hasOwnProperty(option) && !(0, Options_js_1.isObject)(options[option]) ? options[option] : null);\n        if (allow === false || (allow === null && !config.allowOptionsDefault)) {\n            throw new TexError_js_1.default('OptionNotSettable', 'Option \"%1\" is not allowed to be set', option);\n        }\n        if (!((_a = (extension === 'tex' ? parser.options : parser.options[extension])) === null || _a === void 0 ? void 0 : _a.hasOwnProperty(option))) {\n            if (extension === 'tex') {\n                throw new TexError_js_1.default('InvalidTexOption', 'Invalid TeX option \"%1\"', option);\n            }\n            else {\n                throw new TexError_js_1.default('InvalidOptionKey', 'Invalid option \"%1\" for package \"%2\"', option, extension);\n            }\n        }\n        return true;\n    },\n    filterValue: function (_parser, _extension, _option, value) {\n        return value;\n    }\n};\nvar setOptionsMap = new SymbolMap_js_1.CommandMap('setoptions', {\n    setOptions: 'SetOptions'\n}, {\n    SetOptions: function (parser, name) {\n        var e_1, _a;\n        var extension = parser.GetBrackets(name) || 'tex';\n        var options = ParseUtil_js_1.default.keyvalOptions(parser.GetArgument(name));\n        var config = parser.options.setoptions;\n        if (!config.filterPackage(parser, extension))\n            return;\n        try {\n            for (var _b = __values(Object.keys(options)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var key = _c.value;\n                if (config.filterOption(parser, extension, key)) {\n                    (extension === 'tex' ? parser.options : parser.options[extension])[key] =\n                        config.filterValue(parser, extension, key, options[key]);\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    }\n});\nfunction setoptionsConfig(_config, jax) {\n    var require = jax.parseOptions.handlers.get('macro').lookup('require');\n    if (require) {\n        setOptionsMap.add('Require', new Symbol_js_1.Macro('Require', require._func));\n        setOptionsMap.add('require', new Symbol_js_1.Macro('require', BaseMethods_js_1.default.Macro, ['\\\\Require{#2}\\\\setOptions[#2]{#1}', 2, '']));\n    }\n}\nexports.SetOptionsConfiguration = Configuration_js_1.Configuration.create('setoptions', {\n    handler: { macro: ['setoptions'] },\n    config: setoptionsConfig,\n    priority: 3,\n    options: {\n        setoptions: {\n            filterPackage: exports.SetOptionsUtil.filterPackage,\n            filterOption: exports.SetOptionsUtil.filterOption,\n            filterValue: exports.SetOptionsUtil.filterValue,\n            allowPackageDefault: true,\n            allowOptionsDefault: true,\n            allowOptions: (0, Options_js_1.expandable)({\n                tex: {\n                    FindTeX: false,\n                    formatError: false,\n                    package: false,\n                    baseURL: false,\n                    tags: false,\n                    maxBuffer: false,\n                    maxMaxros: false,\n                    macros: false,\n                    environments: false\n                },\n                setoptions: false,\n                autoload: false,\n                require: false,\n                configmacros: false,\n                tagformat: false\n            })\n        }\n    }\n});\n//# sourceMappingURL=SetOptionsConfiguration.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TagFormatConfiguration = exports.tagformatConfig = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar Tags_js_1 = require(\"../Tags.js\");\nvar tagID = 0;\nfunction tagformatConfig(config, jax) {\n    var tags = jax.parseOptions.options.tags;\n    if (tags !== 'base' && config.tags.hasOwnProperty(tags)) {\n        Tags_js_1.TagsFactory.add(tags, config.tags[tags]);\n    }\n    var TagClass = Tags_js_1.TagsFactory.create(jax.parseOptions.options.tags).constructor;\n    var TagFormat = (function (_super) {\n        __extends(TagFormat, _super);\n        function TagFormat() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        TagFormat.prototype.formatNumber = function (n) {\n            return jax.parseOptions.options.tagformat.number(n);\n        };\n        TagFormat.prototype.formatTag = function (tag) {\n            return jax.parseOptions.options.tagformat.tag(tag);\n        };\n        TagFormat.prototype.formatId = function (id) {\n            return jax.parseOptions.options.tagformat.id(id);\n        };\n        TagFormat.prototype.formatUrl = function (id, base) {\n            return jax.parseOptions.options.tagformat.url(id, base);\n        };\n        return TagFormat;\n    }(TagClass));\n    tagID++;\n    var tagName = 'configTags-' + tagID;\n    Tags_js_1.TagsFactory.add(tagName, TagFormat);\n    jax.parseOptions.options.tags = tagName;\n}\nexports.tagformatConfig = tagformatConfig;\nexports.TagFormatConfiguration = Configuration_js_1.Configuration.create('tagformat', {\n    config: [tagformatConfig, 10],\n    options: {\n        tagformat: {\n            number: function (n) { return n.toString(); },\n            tag: function (tag) { return '(' + tag + ')'; },\n            id: function (id) { return 'mjx-eqn:' + id.replace(/\\s/g, '_'); },\n            url: function (id, base) { return base + '#' + encodeURIComponent(id); },\n        }\n    }\n});\n//# sourceMappingURL=TagFormatConfiguration.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TextcompConfiguration = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nrequire(\"./TextcompMappings.js\");\nexports.TextcompConfiguration = Configuration_js_1.Configuration.create('textcomp', {\n    handler: { macro: ['textcomp-macros'] }\n});\n//# sourceMappingURL=TextcompConfiguration.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar TexConstants_js_1 = require(\"../TexConstants.js\");\nvar TextMacrosMethods_js_1 = require(\"../textmacros/TextMacrosMethods.js\");\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar TextParser_js_1 = require(\"../textmacros/TextParser.js\");\nnew SymbolMap_js_1.CommandMap('textcomp-macros', {\n    'textasciicircum': ['Insert', '\\u005E'],\n    'textasciitilde': ['Insert', '\\u007E'],\n    'textasteriskcentered': ['Insert', '\\u002A'],\n    'textbackslash': ['Insert', '\\u005C'],\n    'textbar': ['Insert', '\\u007C'],\n    'textbraceleft': ['Insert', '\\u007B'],\n    'textbraceright': ['Insert', '\\u007D'],\n    'textbullet': ['Insert', '\\u2022'],\n    'textdagger': ['Insert', '\\u2020'],\n    'textdaggerdbl': ['Insert', '\\u2021'],\n    'textellipsis': ['Insert', '\\u2026'],\n    'textemdash': ['Insert', '\\u2014'],\n    'textendash': ['Insert', '\\u2013'],\n    'textexclamdown': ['Insert', '\\u00A1'],\n    'textgreater': ['Insert', '\\u003E'],\n    'textless': ['Insert', '\\u003C'],\n    'textordfeminine': ['Insert', '\\u00AA'],\n    'textordmasculine': ['Insert', '\\u00BA'],\n    'textparagraph': ['Insert', '\\u00B6'],\n    'textperiodcentered': ['Insert', '\\u00B7'],\n    'textquestiondown': ['Insert', '\\u00BF'],\n    'textquotedblleft': ['Insert', '\\u201C'],\n    'textquotedblright': ['Insert', '\\u201D'],\n    'textquoteleft': ['Insert', '\\u2018'],\n    'textquoteright': ['Insert', '\\u2019'],\n    'textsection': ['Insert', '\\u00A7'],\n    'textunderscore': ['Insert', '\\u005F'],\n    'textvisiblespace': ['Insert', '\\u2423'],\n    'textacutedbl': ['Insert', '\\u02DD'],\n    'textasciiacute': ['Insert', '\\u00B4'],\n    'textasciibreve': ['Insert', '\\u02D8'],\n    'textasciicaron': ['Insert', '\\u02C7'],\n    'textasciidieresis': ['Insert', '\\u00A8'],\n    'textasciimacron': ['Insert', '\\u00AF'],\n    'textgravedbl': ['Insert', '\\u02F5'],\n    'texttildelow': ['Insert', '\\u02F7'],\n    'textbaht': ['Insert', '\\u0E3F'],\n    'textcent': ['Insert', '\\u00A2'],\n    'textcolonmonetary': ['Insert', '\\u20A1'],\n    'textcurrency': ['Insert', '\\u00A4'],\n    'textdollar': ['Insert', '\\u0024'],\n    'textdong': ['Insert', '\\u20AB'],\n    'texteuro': ['Insert', '\\u20AC'],\n    'textflorin': ['Insert', '\\u0192'],\n    'textguarani': ['Insert', '\\u20B2'],\n    'textlira': ['Insert', '\\u20A4'],\n    'textnaira': ['Insert', '\\u20A6'],\n    'textpeso': ['Insert', '\\u20B1'],\n    'textsterling': ['Insert', '\\u00A3'],\n    'textwon': ['Insert', '\\u20A9'],\n    'textyen': ['Insert', '\\u00A5'],\n    'textcircledP': ['Insert', '\\u2117'],\n    'textcompwordmark': ['Insert', '\\u200C'],\n    'textcopyleft': ['Insert', \"\\uD83C\\uDD2F\"],\n    'textcopyright': ['Insert', '\\u00A9'],\n    'textregistered': ['Insert', '\\u00AE'],\n    'textservicemark': ['Insert', '\\u2120'],\n    'texttrademark': ['Insert', '\\u2122'],\n    'textbardbl': ['Insert', '\\u2016'],\n    'textbigcircle': ['Insert', '\\u25EF'],\n    'textblank': ['Insert', '\\u2422'],\n    'textbrokenbar': ['Insert', '\\u00A6'],\n    'textdiscount': ['Insert', '\\u2052'],\n    'textestimated': ['Insert', '\\u212E'],\n    'textinterrobang': ['Insert', '\\u203D'],\n    'textinterrobangdown': ['Insert', '\\u2E18'],\n    'textmusicalnote': ['Insert', '\\u266A'],\n    'textnumero': ['Insert', '\\u2116'],\n    'textopenbullet': ['Insert', '\\u25E6'],\n    'textpertenthousand': ['Insert', '\\u2031'],\n    'textperthousand': ['Insert', '\\u2030'],\n    'textrecipe': ['Insert', '\\u211E'],\n    'textreferencemark': ['Insert', '\\u203B'],\n    'textlangle': ['Insert', '\\u2329'],\n    'textrangle': ['Insert', '\\u232A'],\n    'textlbrackdbl': ['Insert', '\\u27E6'],\n    'textrbrackdbl': ['Insert', '\\u27E7'],\n    'textlquill': ['Insert', '\\u2045'],\n    'textrquill': ['Insert', '\\u2046'],\n    'textcelsius': ['Insert', '\\u2103'],\n    'textdegree': ['Insert', '\\u00B0'],\n    'textdiv': ['Insert', '\\u00F7'],\n    'textdownarrow': ['Insert', '\\u2193'],\n    'textfractionsolidus': ['Insert', '\\u2044'],\n    'textleftarrow': ['Insert', '\\u2190'],\n    'textlnot': ['Insert', '\\u00AC'],\n    'textmho': ['Insert', '\\u2127'],\n    'textminus': ['Insert', '\\u2212'],\n    'textmu': ['Insert', '\\u00B5'],\n    'textohm': ['Insert', '\\u2126'],\n    'textonehalf': ['Insert', '\\u00BD'],\n    'textonequarter': ['Insert', '\\u00BC'],\n    'textonesuperior': ['Insert', '\\u00B9'],\n    'textpm': ['Insert', '\\u00B1'],\n    'textrightarrow': ['Insert', '\\u2192'],\n    'textsurd': ['Insert', '\\u221A'],\n    'textthreequarters': ['Insert', '\\u00BE'],\n    'textthreesuperior': ['Insert', '\\u00B3'],\n    'texttimes': ['Insert', '\\u00D7'],\n    'texttwosuperior': ['Insert', '\\u00B2'],\n    'textuparrow': ['Insert', '\\u2191'],\n    'textborn': ['Insert', '\\u002A'],\n    'textdied': ['Insert', '\\u2020'],\n    'textdivorced': ['Insert', '\\u26AE'],\n    'textmarried': ['Insert', '\\u26AD'],\n    'textcentoldstyle': ['Insert', '\\u00A2', TexConstants_js_1.TexConstant.Variant.OLDSTYLE],\n    'textdollaroldstyle': ['Insert', '\\u0024', TexConstants_js_1.TexConstant.Variant.OLDSTYLE],\n    'textzerooldstyle': ['Insert', '0', TexConstants_js_1.TexConstant.Variant.OLDSTYLE],\n    'textoneoldstyle': ['Insert', '1', TexConstants_js_1.TexConstant.Variant.OLDSTYLE],\n    'texttwooldstyle': ['Insert', '2', TexConstants_js_1.TexConstant.Variant.OLDSTYLE],\n    'textthreeoldstyle': ['Insert', '3', TexConstants_js_1.TexConstant.Variant.OLDSTYLE],\n    'textfouroldstyle': ['Insert', '4', TexConstants_js_1.TexConstant.Variant.OLDSTYLE],\n    'textfiveoldstyle': ['Insert', '5', TexConstants_js_1.TexConstant.Variant.OLDSTYLE],\n    'textsixoldstyle': ['Insert', '6', TexConstants_js_1.TexConstant.Variant.OLDSTYLE],\n    'textsevenoldstyle': ['Insert', '7', TexConstants_js_1.TexConstant.Variant.OLDSTYLE],\n    'texteightoldstyle': ['Insert', '8', TexConstants_js_1.TexConstant.Variant.OLDSTYLE],\n    'textnineoldstyle': ['Insert', '9', TexConstants_js_1.TexConstant.Variant.OLDSTYLE]\n}, {\n    Insert: function (parser, name, c, font) {\n        if (parser instanceof TextParser_js_1.TextParser) {\n            if (!font) {\n                TextMacrosMethods_js_1.TextMacrosMethods.Insert(parser, name, c);\n                return;\n            }\n            parser.saveText();\n        }\n        parser.Push(ParseUtil_js_1.default.internalText(parser, c, font ? { mathvariant: font } : {}));\n    }\n});\n//# sourceMappingURL=TextcompMappings.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TextMacrosConfiguration = exports.TextBaseConfiguration = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar ParseOptions_js_1 = __importDefault(require(\"../ParseOptions.js\"));\nvar Tags_js_1 = require(\"../Tags.js\");\nvar BaseItems_js_1 = require(\"../base/BaseItems.js\");\nvar TextParser_js_1 = require(\"./TextParser.js\");\nvar TextMacrosMethods_js_1 = require(\"./TextMacrosMethods.js\");\nrequire(\"./TextMacrosMappings.js\");\nexports.TextBaseConfiguration = Configuration_js_1.Configuration.create('text-base', {\n    parser: 'text',\n    handler: {\n        character: ['command', 'text-special'],\n        macro: ['text-macros']\n    },\n    fallback: {\n        character: function (parser, c) {\n            parser.text += c;\n        },\n        macro: function (parser, name) {\n            var texParser = parser.texParser;\n            var macro = texParser.lookup('macro', name);\n            if (macro && macro._func !== TextMacrosMethods_js_1.TextMacrosMethods.Macro) {\n                parser.Error('MathMacro', '%1 is only supported in math mode', '\\\\' + name);\n            }\n            texParser.parse('macro', [parser, name]);\n        }\n    },\n    items: (_a = {},\n        _a[BaseItems_js_1.StartItem.prototype.kind] = BaseItems_js_1.StartItem,\n        _a[BaseItems_js_1.StopItem.prototype.kind] = BaseItems_js_1.StopItem,\n        _a[BaseItems_js_1.MmlItem.prototype.kind] = BaseItems_js_1.MmlItem,\n        _a[BaseItems_js_1.StyleItem.prototype.kind] = BaseItems_js_1.StyleItem,\n        _a)\n});\nfunction internalMath(parser, text, level, mathvariant) {\n    var config = parser.configuration.packageData.get('textmacros');\n    if (!(parser instanceof TextParser_js_1.TextParser)) {\n        config.texParser = parser;\n    }\n    return [(new TextParser_js_1.TextParser(text, mathvariant ? { mathvariant: mathvariant } : {}, config.parseOptions, level)).mml()];\n}\nexports.TextMacrosConfiguration = Configuration_js_1.Configuration.create('textmacros', {\n    config: function (_config, jax) {\n        var textConf = new Configuration_js_1.ParserConfiguration(jax.parseOptions.options.textmacros.packages, ['tex', 'text']);\n        textConf.init();\n        var parseOptions = new ParseOptions_js_1.default(textConf, []);\n        parseOptions.options = jax.parseOptions.options;\n        textConf.config(jax);\n        Tags_js_1.TagsFactory.addTags(textConf.tags);\n        parseOptions.tags = Tags_js_1.TagsFactory.getDefault();\n        parseOptions.tags.configuration = parseOptions;\n        parseOptions.packageData = jax.parseOptions.packageData;\n        parseOptions.packageData.set('textmacros', { parseOptions: parseOptions, jax: jax, texParser: null });\n        parseOptions.options.internalMath = internalMath;\n    },\n    preprocessors: [function (data) {\n            var config = data.data.packageData.get('textmacros');\n            config.parseOptions.nodeFactory.setMmlFactory(config.jax.mmlFactory);\n        }],\n    options: {\n        textmacros: {\n            packages: ['text-base']\n        }\n    }\n});\n//# sourceMappingURL=TextMacrosConfiguration.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar TexConstants_js_1 = require(\"../TexConstants.js\");\nvar TextMacrosMethods_js_1 = require(\"./TextMacrosMethods.js\");\nvar lengths_js_1 = require(\"../../../util/lengths.js\");\nnew SymbolMap_js_1.MacroMap('text-special', {\n    '$': 'Math',\n    '%': 'Comment',\n    '^': 'MathModeOnly',\n    '_': 'MathModeOnly',\n    '&': 'Misplaced',\n    '#': 'Misplaced',\n    '~': 'Tilde',\n    ' ': 'Space',\n    '\\t': 'Space',\n    '\\r': 'Space',\n    '\\n': 'Space',\n    '\\u00A0': 'Tilde',\n    '{': 'OpenBrace',\n    '}': 'CloseBrace',\n    '`': 'OpenQuote',\n    '\\'': 'CloseQuote'\n}, TextMacrosMethods_js_1.TextMacrosMethods);\nnew SymbolMap_js_1.CommandMap('text-macros', {\n    '(': 'Math',\n    '$': 'SelfQuote',\n    '_': 'SelfQuote',\n    '%': 'SelfQuote',\n    '{': 'SelfQuote',\n    '}': 'SelfQuote',\n    ' ': 'SelfQuote',\n    '&': 'SelfQuote',\n    '#': 'SelfQuote',\n    '\\\\': 'SelfQuote',\n    '\\'': ['Accent', '\\u00B4'],\n    '\\u2019': ['Accent', '\\u00B4'],\n    '`': ['Accent', '\\u0060'],\n    '\\u2018': ['Accent', '\\u0060'],\n    '^': ['Accent', '^'],\n    '\\\"': ['Accent', '\\u00A8'],\n    '~': ['Accent', '~'],\n    '=': ['Accent', '\\u00AF'],\n    '.': ['Accent', '\\u02D9'],\n    'u': ['Accent', '\\u02D8'],\n    'v': ['Accent', '\\u02C7'],\n    emph: 'Emph',\n    rm: ['SetFont', TexConstants_js_1.TexConstant.Variant.NORMAL],\n    mit: ['SetFont', TexConstants_js_1.TexConstant.Variant.ITALIC],\n    oldstyle: ['SetFont', TexConstants_js_1.TexConstant.Variant.OLDSTYLE],\n    cal: ['SetFont', TexConstants_js_1.TexConstant.Variant.CALLIGRAPHIC],\n    it: ['SetFont', '-tex-mathit'],\n    bf: ['SetFont', TexConstants_js_1.TexConstant.Variant.BOLD],\n    bbFont: ['SetFont', TexConstants_js_1.TexConstant.Variant.DOUBLESTRUCK],\n    scr: ['SetFont', TexConstants_js_1.TexConstant.Variant.SCRIPT],\n    frak: ['SetFont', TexConstants_js_1.TexConstant.Variant.FRAKTUR],\n    sf: ['SetFont', TexConstants_js_1.TexConstant.Variant.SANSSERIF],\n    tt: ['SetFont', TexConstants_js_1.TexConstant.Variant.MONOSPACE],\n    tiny: ['SetSize', 0.5],\n    Tiny: ['SetSize', 0.6],\n    scriptsize: ['SetSize', 0.7],\n    small: ['SetSize', 0.85],\n    normalsize: ['SetSize', 1.0],\n    large: ['SetSize', 1.2],\n    Large: ['SetSize', 1.44],\n    LARGE: ['SetSize', 1.73],\n    huge: ['SetSize', 2.07],\n    Huge: ['SetSize', 2.49],\n    Bbb: ['Macro', '{\\\\bbFont #1}', 1],\n    textnormal: ['Macro', '{\\\\rm #1}', 1],\n    textup: ['Macro', '{\\\\rm #1}', 1],\n    textrm: ['Macro', '{\\\\rm #1}', 1],\n    textit: ['Macro', '{\\\\it #1}', 1],\n    textbf: ['Macro', '{\\\\bf #1}', 1],\n    textsf: ['Macro', '{\\\\sf #1}', 1],\n    texttt: ['Macro', '{\\\\tt #1}', 1],\n    dagger: ['Insert', '\\u2020'],\n    ddagger: ['Insert', '\\u2021'],\n    S: ['Insert', '\\u00A7'],\n    ',': ['Spacer', lengths_js_1.MATHSPACE.thinmathspace],\n    ':': ['Spacer', lengths_js_1.MATHSPACE.mediummathspace],\n    '>': ['Spacer', lengths_js_1.MATHSPACE.mediummathspace],\n    ';': ['Spacer', lengths_js_1.MATHSPACE.thickmathspace],\n    '!': ['Spacer', lengths_js_1.MATHSPACE.negativethinmathspace],\n    enspace: ['Spacer', .5],\n    quad: ['Spacer', 1],\n    qquad: ['Spacer', 2],\n    thinspace: ['Spacer', lengths_js_1.MATHSPACE.thinmathspace],\n    negthinspace: ['Spacer', lengths_js_1.MATHSPACE.negativethinmathspace],\n    hskip: 'Hskip',\n    hspace: 'Hskip',\n    kern: 'Hskip',\n    mskip: 'Hskip',\n    mspace: 'Hskip',\n    mkern: 'Hskip',\n    rule: 'rule',\n    Rule: ['Rule'],\n    Space: ['Rule', 'blank'],\n    color: 'CheckAutoload',\n    textcolor: 'CheckAutoload',\n    colorbox: 'CheckAutoload',\n    fcolorbox: 'CheckAutoload',\n    href: 'CheckAutoload',\n    style: 'CheckAutoload',\n    class: 'CheckAutoload',\n    cssId: 'CheckAutoload',\n    unicode: 'CheckAutoload',\n    ref: ['HandleRef', false],\n    eqref: ['HandleRef', true],\n}, TextMacrosMethods_js_1.TextMacrosMethods);\n//# sourceMappingURL=TextMacrosMappings.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TextMacrosMethods = void 0;\nvar TexParser_js_1 = __importDefault(require(\"../TexParser.js\"));\nvar Retries_js_1 = require(\"../../../util/Retries.js\");\nvar BaseMethods_js_1 = __importDefault(require(\"../base/BaseMethods.js\"));\nexports.TextMacrosMethods = {\n    Comment: function (parser, _c) {\n        while (parser.i < parser.string.length && parser.string.charAt(parser.i) !== '\\n') {\n            parser.i++;\n        }\n        parser.i++;\n    },\n    Math: function (parser, open) {\n        parser.saveText();\n        var i = parser.i;\n        var j, c;\n        var braces = 0;\n        while ((c = parser.GetNext())) {\n            j = parser.i++;\n            switch (c) {\n                case '\\\\':\n                    var cs = parser.GetCS();\n                    if (cs === ')')\n                        c = '\\\\(';\n                case '$':\n                    if (braces === 0 && open === c) {\n                        var config = parser.texParser.configuration;\n                        var mml = (new TexParser_js_1.default(parser.string.substr(i, j - i), parser.stack.env, config)).mml();\n                        parser.PushMath(mml);\n                        return;\n                    }\n                    break;\n                case '{':\n                    braces++;\n                    break;\n                case '}':\n                    if (braces === 0) {\n                        parser.Error('ExtraCloseMissingOpen', 'Extra close brace or missing open brace');\n                    }\n                    braces--;\n                    break;\n            }\n        }\n        parser.Error('MathNotTerminated', 'Math-mode is not properly terminated');\n    },\n    MathModeOnly: function (parser, c) {\n        parser.Error('MathModeOnly', '\\'%1\\' allowed only in math mode', c);\n    },\n    Misplaced: function (parser, c) {\n        parser.Error('Misplaced', '\\'%1\\' can not be used here', c);\n    },\n    OpenBrace: function (parser, _c) {\n        var env = parser.stack.env;\n        parser.envStack.push(env);\n        parser.stack.env = Object.assign({}, env);\n    },\n    CloseBrace: function (parser, _c) {\n        if (parser.envStack.length) {\n            parser.saveText();\n            parser.stack.env = parser.envStack.pop();\n        }\n        else {\n            parser.Error('ExtraCloseMissingOpen', 'Extra close brace or missing open brace');\n        }\n    },\n    OpenQuote: function (parser, c) {\n        if (parser.string.charAt(parser.i) === c) {\n            parser.text += '\\u201C';\n            parser.i++;\n        }\n        else {\n            parser.text += '\\u2018';\n        }\n    },\n    CloseQuote: function (parser, c) {\n        if (parser.string.charAt(parser.i) === c) {\n            parser.text += '\\u201D';\n            parser.i++;\n        }\n        else {\n            parser.text += '\\u2019';\n        }\n    },\n    Tilde: function (parser, _c) {\n        parser.text += '\\u00A0';\n    },\n    Space: function (parser, _c) {\n        parser.text += ' ';\n        while (parser.GetNext().match(/\\s/))\n            parser.i++;\n    },\n    SelfQuote: function (parser, name) {\n        parser.text += name.substr(1);\n    },\n    Insert: function (parser, _name, c) {\n        parser.text += c;\n    },\n    Accent: function (parser, name, c) {\n        var base = parser.ParseArg(name);\n        var accent = parser.create('token', 'mo', {}, c);\n        parser.addAttributes(accent);\n        parser.Push(parser.create('node', 'mover', [base, accent]));\n    },\n    Emph: function (parser, name) {\n        var variant = (parser.stack.env.mathvariant === '-tex-mathit' ? 'normal' : '-tex-mathit');\n        parser.Push(parser.ParseTextArg(name, { mathvariant: variant }));\n    },\n    SetFont: function (parser, _name, variant) {\n        parser.saveText();\n        parser.stack.env.mathvariant = variant;\n    },\n    SetSize: function (parser, _name, size) {\n        parser.saveText();\n        parser.stack.env.mathsize = size;\n    },\n    CheckAutoload: function (parser, name) {\n        var autoload = parser.configuration.packageData.get('autoload');\n        var texParser = parser.texParser;\n        name = name.slice(1);\n        var macro = texParser.lookup('macro', name);\n        if (!macro || (autoload && macro._func === autoload.Autoload)) {\n            texParser.parse('macro', [texParser, name]);\n            if (!macro)\n                return;\n            (0, Retries_js_1.retryAfter)(Promise.resolve());\n        }\n        texParser.parse('macro', [parser, name]);\n    },\n    Macro: BaseMethods_js_1.default.Macro,\n    Spacer: BaseMethods_js_1.default.Spacer,\n    Hskip: BaseMethods_js_1.default.Hskip,\n    rule: BaseMethods_js_1.default.rule,\n    Rule: BaseMethods_js_1.default.Rule,\n    HandleRef: BaseMethods_js_1.default.HandleRef\n};\n//# sourceMappingURL=TextMacrosMethods.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TextParser = void 0;\nvar TexParser_js_1 = __importDefault(require(\"../TexParser.js\"));\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar BaseItems_js_1 = require(\"../base/BaseItems.js\");\nvar TextParser = (function (_super) {\n    __extends(TextParser, _super);\n    function TextParser(text, env, configuration, level) {\n        var _this = _super.call(this, text, env, configuration) || this;\n        _this.level = level;\n        return _this;\n    }\n    Object.defineProperty(TextParser.prototype, \"texParser\", {\n        get: function () {\n            return this.configuration.packageData.get('textmacros').texParser;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(TextParser.prototype, \"tags\", {\n        get: function () {\n            return this.texParser.tags;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    TextParser.prototype.mml = function () {\n        return (this.level != null ?\n            this.create('node', 'mstyle', this.nodes, { displaystyle: false, scriptlevel: this.level }) :\n            this.nodes.length === 1 ? this.nodes[0] : this.create('node', 'mrow', this.nodes));\n    };\n    TextParser.prototype.Parse = function () {\n        this.text = '';\n        this.nodes = [];\n        this.envStack = [];\n        _super.prototype.Parse.call(this);\n    };\n    TextParser.prototype.saveText = function () {\n        if (this.text) {\n            var mathvariant = this.stack.env.mathvariant;\n            var text = ParseUtil_js_1.default.internalText(this, this.text, mathvariant ? { mathvariant: mathvariant } : {});\n            this.text = '';\n            this.Push(text);\n        }\n    };\n    TextParser.prototype.Push = function (mml) {\n        if (this.text) {\n            this.saveText();\n        }\n        if (mml instanceof BaseItems_js_1.StopItem) {\n            return _super.prototype.Push.call(this, mml);\n        }\n        if (mml instanceof BaseItems_js_1.StyleItem) {\n            this.stack.env.mathcolor = this.stack.env.color;\n            return;\n        }\n        if (mml instanceof MmlNode_js_1.AbstractMmlNode) {\n            this.addAttributes(mml);\n            this.nodes.push(mml);\n        }\n    };\n    TextParser.prototype.PushMath = function (mml) {\n        var e_1, _a;\n        var env = this.stack.env;\n        if (!mml.isKind('TeXAtom')) {\n            mml = this.create('node', 'TeXAtom', [mml]);\n        }\n        try {\n            for (var _b = __values(['mathsize', 'mathcolor']), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var name_1 = _c.value;\n                if (env[name_1] && !mml.attributes.getExplicit(name_1)) {\n                    if (!mml.isToken && !mml.isKind('mstyle')) {\n                        mml = this.create('node', 'mstyle', [mml]);\n                    }\n                    NodeUtil_js_1.default.setAttribute(mml, name_1, env[name_1]);\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        if (mml.isInferred) {\n            mml = this.create('node', 'mrow', mml.childNodes);\n        }\n        this.nodes.push(mml);\n    };\n    TextParser.prototype.addAttributes = function (mml) {\n        var e_2, _a;\n        var env = this.stack.env;\n        if (!mml.isToken)\n            return;\n        try {\n            for (var _b = __values(['mathsize', 'mathcolor', 'mathvariant']), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var name_2 = _c.value;\n                if (env[name_2] && !mml.attributes.getExplicit(name_2)) {\n                    NodeUtil_js_1.default.setAttribute(mml, name_2, env[name_2]);\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    };\n    TextParser.prototype.ParseTextArg = function (name, env) {\n        var text = this.GetArgument(name);\n        env = Object.assign(Object.assign({}, this.stack.env), env);\n        return (new TextParser(text, env, this.configuration)).mml();\n    };\n    TextParser.prototype.ParseArg = function (name) {\n        return (new TextParser(this.GetArgument(name), this.stack.env, this.configuration)).mml();\n    };\n    TextParser.prototype.Error = function (id, message) {\n        var args = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            args[_i - 2] = arguments[_i];\n        }\n        throw new (TexError_js_1.default.bind.apply(TexError_js_1.default, __spreadArray([void 0, id, message], __read(args), false)))();\n    };\n    return TextParser;\n}(TexParser_js_1.default));\nexports.TextParser = TextParser;\n//# sourceMappingURL=TextParser.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.UnicodeConfiguration = exports.UnicodeMethods = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar Entities_js_1 = require(\"../../../util/Entities.js\");\nexports.UnicodeMethods = {};\nvar UnicodeCache = {};\nexports.UnicodeMethods.Unicode = function (parser, name) {\n    var HD = parser.GetBrackets(name);\n    var HDsplit = null;\n    var font = null;\n    if (HD) {\n        if (HD.replace(/ /g, '').\n            match(/^(\\d+(\\.\\d*)?|\\.\\d+),(\\d+(\\.\\d*)?|\\.\\d+)$/)) {\n            HDsplit = HD.replace(/ /g, '').split(/,/);\n            font = parser.GetBrackets(name);\n        }\n        else {\n            font = HD;\n        }\n    }\n    var n = ParseUtil_js_1.default.trimSpaces(parser.GetArgument(name)).replace(/^0x/, 'x');\n    if (!n.match(/^(x[0-9A-Fa-f]+|[0-9]+)$/)) {\n        throw new TexError_js_1.default('BadUnicode', 'Argument to \\\\unicode must be a number');\n    }\n    var N = parseInt(n.match(/^x/) ? '0' + n : n);\n    if (!UnicodeCache[N]) {\n        UnicodeCache[N] = [800, 200, font, N];\n    }\n    else if (!font) {\n        font = UnicodeCache[N][2];\n    }\n    if (HDsplit) {\n        UnicodeCache[N][0] = Math.floor(parseFloat(HDsplit[0]) * 1000);\n        UnicodeCache[N][1] = Math.floor(parseFloat(HDsplit[1]) * 1000);\n    }\n    var variant = parser.stack.env.font;\n    var def = {};\n    if (font) {\n        UnicodeCache[N][2] = def.fontfamily = font.replace(/'/g, '\\'');\n        if (variant) {\n            if (variant.match(/bold/)) {\n                def.fontweight = 'bold';\n            }\n            if (variant.match(/italic|-mathit/)) {\n                def.fontstyle = 'italic';\n            }\n        }\n    }\n    else if (variant) {\n        def.mathvariant = variant;\n    }\n    var node = parser.create('token', 'mtext', def, (0, Entities_js_1.numeric)(n));\n    NodeUtil_js_1.default.setProperty(node, 'unicode', true);\n    parser.Push(node);\n};\nnew SymbolMap_js_1.CommandMap('unicode', { unicode: 'Unicode' }, exports.UnicodeMethods);\nexports.UnicodeConfiguration = Configuration_js_1.Configuration.create('unicode', { handler: { macro: ['unicode'] } });\n//# sourceMappingURL=UnicodeConfiguration.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.UpgreekConfiguration = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar TexConstants_js_1 = require(\"../TexConstants.js\");\nfunction mathchar0miNormal(parser, mchar) {\n    var def = mchar.attributes || {};\n    def.mathvariant = TexConstants_js_1.TexConstant.Variant.NORMAL;\n    var node = parser.create('token', 'mi', def, mchar.char);\n    parser.Push(node);\n}\nnew SymbolMap_js_1.CharacterMap('upgreek', mathchar0miNormal, {\n    upalpha: '\\u03B1',\n    upbeta: '\\u03B2',\n    upgamma: '\\u03B3',\n    updelta: '\\u03B4',\n    upepsilon: '\\u03F5',\n    upzeta: '\\u03B6',\n    upeta: '\\u03B7',\n    uptheta: '\\u03B8',\n    upiota: '\\u03B9',\n    upkappa: '\\u03BA',\n    uplambda: '\\u03BB',\n    upmu: '\\u03BC',\n    upnu: '\\u03BD',\n    upxi: '\\u03BE',\n    upomicron: '\\u03BF',\n    uppi: '\\u03C0',\n    uprho: '\\u03C1',\n    upsigma: '\\u03C3',\n    uptau: '\\u03C4',\n    upupsilon: '\\u03C5',\n    upphi: '\\u03D5',\n    upchi: '\\u03C7',\n    uppsi: '\\u03C8',\n    upomega: '\\u03C9',\n    upvarepsilon: '\\u03B5',\n    upvartheta: '\\u03D1',\n    upvarpi: '\\u03D6',\n    upvarrho: '\\u03F1',\n    upvarsigma: '\\u03C2',\n    upvarphi: '\\u03C6',\n    Upgamma: '\\u0393',\n    Updelta: '\\u0394',\n    Uptheta: '\\u0398',\n    Uplambda: '\\u039B',\n    Upxi: '\\u039E',\n    Uppi: '\\u03A0',\n    Upsigma: '\\u03A3',\n    Upupsilon: '\\u03A5',\n    Upphi: '\\u03A6',\n    Uppsi: '\\u03A8',\n    Upomega: '\\u03A9'\n});\nexports.UpgreekConfiguration = Configuration_js_1.Configuration.create('upgreek', {\n    handler: { macro: ['upgreek'] },\n});\n//# sourceMappingURL=UpgreekConfiguration.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.VerbConfiguration = exports.VerbMethods = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar TexConstants_js_1 = require(\"../TexConstants.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nexports.VerbMethods = {};\nexports.VerbMethods.Verb = function (parser, name) {\n    var c = parser.GetNext();\n    var start = ++parser.i;\n    if (c === '') {\n        throw new TexError_js_1.default('MissingArgFor', 'Missing argument for %1', name);\n    }\n    while (parser.i < parser.string.length &&\n        parser.string.charAt(parser.i) !== c) {\n        parser.i++;\n    }\n    if (parser.i === parser.string.length) {\n        throw new TexError_js_1.default('NoClosingDelim', 'Can\\'t find closing delimiter for %1', parser.currentCS);\n    }\n    var text = parser.string.slice(start, parser.i).replace(/ /g, '\\u00A0');\n    parser.i++;\n    parser.Push(parser.create('token', 'mtext', { mathvariant: TexConstants_js_1.TexConstant.Variant.MONOSPACE }, text));\n};\nnew SymbolMap_js_1.CommandMap('verb', { verb: 'Verb' }, exports.VerbMethods);\nexports.VerbConfiguration = Configuration_js_1.Configuration.create('verb', { handler: { macro: ['verb'] } });\n//# sourceMappingURL=VerbConfiguration.js.map", "\"use strict\";\n/*!\n *************************************************************************\n *\n *  mhchemParser.ts\n *  4.2.1\n *\n *  Parser for the \\ce command and \\pu command for MathJax and Co.\n *\n *  mhchem's \\ce is a tool for writing beautiful chemical equations easily.\n *  mhchem's \\pu is a tool for writing physical units easily.\n *\n *  ----------------------------------------------------------------------\n *\n *  Copyright (c) 2015-2023 <PERSON>\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n *\n *  ----------------------------------------------------------------------\n *\n *  https://github.com/mhchem/mhchemParser\n *\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mhchemParser = void 0;\nvar mhchemParser = (function () {\n    function mhchemParser() {\n    }\n    mhchemParser.toTex = function (input, type) {\n        return _mhchemTexify.go(_mhchemParser.go(input, type), type !== \"tex\");\n    };\n    return mhchemParser;\n}());\nexports.mhchemParser = mhchemParser;\nfunction _mhchemCreateTransitions(o) {\n    var pattern, state;\n    var transitions = {};\n    for (pattern in o) {\n        for (state in o[pattern]) {\n            var stateArray = state.split(\"|\");\n            o[pattern][state].stateArray = stateArray;\n            for (var i = 0; i < stateArray.length; i++) {\n                transitions[stateArray[i]] = [];\n            }\n        }\n    }\n    for (pattern in o) {\n        for (state in o[pattern]) {\n            var stateArray = o[pattern][state].stateArray || [];\n            for (var i = 0; i < stateArray.length; i++) {\n                var p = o[pattern][state];\n                p.action_ = [].concat(p.action_);\n                for (var k = 0; k < p.action_.length; k++) {\n                    if (typeof p.action_[k] === \"string\") {\n                        p.action_[k] = { type_: p.action_[k] };\n                    }\n                }\n                var patternArray = pattern.split(\"|\");\n                for (var j = 0; j < patternArray.length; j++) {\n                    if (stateArray[i] === '*') {\n                        var t = void 0;\n                        for (t in transitions) {\n                            transitions[t].push({ pattern: patternArray[j], task: p });\n                        }\n                    }\n                    else {\n                        transitions[stateArray[i]].push({ pattern: patternArray[j], task: p });\n                    }\n                }\n            }\n        }\n    }\n    return transitions;\n}\n;\nvar _mhchemParser = {\n    go: function (input, stateMachine) {\n        if (!input) {\n            return [];\n        }\n        if (stateMachine === undefined) {\n            stateMachine = 'ce';\n        }\n        var state = '0';\n        var buffer = {};\n        buffer['parenthesisLevel'] = 0;\n        input = input.replace(/\\n/g, \" \");\n        input = input.replace(/[\\u2212\\u2013\\u2014\\u2010]/g, \"-\");\n        input = input.replace(/[\\u2026]/g, \"...\");\n        var lastInput;\n        var watchdog = 10;\n        var output = [];\n        while (true) {\n            if (lastInput !== input) {\n                watchdog = 10;\n                lastInput = input;\n            }\n            else {\n                watchdog--;\n            }\n            var machine = _mhchemParser.stateMachines[stateMachine];\n            var t = machine.transitions[state] || machine.transitions['*'];\n            iterateTransitions: for (var i = 0; i < t.length; i++) {\n                var matches = _mhchemParser.patterns.match_(t[i].pattern, input);\n                if (matches) {\n                    var task = t[i].task;\n                    for (var iA = 0; iA < task.action_.length; iA++) {\n                        var o = void 0;\n                        if (machine.actions[task.action_[iA].type_]) {\n                            o = machine.actions[task.action_[iA].type_](buffer, matches.match_, task.action_[iA].option);\n                        }\n                        else if (_mhchemParser.actions[task.action_[iA].type_]) {\n                            o = _mhchemParser.actions[task.action_[iA].type_](buffer, matches.match_, task.action_[iA].option);\n                        }\n                        else {\n                            throw [\"MhchemBugA\", \"mhchem bug A. Please report. (\" + task.action_[iA].type_ + \")\"];\n                        }\n                        _mhchemParser.concatArray(output, o);\n                    }\n                    state = task.nextState || state;\n                    if (input.length > 0) {\n                        if (!task.revisit) {\n                            input = matches.remainder;\n                        }\n                        if (!task.toContinue) {\n                            break iterateTransitions;\n                        }\n                    }\n                    else {\n                        return output;\n                    }\n                }\n            }\n            if (watchdog <= 0) {\n                throw [\"MhchemBugU\", \"mhchem bug U. Please report.\"];\n            }\n        }\n    },\n    concatArray: function (a, b) {\n        if (b) {\n            if (Array.isArray(b)) {\n                for (var iB = 0; iB < b.length; iB++) {\n                    a.push(b[iB]);\n                }\n            }\n            else {\n                a.push(b);\n            }\n        }\n    },\n    patterns: {\n        patterns: {\n            'empty': /^$/,\n            'else': /^./,\n            'else2': /^./,\n            'space': /^\\s/,\n            'space A': /^\\s(?=[A-Z\\\\$])/,\n            'space$': /^\\s$/,\n            'a-z': /^[a-z]/,\n            'x': /^x/,\n            'x$': /^x$/,\n            'i$': /^i$/,\n            'letters': /^(?:[a-zA-Z\\u03B1-\\u03C9\\u0391-\\u03A9?@]|(?:\\\\(?:alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega|Gamma|Delta|Theta|Lambda|Xi|Pi|Sigma|Upsilon|Phi|Psi|Omega)(?:\\s+|\\{\\}|(?![a-zA-Z]))))+/,\n            '\\\\greek': /^\\\\(?:alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega|Gamma|Delta|Theta|Lambda|Xi|Pi|Sigma|Upsilon|Phi|Psi|Omega)(?:\\s+|\\{\\}|(?![a-zA-Z]))/,\n            'one lowercase latin letter $': /^(?:([a-z])(?:$|[^a-zA-Z]))$/,\n            '$one lowercase latin letter$ $': /^\\$(?:([a-z])(?:$|[^a-zA-Z]))\\$$/,\n            'one lowercase greek letter $': /^(?:\\$?[\\u03B1-\\u03C9]\\$?|\\$?\\\\(?:alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega)\\s*\\$?)(?:\\s+|\\{\\}|(?![a-zA-Z]))$/,\n            'digits': /^[0-9]+/,\n            '-9.,9': /^[+\\-]?(?:[0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\\.[0-9]+))/,\n            '-9.,9 no missing 0': /^[+\\-]?[0-9]+(?:[.,][0-9]+)?/,\n            '(-)(9.,9)(e)(99)': function (input) {\n                var match = input.match(/^(\\+\\-|\\+\\/\\-|\\+|\\-|\\\\pm\\s?)?([0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\\.[0-9]+))?(\\((?:[0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\\.[0-9]+))\\))?(?:(?:([eE])|\\s*(\\*|x|\\\\times|\\u00D7)\\s*10\\^)([+\\-]?[0-9]+|\\{[+\\-]?[0-9]+\\}))?/);\n                if (match && match[0]) {\n                    return { match_: match.slice(1), remainder: input.substr(match[0].length) };\n                }\n                return null;\n            },\n            '(-)(9)^(-9)': /^(\\+\\-|\\+\\/\\-|\\+|\\-|\\\\pm\\s?)?([0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\\.[0-9]+)?)\\^([+\\-]?[0-9]+|\\{[+\\-]?[0-9]+\\})/,\n            'state of aggregation $': function (input) {\n                var a = _mhchemParser.patterns.findObserveGroups(input, \"\", /^\\([a-z]{1,3}(?=[\\),])/, \")\", \"\");\n                if (a && a.remainder.match(/^($|[\\s,;\\)\\]\\}])/)) {\n                    return a;\n                }\n                var match = input.match(/^(?:\\((?:\\\\ca\\s?)?\\$[amothc]\\$\\))/);\n                if (match) {\n                    return { match_: match[0], remainder: input.substr(match[0].length) };\n                }\n                return null;\n            },\n            '_{(state of aggregation)}$': /^_\\{(\\([a-z]{1,3}\\))\\}/,\n            '{[(': /^(?:\\\\\\{|\\[|\\()/,\n            ')]}': /^(?:\\)|\\]|\\\\\\})/,\n            ', ': /^[,;]\\s*/,\n            ',': /^[,;]/,\n            '.': /^[.]/,\n            '. __* ': /^([.\\u22C5\\u00B7\\u2022]|[*])\\s*/,\n            '...': /^\\.\\.\\.(?=$|[^.])/,\n            '^{(...)}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"^{\", \"\", \"\", \"}\"); },\n            '^($...$)': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"^\", \"$\", \"$\", \"\"); },\n            '^a': /^\\^([0-9]+|[^\\\\_])/,\n            '^\\\\x{}{}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"^\", /^\\\\[a-zA-Z]+\\{/, \"}\", \"\", \"\", \"{\", \"}\", \"\", true); },\n            '^\\\\x{}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"^\", /^\\\\[a-zA-Z]+\\{/, \"}\", \"\"); },\n            '^\\\\x': /^\\^(\\\\[a-zA-Z]+)\\s*/,\n            '^(-1)': /^\\^(-?\\d+)/,\n            '\\'': /^'/,\n            '_{(...)}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"_{\", \"\", \"\", \"}\"); },\n            '_($...$)': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"_\", \"$\", \"$\", \"\"); },\n            '_9': /^_([+\\-]?[0-9]+|[^\\\\])/,\n            '_\\\\x{}{}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"_\", /^\\\\[a-zA-Z]+\\{/, \"}\", \"\", \"\", \"{\", \"}\", \"\", true); },\n            '_\\\\x{}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"_\", /^\\\\[a-zA-Z]+\\{/, \"}\", \"\"); },\n            '_\\\\x': /^_(\\\\[a-zA-Z]+)\\s*/,\n            '^_': /^(?:\\^(?=_)|\\_(?=\\^)|[\\^_]$)/,\n            '{}^': /^\\{\\}(?=\\^)/,\n            '{}': /^\\{\\}/,\n            '{...}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"\", \"{\", \"}\", \"\"); },\n            '{(...)}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"{\", \"\", \"\", \"}\"); },\n            '$...$': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"\", \"$\", \"$\", \"\"); },\n            '${(...)}$__$(...)$': function (input) {\n                return _mhchemParser.patterns.findObserveGroups(input, \"${\", \"\", \"\", \"}$\") || _mhchemParser.patterns.findObserveGroups(input, \"$\", \"\", \"\", \"$\");\n            },\n            '=<>': /^[=<>]/,\n            '#': /^[#\\u2261]/,\n            '+': /^\\+/,\n            '-$': /^-(?=[\\s_},;\\]/]|$|\\([a-z]+\\))/,\n            '-9': /^-(?=[0-9])/,\n            '- orbital overlap': /^-(?=(?:[spd]|sp)(?:$|[\\s,;\\)\\]\\}]))/,\n            '-': /^-/,\n            'pm-operator': /^(?:\\\\pm|\\$\\\\pm\\$|\\+-|\\+\\/-)/,\n            'operator': /^(?:\\+|(?:[\\-=<>]|<<|>>|\\\\approx|\\$\\\\approx\\$)(?=\\s|$|-?[0-9]))/,\n            'arrowUpDown': /^(?:v|\\(v\\)|\\^|\\(\\^\\))(?=$|[\\s,;\\)\\]\\}])/,\n            '\\\\bond{(...)}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"\\\\bond{\", \"\", \"\", \"}\"); },\n            '->': /^(?:<->|<-->|->|<-|<=>>|<<=>|<=>|[\\u2192\\u27F6\\u21CC])/,\n            'CMT': /^[CMT](?=\\[)/,\n            '[(...)]': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"[\", \"\", \"\", \"]\"); },\n            '1st-level escape': /^(&|\\\\\\\\|\\\\hline)\\s*/,\n            '\\\\,': /^(?:\\\\[,\\ ;:])/,\n            '\\\\x{}{}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"\", /^\\\\[a-zA-Z]+\\{/, \"}\", \"\", \"\", \"{\", \"}\", \"\", true); },\n            '\\\\x{}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"\", /^\\\\[a-zA-Z]+\\{/, \"}\", \"\"); },\n            '\\\\ca': /^\\\\ca(?:\\s+|(?![a-zA-Z]))/,\n            '\\\\x': /^(?:\\\\[a-zA-Z]+\\s*|\\\\[_&{}%])/,\n            'orbital': /^(?:[0-9]{1,2}[spdfgh]|[0-9]{0,2}sp)(?=$|[^a-zA-Z])/,\n            'others': /^[\\/~|]/,\n            '\\\\frac{(...)}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"\\\\frac{\", \"\", \"\", \"}\", \"{\", \"\", \"\", \"}\"); },\n            '\\\\overset{(...)}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"\\\\overset{\", \"\", \"\", \"}\", \"{\", \"\", \"\", \"}\"); },\n            '\\\\underset{(...)}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"\\\\underset{\", \"\", \"\", \"}\", \"{\", \"\", \"\", \"}\"); },\n            '\\\\underbrace{(...)}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"\\\\underbrace{\", \"\", \"\", \"}_\", \"{\", \"\", \"\", \"}\"); },\n            '\\\\color{(...)}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"\\\\color{\", \"\", \"\", \"}\"); },\n            '\\\\color{(...)}{(...)}': function (input) {\n                return _mhchemParser.patterns.findObserveGroups(input, \"\\\\color{\", \"\", \"\", \"}\", \"{\", \"\", \"\", \"}\") ||\n                    _mhchemParser.patterns.findObserveGroups(input, \"\\\\color\", \"\\\\\", \"\", /^(?=\\{)/, \"{\", \"\", \"\", \"}\");\n            },\n            '\\\\ce{(...)}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"\\\\ce{\", \"\", \"\", \"}\"); },\n            '\\\\pu{(...)}': function (input) { return _mhchemParser.patterns.findObserveGroups(input, \"\\\\pu{\", \"\", \"\", \"}\"); },\n            'oxidation$': /^(?:[+-][IVX]+|(?:\\\\pm|\\$\\\\pm\\$|\\+-|\\+\\/-)\\s*0)$/,\n            'd-oxidation$': /^(?:[+-]?[IVX]+|(?:\\\\pm|\\$\\\\pm\\$|\\+-|\\+\\/-)\\s*0)$/,\n            '1/2$': /^[+\\-]?(?:[0-9]+|\\$[a-z]\\$|[a-z])\\/[0-9]+(?:\\$[a-z]\\$|[a-z])?$/,\n            'amount': function (input) {\n                var match;\n                match = input.match(/^(?:(?:(?:\\([+\\-]?[0-9]+\\/[0-9]+\\)|[+\\-]?(?:[0-9]+|\\$[a-z]\\$|[a-z])\\/[0-9]+|[+\\-]?[0-9]+[.,][0-9]+|[+\\-]?\\.[0-9]+|[+\\-]?[0-9]+)(?:[a-z](?=\\s*[A-Z]))?)|[+\\-]?[a-z](?=\\s*[A-Z])|\\+(?!\\s))/);\n                if (match) {\n                    return { match_: match[0], remainder: input.substr(match[0].length) };\n                }\n                var a = _mhchemParser.patterns.findObserveGroups(input, \"\", \"$\", \"$\", \"\");\n                if (a) {\n                    match = a.match_.match(/^\\$(?:\\(?[+\\-]?(?:[0-9]*[a-z]?[+\\-])?[0-9]*[a-z](?:[+\\-][0-9]*[a-z]?)?\\)?|\\+|-)\\$$/);\n                    if (match) {\n                        return { match_: match[0], remainder: input.substr(match[0].length) };\n                    }\n                }\n                return null;\n            },\n            'amount2': function (input) { return this['amount'](input); },\n            '(KV letters),': /^(?:[A-Z][a-z]{0,2}|i)(?=,)/,\n            'formula$': function (input) {\n                if (input.match(/^\\([a-z]+\\)$/)) {\n                    return null;\n                }\n                var match = input.match(/^(?:[a-z]|(?:[0-9\\ \\+\\-\\,\\.\\(\\)]+[a-z])+[0-9\\ \\+\\-\\,\\.\\(\\)]*|(?:[a-z][0-9\\ \\+\\-\\,\\.\\(\\)]+)+[a-z]?)$/);\n                if (match) {\n                    return { match_: match[0], remainder: input.substr(match[0].length) };\n                }\n                return null;\n            },\n            'uprightEntities': /^(?:pH|pOH|pC|pK|iPr|iBu)(?=$|[^a-zA-Z])/,\n            '/': /^\\s*(\\/)\\s*/,\n            '//': /^\\s*(\\/\\/)\\s*/,\n            '*': /^\\s*[*.]\\s*/\n        },\n        findObserveGroups: function (input, begExcl, begIncl, endIncl, endExcl, beg2Excl, beg2Incl, end2Incl, end2Excl, combine) {\n            var _match = function (input, pattern) {\n                if (typeof pattern === \"string\") {\n                    if (input.indexOf(pattern) !== 0) {\n                        return null;\n                    }\n                    return pattern;\n                }\n                else {\n                    var match_1 = input.match(pattern);\n                    if (!match_1) {\n                        return null;\n                    }\n                    return match_1[0];\n                }\n            };\n            var _findObserveGroups = function (input, i, endChars) {\n                var braces = 0;\n                while (i < input.length) {\n                    var a = input.charAt(i);\n                    var match_2 = _match(input.substr(i), endChars);\n                    if (match_2 !== null && braces === 0) {\n                        return { endMatchBegin: i, endMatchEnd: i + match_2.length };\n                    }\n                    else if (a === \"{\") {\n                        braces++;\n                    }\n                    else if (a === \"}\") {\n                        if (braces === 0) {\n                            throw [\"ExtraCloseMissingOpen\", \"Extra close brace or missing open brace\"];\n                        }\n                        else {\n                            braces--;\n                        }\n                    }\n                    i++;\n                }\n                if (braces > 0) {\n                    return null;\n                }\n                return null;\n            };\n            var match = _match(input, begExcl);\n            if (match === null) {\n                return null;\n            }\n            input = input.substr(match.length);\n            match = _match(input, begIncl);\n            if (match === null) {\n                return null;\n            }\n            var e = _findObserveGroups(input, match.length, endIncl || endExcl);\n            if (e === null) {\n                return null;\n            }\n            var match1 = input.substring(0, (endIncl ? e.endMatchEnd : e.endMatchBegin));\n            if (!(beg2Excl || beg2Incl)) {\n                return {\n                    match_: match1,\n                    remainder: input.substr(e.endMatchEnd)\n                };\n            }\n            else {\n                var group2 = this.findObserveGroups(input.substr(e.endMatchEnd), beg2Excl, beg2Incl, end2Incl, end2Excl);\n                if (group2 === null) {\n                    return null;\n                }\n                var matchRet = [match1, group2.match_];\n                return {\n                    match_: (combine ? matchRet.join(\"\") : matchRet),\n                    remainder: group2.remainder\n                };\n            }\n        },\n        match_: function (m, input) {\n            var pattern = _mhchemParser.patterns.patterns[m];\n            if (pattern === undefined) {\n                throw [\"MhchemBugP\", \"mhchem bug P. Please report. (\" + m + \")\"];\n            }\n            else if (typeof pattern === \"function\") {\n                return _mhchemParser.patterns.patterns[m](input);\n            }\n            else {\n                var match = input.match(pattern);\n                if (match) {\n                    if (match.length > 2) {\n                        return { match_: match.slice(1), remainder: input.substr(match[0].length) };\n                    }\n                    else {\n                        return { match_: match[1] || match[0], remainder: input.substr(match[0].length) };\n                    }\n                }\n                return null;\n            }\n        }\n    },\n    actions: {\n        'a=': function (buffer, m) { buffer.a = (buffer.a || \"\") + m; return undefined; },\n        'b=': function (buffer, m) { buffer.b = (buffer.b || \"\") + m; return undefined; },\n        'p=': function (buffer, m) { buffer.p = (buffer.p || \"\") + m; return undefined; },\n        'o=': function (buffer, m) { buffer.o = (buffer.o || \"\") + m; return undefined; },\n        'o=+p1': function (buffer, _m, a) { buffer.o = (buffer.o || \"\") + a; return undefined; },\n        'q=': function (buffer, m) { buffer.q = (buffer.q || \"\") + m; return undefined; },\n        'd=': function (buffer, m) { buffer.d = (buffer.d || \"\") + m; return undefined; },\n        'rm=': function (buffer, m) { buffer.rm = (buffer.rm || \"\") + m; return undefined; },\n        'text=': function (buffer, m) { buffer.text_ = (buffer.text_ || \"\") + m; return undefined; },\n        'insert': function (_buffer, _m, a) { return { type_: a }; },\n        'insert+p1': function (_buffer, m, a) { return { type_: a, p1: m }; },\n        'insert+p1+p2': function (_buffer, m, a) { return { type_: a, p1: m[0], p2: m[1] }; },\n        'copy': function (_buffer, m) { return m; },\n        'write': function (_buffer, _m, a) { return a; },\n        'rm': function (_buffer, m) { return { type_: 'rm', p1: m }; },\n        'text': function (_buffer, m) { return _mhchemParser.go(m, 'text'); },\n        'tex-math': function (_buffer, m) { return _mhchemParser.go(m, 'tex-math'); },\n        'tex-math tight': function (_buffer, m) { return _mhchemParser.go(m, 'tex-math tight'); },\n        'bond': function (_buffer, m, k) { return { type_: 'bond', kind_: k || m }; },\n        'color0-output': function (_buffer, m) { return { type_: 'color0', color: m }; },\n        'ce': function (_buffer, m) { return _mhchemParser.go(m, 'ce'); },\n        'pu': function (_buffer, m) { return _mhchemParser.go(m, 'pu'); },\n        '1/2': function (_buffer, m) {\n            var ret = [];\n            if (m.match(/^[+\\-]/)) {\n                ret.push(m.substr(0, 1));\n                m = m.substr(1);\n            }\n            var n = m.match(/^([0-9]+|\\$[a-z]\\$|[a-z])\\/([0-9]+)(\\$[a-z]\\$|[a-z])?$/);\n            n[1] = n[1].replace(/\\$/g, \"\");\n            ret.push({ type_: 'frac', p1: n[1], p2: n[2] });\n            if (n[3]) {\n                n[3] = n[3].replace(/\\$/g, \"\");\n                ret.push({ type_: 'tex-math', p1: n[3] });\n            }\n            return ret;\n        },\n        '9,9': function (_buffer, m) { return _mhchemParser.go(m, '9,9'); }\n    },\n    stateMachines: {\n        'tex': {\n            transitions: _mhchemCreateTransitions({\n                'empty': {\n                    '0': { action_: 'copy' }\n                },\n                '\\\\ce{(...)}': {\n                    '0': { action_: [{ type_: 'write', option: \"{\" }, 'ce', { type_: 'write', option: \"}\" }] }\n                },\n                '\\\\pu{(...)}': {\n                    '0': { action_: [{ type_: 'write', option: \"{\" }, 'pu', { type_: 'write', option: \"}\" }] }\n                },\n                'else': {\n                    '0': { action_: 'copy' }\n                },\n            }),\n            actions: {}\n        },\n        'ce': {\n            transitions: _mhchemCreateTransitions({\n                'empty': {\n                    '*': { action_: 'output' }\n                },\n                'else': {\n                    '0|1|2': { action_: 'beginsWithBond=false', revisit: true, toContinue: true }\n                },\n                'oxidation$': {\n                    '0': { action_: 'oxidation-output' }\n                },\n                'CMT': {\n                    'r': { action_: 'rdt=', nextState: 'rt' },\n                    'rd': { action_: 'rqt=', nextState: 'rdt' }\n                },\n                'arrowUpDown': {\n                    '0|1|2|as': { action_: ['sb=false', 'output', 'operator'], nextState: '1' }\n                },\n                'uprightEntities': {\n                    '0|1|2': { action_: ['o=', 'output'], nextState: '1' }\n                },\n                'orbital': {\n                    '0|1|2|3': { action_: 'o=', nextState: 'o' }\n                },\n                '->': {\n                    '0|1|2|3': { action_: 'r=', nextState: 'r' },\n                    'a|as': { action_: ['output', 'r='], nextState: 'r' },\n                    '*': { action_: ['output', 'r='], nextState: 'r' }\n                },\n                '+': {\n                    'o': { action_: 'd= kv', nextState: 'd' },\n                    'd|D': { action_: 'd=', nextState: 'd' },\n                    'q': { action_: 'd=', nextState: 'qd' },\n                    'qd|qD': { action_: 'd=', nextState: 'qd' },\n                    'dq': { action_: ['output', 'd='], nextState: 'd' },\n                    '3': { action_: ['sb=false', 'output', 'operator'], nextState: '0' }\n                },\n                'amount': {\n                    '0|2': { action_: 'a=', nextState: 'a' }\n                },\n                'pm-operator': {\n                    '0|1|2|a|as': { action_: ['sb=false', 'output', { type_: 'operator', option: '\\\\pm' }], nextState: '0' }\n                },\n                'operator': {\n                    '0|1|2|a|as': { action_: ['sb=false', 'output', 'operator'], nextState: '0' }\n                },\n                '-$': {\n                    'o|q': { action_: ['charge or bond', 'output'], nextState: 'qd' },\n                    'd': { action_: 'd=', nextState: 'd' },\n                    'D': { action_: ['output', { type_: 'bond', option: \"-\" }], nextState: '3' },\n                    'q': { action_: 'd=', nextState: 'qd' },\n                    'qd': { action_: 'd=', nextState: 'qd' },\n                    'qD|dq': { action_: ['output', { type_: 'bond', option: \"-\" }], nextState: '3' }\n                },\n                '-9': {\n                    '3|o': { action_: ['output', { type_: 'insert', option: 'hyphen' }], nextState: '3' }\n                },\n                '- orbital overlap': {\n                    'o': { action_: ['output', { type_: 'insert', option: 'hyphen' }], nextState: '2' },\n                    'd': { action_: ['output', { type_: 'insert', option: 'hyphen' }], nextState: '2' }\n                },\n                '-': {\n                    '0|1|2': { action_: [{ type_: 'output', option: 1 }, 'beginsWithBond=true', { type_: 'bond', option: \"-\" }], nextState: '3' },\n                    '3': { action_: { type_: 'bond', option: \"-\" } },\n                    'a': { action_: ['output', { type_: 'insert', option: 'hyphen' }], nextState: '2' },\n                    'as': { action_: [{ type_: 'output', option: 2 }, { type_: 'bond', option: \"-\" }], nextState: '3' },\n                    'b': { action_: 'b=' },\n                    'o': { action_: { type_: '- after o/d', option: false }, nextState: '2' },\n                    'q': { action_: { type_: '- after o/d', option: false }, nextState: '2' },\n                    'd|qd|dq': { action_: { type_: '- after o/d', option: true }, nextState: '2' },\n                    'D|qD|p': { action_: ['output', { type_: 'bond', option: \"-\" }], nextState: '3' }\n                },\n                'amount2': {\n                    '1|3': { action_: 'a=', nextState: 'a' }\n                },\n                'letters': {\n                    '0|1|2|3|a|as|b|p|bp|o': { action_: 'o=', nextState: 'o' },\n                    'q|dq': { action_: ['output', 'o='], nextState: 'o' },\n                    'd|D|qd|qD': { action_: 'o after d', nextState: 'o' }\n                },\n                'digits': {\n                    'o': { action_: 'q=', nextState: 'q' },\n                    'd|D': { action_: 'q=', nextState: 'dq' },\n                    'q': { action_: ['output', 'o='], nextState: 'o' },\n                    'a': { action_: 'o=', nextState: 'o' }\n                },\n                'space A': {\n                    'b|p|bp': { action_: [] }\n                },\n                'space': {\n                    'a': { action_: [], nextState: 'as' },\n                    '0': { action_: 'sb=false' },\n                    '1|2': { action_: 'sb=true' },\n                    'r|rt|rd|rdt|rdq': { action_: 'output', nextState: '0' },\n                    '*': { action_: ['output', 'sb=true'], nextState: '1' }\n                },\n                '1st-level escape': {\n                    '1|2': { action_: ['output', { type_: 'insert+p1', option: '1st-level escape' }] },\n                    '*': { action_: ['output', { type_: 'insert+p1', option: '1st-level escape' }], nextState: '0' }\n                },\n                '[(...)]': {\n                    'r|rt': { action_: 'rd=', nextState: 'rd' },\n                    'rd|rdt': { action_: 'rq=', nextState: 'rdq' }\n                },\n                '...': {\n                    'o|d|D|dq|qd|qD': { action_: ['output', { type_: 'bond', option: \"...\" }], nextState: '3' },\n                    '*': { action_: [{ type_: 'output', option: 1 }, { type_: 'insert', option: 'ellipsis' }], nextState: '1' }\n                },\n                '. __* ': {\n                    '*': { action_: ['output', { type_: 'insert', option: 'addition compound' }], nextState: '1' }\n                },\n                'state of aggregation $': {\n                    '*': { action_: ['output', 'state of aggregation'], nextState: '1' }\n                },\n                '{[(': {\n                    'a|as|o': { action_: ['o=', 'output', 'parenthesisLevel++'], nextState: '2' },\n                    '0|1|2|3': { action_: ['o=', 'output', 'parenthesisLevel++'], nextState: '2' },\n                    '*': { action_: ['output', 'o=', 'output', 'parenthesisLevel++'], nextState: '2' }\n                },\n                ')]}': {\n                    '0|1|2|3|b|p|bp|o': { action_: ['o=', 'parenthesisLevel--'], nextState: 'o' },\n                    'a|as|d|D|q|qd|qD|dq': { action_: ['output', 'o=', 'parenthesisLevel--'], nextState: 'o' }\n                },\n                ', ': {\n                    '*': { action_: ['output', 'comma'], nextState: '0' }\n                },\n                '^_': {\n                    '*': { action_: [] }\n                },\n                '^{(...)}|^($...$)': {\n                    '0|1|2|as': { action_: 'b=', nextState: 'b' },\n                    'p': { action_: 'b=', nextState: 'bp' },\n                    '3|o': { action_: 'd= kv', nextState: 'D' },\n                    'q': { action_: 'd=', nextState: 'qD' },\n                    'd|D|qd|qD|dq': { action_: ['output', 'd='], nextState: 'D' }\n                },\n                '^a|^\\\\x{}{}|^\\\\x{}|^\\\\x|\\'': {\n                    '0|1|2|as': { action_: 'b=', nextState: 'b' },\n                    'p': { action_: 'b=', nextState: 'bp' },\n                    '3|o': { action_: 'd= kv', nextState: 'd' },\n                    'q': { action_: 'd=', nextState: 'qd' },\n                    'd|qd|D|qD': { action_: 'd=' },\n                    'dq': { action_: ['output', 'd='], nextState: 'd' }\n                },\n                '_{(state of aggregation)}$': {\n                    'd|D|q|qd|qD|dq': { action_: ['output', 'q='], nextState: 'q' }\n                },\n                '_{(...)}|_($...$)|_9|_\\\\x{}{}|_\\\\x{}|_\\\\x': {\n                    '0|1|2|as': { action_: 'p=', nextState: 'p' },\n                    'b': { action_: 'p=', nextState: 'bp' },\n                    '3|o': { action_: 'q=', nextState: 'q' },\n                    'd|D': { action_: 'q=', nextState: 'dq' },\n                    'q|qd|qD|dq': { action_: ['output', 'q='], nextState: 'q' }\n                },\n                '=<>': {\n                    '0|1|2|3|a|as|o|q|d|D|qd|qD|dq': { action_: [{ type_: 'output', option: 2 }, 'bond'], nextState: '3' }\n                },\n                '#': {\n                    '0|1|2|3|a|as|o': { action_: [{ type_: 'output', option: 2 }, { type_: 'bond', option: \"#\" }], nextState: '3' }\n                },\n                '{}^': {\n                    '*': { action_: [{ type_: 'output', option: 1 }, { type_: 'insert', option: 'tinySkip' }], nextState: '1' }\n                },\n                '{}': {\n                    '*': { action_: { type_: 'output', option: 1 }, nextState: '1' }\n                },\n                '{...}': {\n                    '0|1|2|3|a|as|b|p|bp': { action_: 'o=', nextState: 'o' },\n                    'o|d|D|q|qd|qD|dq': { action_: ['output', 'o='], nextState: 'o' }\n                },\n                '$...$': {\n                    'a': { action_: 'a=' },\n                    '0|1|2|3|as|b|p|bp|o': { action_: 'o=', nextState: 'o' },\n                    'as|o': { action_: 'o=' },\n                    'q|d|D|qd|qD|dq': { action_: ['output', 'o='], nextState: 'o' }\n                },\n                '\\\\bond{(...)}': {\n                    '*': { action_: [{ type_: 'output', option: 2 }, 'bond'], nextState: \"3\" }\n                },\n                '\\\\frac{(...)}': {\n                    '*': { action_: [{ type_: 'output', option: 1 }, 'frac-output'], nextState: '3' }\n                },\n                '\\\\overset{(...)}': {\n                    '*': { action_: [{ type_: 'output', option: 2 }, 'overset-output'], nextState: '3' }\n                },\n                '\\\\underset{(...)}': {\n                    '*': { action_: [{ type_: 'output', option: 2 }, 'underset-output'], nextState: '3' }\n                },\n                '\\\\underbrace{(...)}': {\n                    '*': { action_: [{ type_: 'output', option: 2 }, 'underbrace-output'], nextState: '3' }\n                },\n                '\\\\color{(...)}{(...)}': {\n                    '*': { action_: [{ type_: 'output', option: 2 }, 'color-output'], nextState: '3' }\n                },\n                '\\\\color{(...)}': {\n                    '*': { action_: [{ type_: 'output', option: 2 }, 'color0-output'] }\n                },\n                '\\\\ce{(...)}': {\n                    '*': { action_: [{ type_: 'output', option: 2 }, 'ce'], nextState: '3' }\n                },\n                '\\\\,': {\n                    '*': { action_: [{ type_: 'output', option: 1 }, 'copy'], nextState: '1' }\n                },\n                '\\\\pu{(...)}': {\n                    '*': { action_: ['output', { type_: 'write', option: \"{\" }, 'pu', { type_: 'write', option: \"}\" }], nextState: '3' }\n                },\n                '\\\\x{}{}|\\\\x{}|\\\\x': {\n                    '0|1|2|3|a|as|b|p|bp|o|c0': { action_: ['o=', 'output'], nextState: '3' },\n                    '*': { action_: ['output', 'o=', 'output'], nextState: '3' }\n                },\n                'others': {\n                    '*': { action_: [{ type_: 'output', option: 1 }, 'copy'], nextState: '3' }\n                },\n                'else2': {\n                    'a': { action_: 'a to o', nextState: 'o', revisit: true },\n                    'as': { action_: ['output', 'sb=true'], nextState: '1', revisit: true },\n                    'r|rt|rd|rdt|rdq': { action_: ['output'], nextState: '0', revisit: true },\n                    '*': { action_: ['output', 'copy'], nextState: '3' }\n                }\n            }),\n            actions: {\n                'o after d': function (buffer, m) {\n                    var ret;\n                    if ((buffer.d || \"\").match(/^[1-9][0-9]*$/)) {\n                        var tmp = buffer.d;\n                        buffer.d = undefined;\n                        ret = this['output'](buffer);\n                        ret.push({ type_: 'tinySkip' });\n                        buffer.b = tmp;\n                    }\n                    else {\n                        ret = this['output'](buffer);\n                    }\n                    _mhchemParser.actions['o='](buffer, m);\n                    return ret;\n                },\n                'd= kv': function (buffer, m) {\n                    buffer.d = m;\n                    buffer.dType = 'kv';\n                    return undefined;\n                },\n                'charge or bond': function (buffer, m) {\n                    if (buffer['beginsWithBond']) {\n                        var ret = [];\n                        _mhchemParser.concatArray(ret, this['output'](buffer));\n                        _mhchemParser.concatArray(ret, _mhchemParser.actions['bond'](buffer, m, \"-\"));\n                        return ret;\n                    }\n                    else {\n                        buffer.d = m;\n                        return undefined;\n                    }\n                },\n                '- after o/d': function (buffer, m, isAfterD) {\n                    var c1 = _mhchemParser.patterns.match_('orbital', buffer.o || \"\");\n                    var c2 = _mhchemParser.patterns.match_('one lowercase greek letter $', buffer.o || \"\");\n                    var c3 = _mhchemParser.patterns.match_('one lowercase latin letter $', buffer.o || \"\");\n                    var c4 = _mhchemParser.patterns.match_('$one lowercase latin letter$ $', buffer.o || \"\");\n                    var hyphenFollows = m === \"-\" && (c1 && c1.remainder === \"\" || c2 || c3 || c4);\n                    if (hyphenFollows && !buffer.a && !buffer.b && !buffer.p && !buffer.d && !buffer.q && !c1 && c3) {\n                        buffer.o = '$' + buffer.o + '$';\n                    }\n                    var ret = [];\n                    if (hyphenFollows) {\n                        _mhchemParser.concatArray(ret, this['output'](buffer));\n                        ret.push({ type_: 'hyphen' });\n                    }\n                    else {\n                        c1 = _mhchemParser.patterns.match_('digits', buffer.d || \"\");\n                        if (isAfterD && c1 && c1.remainder === '') {\n                            _mhchemParser.concatArray(ret, _mhchemParser.actions['d='](buffer, m));\n                            _mhchemParser.concatArray(ret, this['output'](buffer));\n                        }\n                        else {\n                            _mhchemParser.concatArray(ret, this['output'](buffer));\n                            _mhchemParser.concatArray(ret, _mhchemParser.actions['bond'](buffer, m, \"-\"));\n                        }\n                    }\n                    return ret;\n                },\n                'a to o': function (buffer) {\n                    buffer.o = buffer.a;\n                    buffer.a = undefined;\n                    return undefined;\n                },\n                'sb=true': function (buffer) { buffer.sb = true; return undefined; },\n                'sb=false': function (buffer) { buffer.sb = false; return undefined; },\n                'beginsWithBond=true': function (buffer) { buffer['beginsWithBond'] = true; return undefined; },\n                'beginsWithBond=false': function (buffer) { buffer['beginsWithBond'] = false; return undefined; },\n                'parenthesisLevel++': function (buffer) { buffer['parenthesisLevel']++; return undefined; },\n                'parenthesisLevel--': function (buffer) { buffer['parenthesisLevel']--; return undefined; },\n                'state of aggregation': function (_buffer, m) {\n                    return { type_: 'state of aggregation', p1: _mhchemParser.go(m, 'o') };\n                },\n                'comma': function (buffer, m) {\n                    var a = m.replace(/\\s*$/, '');\n                    var withSpace = (a !== m);\n                    if (withSpace && buffer['parenthesisLevel'] === 0) {\n                        return { type_: 'comma enumeration L', p1: a };\n                    }\n                    else {\n                        return { type_: 'comma enumeration M', p1: a };\n                    }\n                },\n                'output': function (buffer, _m, entityFollows) {\n                    var ret;\n                    if (!buffer.r) {\n                        ret = [];\n                        if (!buffer.a && !buffer.b && !buffer.p && !buffer.o && !buffer.q && !buffer.d && !entityFollows) {\n                        }\n                        else {\n                            if (buffer.sb) {\n                                ret.push({ type_: 'entitySkip' });\n                            }\n                            if (!buffer.o && !buffer.q && !buffer.d && !buffer.b && !buffer.p && entityFollows !== 2) {\n                                buffer.o = buffer.a;\n                                buffer.a = undefined;\n                            }\n                            else if (!buffer.o && !buffer.q && !buffer.d && (buffer.b || buffer.p)) {\n                                buffer.o = buffer.a;\n                                buffer.d = buffer.b;\n                                buffer.q = buffer.p;\n                                buffer.a = buffer.b = buffer.p = undefined;\n                            }\n                            else {\n                                if (buffer.o && buffer.dType === 'kv' && _mhchemParser.patterns.match_('d-oxidation$', buffer.d || \"\")) {\n                                    buffer.dType = 'oxidation';\n                                }\n                                else if (buffer.o && buffer.dType === 'kv' && !buffer.q) {\n                                    buffer.dType = undefined;\n                                }\n                            }\n                            ret.push({\n                                type_: 'chemfive',\n                                a: _mhchemParser.go(buffer.a, 'a'),\n                                b: _mhchemParser.go(buffer.b, 'bd'),\n                                p: _mhchemParser.go(buffer.p, 'pq'),\n                                o: _mhchemParser.go(buffer.o, 'o'),\n                                q: _mhchemParser.go(buffer.q, 'pq'),\n                                d: _mhchemParser.go(buffer.d, (buffer.dType === 'oxidation' ? 'oxidation' : 'bd')),\n                                dType: buffer.dType\n                            });\n                        }\n                    }\n                    else {\n                        var rd = void 0;\n                        if (buffer.rdt === 'M') {\n                            rd = _mhchemParser.go(buffer.rd, 'tex-math');\n                        }\n                        else if (buffer.rdt === 'T') {\n                            rd = [{ type_: 'text', p1: buffer.rd || \"\" }];\n                        }\n                        else {\n                            rd = _mhchemParser.go(buffer.rd, 'ce');\n                        }\n                        var rq = void 0;\n                        if (buffer.rqt === 'M') {\n                            rq = _mhchemParser.go(buffer.rq, 'tex-math');\n                        }\n                        else if (buffer.rqt === 'T') {\n                            rq = [{ type_: 'text', p1: buffer.rq || \"\" }];\n                        }\n                        else {\n                            rq = _mhchemParser.go(buffer.rq, 'ce');\n                        }\n                        ret = {\n                            type_: 'arrow',\n                            r: buffer.r,\n                            rd: rd,\n                            rq: rq\n                        };\n                    }\n                    for (var p in buffer) {\n                        if (p !== 'parenthesisLevel' && p !== 'beginsWithBond') {\n                            delete buffer[p];\n                        }\n                    }\n                    return ret;\n                },\n                'oxidation-output': function (_buffer, m) {\n                    var ret = [\"{\"];\n                    _mhchemParser.concatArray(ret, _mhchemParser.go(m, 'oxidation'));\n                    ret.push(\"}\");\n                    return ret;\n                },\n                'frac-output': function (_buffer, m) {\n                    return { type_: 'frac-ce', p1: _mhchemParser.go(m[0], 'ce'), p2: _mhchemParser.go(m[1], 'ce') };\n                },\n                'overset-output': function (_buffer, m) {\n                    return { type_: 'overset', p1: _mhchemParser.go(m[0], 'ce'), p2: _mhchemParser.go(m[1], 'ce') };\n                },\n                'underset-output': function (_buffer, m) {\n                    return { type_: 'underset', p1: _mhchemParser.go(m[0], 'ce'), p2: _mhchemParser.go(m[1], 'ce') };\n                },\n                'underbrace-output': function (_buffer, m) {\n                    return { type_: 'underbrace', p1: _mhchemParser.go(m[0], 'ce'), p2: _mhchemParser.go(m[1], 'ce') };\n                },\n                'color-output': function (_buffer, m) {\n                    return { type_: 'color', color1: m[0], color2: _mhchemParser.go(m[1], 'ce') };\n                },\n                'r=': function (buffer, m) { buffer.r = m; return undefined; },\n                'rdt=': function (buffer, m) { buffer.rdt = m; return undefined; },\n                'rd=': function (buffer, m) { buffer.rd = m; return undefined; },\n                'rqt=': function (buffer, m) { buffer.rqt = m; return undefined; },\n                'rq=': function (buffer, m) { buffer.rq = m; return undefined; },\n                'operator': function (_buffer, m, p1) { return { type_: 'operator', kind_: (p1 || m) }; }\n            }\n        },\n        'a': {\n            transitions: _mhchemCreateTransitions({\n                'empty': {\n                    '*': { action_: [] }\n                },\n                '1/2$': {\n                    '0': { action_: '1/2' }\n                },\n                'else': {\n                    '0': { action_: [], nextState: '1', revisit: true }\n                },\n                '${(...)}$__$(...)$': {\n                    '*': { action_: 'tex-math tight', nextState: '1' }\n                },\n                ',': {\n                    '*': { action_: { type_: 'insert', option: 'commaDecimal' } }\n                },\n                'else2': {\n                    '*': { action_: 'copy' }\n                }\n            }),\n            actions: {}\n        },\n        'o': {\n            transitions: _mhchemCreateTransitions({\n                'empty': {\n                    '*': { action_: [] }\n                },\n                '1/2$': {\n                    '0': { action_: '1/2' }\n                },\n                'else': {\n                    '0': { action_: [], nextState: '1', revisit: true }\n                },\n                'letters': {\n                    '*': { action_: 'rm' }\n                },\n                '\\\\ca': {\n                    '*': { action_: { type_: 'insert', option: 'circa' } }\n                },\n                '\\\\pu{(...)}': {\n                    '*': { action_: [{ type_: 'write', option: \"{\" }, 'pu', { type_: 'write', option: \"}\" }] }\n                },\n                '\\\\x{}{}|\\\\x{}|\\\\x': {\n                    '*': { action_: 'copy' }\n                },\n                '${(...)}$__$(...)$': {\n                    '*': { action_: 'tex-math' }\n                },\n                '{(...)}': {\n                    '*': { action_: [{ type_: 'write', option: \"{\" }, 'text', { type_: 'write', option: \"}\" }] }\n                },\n                'else2': {\n                    '*': { action_: 'copy' }\n                }\n            }),\n            actions: {}\n        },\n        'text': {\n            transitions: _mhchemCreateTransitions({\n                'empty': {\n                    '*': { action_: 'output' }\n                },\n                '{...}': {\n                    '*': { action_: 'text=' }\n                },\n                '${(...)}$__$(...)$': {\n                    '*': { action_: 'tex-math' }\n                },\n                '\\\\greek': {\n                    '*': { action_: ['output', 'rm'] }\n                },\n                '\\\\pu{(...)}': {\n                    '*': { action_: ['output', { type_: 'write', option: \"{\" }, 'pu', { type_: 'write', option: \"}\" }] }\n                },\n                '\\\\,|\\\\x{}{}|\\\\x{}|\\\\x': {\n                    '*': { action_: ['output', 'copy'] }\n                },\n                'else': {\n                    '*': { action_: 'text=' }\n                }\n            }),\n            actions: {\n                'output': function (buffer) {\n                    if (buffer.text_) {\n                        var ret = { type_: 'text', p1: buffer.text_ };\n                        for (var p in buffer) {\n                            delete buffer[p];\n                        }\n                        return ret;\n                    }\n                    return undefined;\n                }\n            }\n        },\n        'pq': {\n            transitions: _mhchemCreateTransitions({\n                'empty': {\n                    '*': { action_: [] }\n                },\n                'state of aggregation $': {\n                    '*': { action_: 'state of aggregation' }\n                },\n                'i$': {\n                    '0': { action_: [], nextState: '!f', revisit: true }\n                },\n                '(KV letters),': {\n                    '0': { action_: 'rm', nextState: '0' }\n                },\n                'formula$': {\n                    '0': { action_: [], nextState: 'f', revisit: true }\n                },\n                '1/2$': {\n                    '0': { action_: '1/2' }\n                },\n                'else': {\n                    '0': { action_: [], nextState: '!f', revisit: true }\n                },\n                '${(...)}$__$(...)$': {\n                    '*': { action_: 'tex-math' }\n                },\n                '{(...)}': {\n                    '*': { action_: 'text' }\n                },\n                'a-z': {\n                    'f': { action_: 'tex-math' }\n                },\n                'letters': {\n                    '*': { action_: 'rm' }\n                },\n                '-9.,9': {\n                    '*': { action_: '9,9' }\n                },\n                ',': {\n                    '*': { action_: { type_: 'insert+p1', option: 'comma enumeration S' } }\n                },\n                '\\\\color{(...)}{(...)}': {\n                    '*': { action_: 'color-output' }\n                },\n                '\\\\color{(...)}': {\n                    '*': { action_: 'color0-output' }\n                },\n                '\\\\ce{(...)}': {\n                    '*': { action_: 'ce' }\n                },\n                '\\\\pu{(...)}': {\n                    '*': { action_: [{ type_: 'write', option: \"{\" }, 'pu', { type_: 'write', option: \"}\" }] }\n                },\n                '\\\\,|\\\\x{}{}|\\\\x{}|\\\\x': {\n                    '*': { action_: 'copy' }\n                },\n                'else2': {\n                    '*': { action_: 'copy' }\n                }\n            }),\n            actions: {\n                'state of aggregation': function (_buffer, m) {\n                    return { type_: 'state of aggregation subscript', p1: _mhchemParser.go(m, 'o') };\n                },\n                'color-output': function (_buffer, m) {\n                    return { type_: 'color', color1: m[0], color2: _mhchemParser.go(m[1], 'pq') };\n                }\n            }\n        },\n        'bd': {\n            transitions: _mhchemCreateTransitions({\n                'empty': {\n                    '*': { action_: [] }\n                },\n                'x$': {\n                    '0': { action_: [], nextState: '!f', revisit: true }\n                },\n                'formula$': {\n                    '0': { action_: [], nextState: 'f', revisit: true }\n                },\n                'else': {\n                    '0': { action_: [], nextState: '!f', revisit: true }\n                },\n                '-9.,9 no missing 0': {\n                    '*': { action_: '9,9' }\n                },\n                '.': {\n                    '*': { action_: { type_: 'insert', option: 'electron dot' } }\n                },\n                'a-z': {\n                    'f': { action_: 'tex-math' }\n                },\n                'x': {\n                    '*': { action_: { type_: 'insert', option: 'KV x' } }\n                },\n                'letters': {\n                    '*': { action_: 'rm' }\n                },\n                '\\'': {\n                    '*': { action_: { type_: 'insert', option: 'prime' } }\n                },\n                '${(...)}$__$(...)$': {\n                    '*': { action_: 'tex-math' }\n                },\n                '{(...)}': {\n                    '*': { action_: 'text' }\n                },\n                '\\\\color{(...)}{(...)}': {\n                    '*': { action_: 'color-output' }\n                },\n                '\\\\color{(...)}': {\n                    '*': { action_: 'color0-output' }\n                },\n                '\\\\ce{(...)}': {\n                    '*': { action_: 'ce' }\n                },\n                '\\\\pu{(...)}': {\n                    '*': { action_: [{ type_: 'write', option: \"{\" }, 'pu', { type_: 'write', option: \"}\" }] }\n                },\n                '\\\\,|\\\\x{}{}|\\\\x{}|\\\\x': {\n                    '*': { action_: 'copy' }\n                },\n                'else2': {\n                    '*': { action_: 'copy' }\n                }\n            }),\n            actions: {\n                'color-output': function (_buffer, m) {\n                    return { type_: 'color', color1: m[0], color2: _mhchemParser.go(m[1], 'bd') };\n                }\n            }\n        },\n        'oxidation': {\n            transitions: _mhchemCreateTransitions({\n                'empty': {\n                    '*': { action_: 'roman-numeral' }\n                },\n                'pm-operator': {\n                    '*': { action_: { type_: 'o=+p1', option: \"\\\\pm\" } }\n                },\n                'else': {\n                    '*': { action_: 'o=' }\n                }\n            }),\n            actions: {\n                'roman-numeral': function (buffer) { return { type_: 'roman numeral', p1: buffer.o || \"\" }; }\n            }\n        },\n        'tex-math': {\n            transitions: _mhchemCreateTransitions({\n                'empty': {\n                    '*': { action_: 'output' }\n                },\n                '\\\\ce{(...)}': {\n                    '*': { action_: ['output', 'ce'] }\n                },\n                '\\\\pu{(...)}': {\n                    '*': { action_: ['output', { type_: 'write', option: \"{\" }, 'pu', { type_: 'write', option: \"}\" }] }\n                },\n                '{...}|\\\\,|\\\\x{}{}|\\\\x{}|\\\\x': {\n                    '*': { action_: 'o=' }\n                },\n                'else': {\n                    '*': { action_: 'o=' }\n                }\n            }),\n            actions: {\n                'output': function (buffer) {\n                    if (buffer.o) {\n                        var ret = { type_: 'tex-math', p1: buffer.o };\n                        for (var p in buffer) {\n                            delete buffer[p];\n                        }\n                        return ret;\n                    }\n                    return undefined;\n                }\n            }\n        },\n        'tex-math tight': {\n            transitions: _mhchemCreateTransitions({\n                'empty': {\n                    '*': { action_: 'output' }\n                },\n                '\\\\ce{(...)}': {\n                    '*': { action_: ['output', 'ce'] }\n                },\n                '\\\\pu{(...)}': {\n                    '*': { action_: ['output', { type_: 'write', option: \"{\" }, 'pu', { type_: 'write', option: \"}\" }] }\n                },\n                '{...}|\\\\,|\\\\x{}{}|\\\\x{}|\\\\x': {\n                    '*': { action_: 'o=' }\n                },\n                '-|+': {\n                    '*': { action_: 'tight operator' }\n                },\n                'else': {\n                    '*': { action_: 'o=' }\n                }\n            }),\n            actions: {\n                'tight operator': function (buffer, m) { buffer.o = (buffer.o || \"\") + \"{\" + m + \"}\"; return undefined; },\n                'output': function (buffer) {\n                    if (buffer.o) {\n                        var ret = { type_: 'tex-math', p1: buffer.o };\n                        for (var p in buffer) {\n                            delete buffer[p];\n                        }\n                        return ret;\n                    }\n                    return undefined;\n                }\n            }\n        },\n        '9,9': {\n            transitions: _mhchemCreateTransitions({\n                'empty': {\n                    '*': { action_: [] }\n                },\n                ',': {\n                    '*': { action_: 'comma' }\n                },\n                'else': {\n                    '*': { action_: 'copy' }\n                }\n            }),\n            actions: {\n                'comma': function () { return { type_: 'commaDecimal' }; }\n            }\n        },\n        'pu': {\n            transitions: _mhchemCreateTransitions({\n                'empty': {\n                    '*': { action_: 'output' }\n                },\n                'space$': {\n                    '*': { action_: ['output', 'space'] }\n                },\n                '{[(|)]}': {\n                    '0|a': { action_: 'copy' }\n                },\n                '(-)(9)^(-9)': {\n                    '0': { action_: 'number^', nextState: 'a' }\n                },\n                '(-)(9.,9)(e)(99)': {\n                    '0': { action_: 'enumber', nextState: 'a' }\n                },\n                'space': {\n                    '0|a': { action_: [] }\n                },\n                'pm-operator': {\n                    '0|a': { action_: { type_: 'operator', option: '\\\\pm' }, nextState: '0' }\n                },\n                'operator': {\n                    '0|a': { action_: 'copy', nextState: '0' }\n                },\n                '//': {\n                    'd': { action_: 'o=', nextState: '/' }\n                },\n                '/': {\n                    'd': { action_: 'o=', nextState: '/' }\n                },\n                '{...}|else': {\n                    '0|d': { action_: 'd=', nextState: 'd' },\n                    'a': { action_: ['space', 'd='], nextState: 'd' },\n                    '/|q': { action_: 'q=', nextState: 'q' }\n                }\n            }),\n            actions: {\n                'enumber': function (_buffer, m) {\n                    var ret = [];\n                    if (m[0] === \"+-\" || m[0] === \"+/-\") {\n                        ret.push(\"\\\\pm \");\n                    }\n                    else if (m[0]) {\n                        ret.push(m[0]);\n                    }\n                    if (m[1]) {\n                        _mhchemParser.concatArray(ret, _mhchemParser.go(m[1], 'pu-9,9'));\n                        if (m[2]) {\n                            if (m[2].match(/[,.]/)) {\n                                _mhchemParser.concatArray(ret, _mhchemParser.go(m[2], 'pu-9,9'));\n                            }\n                            else {\n                                ret.push(m[2]);\n                            }\n                        }\n                        if (m[3] || m[4]) {\n                            if (m[3] === \"e\" || m[4] === \"*\") {\n                                ret.push({ type_: 'cdot' });\n                            }\n                            else {\n                                ret.push({ type_: 'times' });\n                            }\n                        }\n                    }\n                    if (m[5]) {\n                        ret.push(\"10^{\" + m[5] + \"}\");\n                    }\n                    return ret;\n                },\n                'number^': function (_buffer, m) {\n                    var ret = [];\n                    if (m[0] === \"+-\" || m[0] === \"+/-\") {\n                        ret.push(\"\\\\pm \");\n                    }\n                    else if (m[0]) {\n                        ret.push(m[0]);\n                    }\n                    _mhchemParser.concatArray(ret, _mhchemParser.go(m[1], 'pu-9,9'));\n                    ret.push(\"^{\" + m[2] + \"}\");\n                    return ret;\n                },\n                'operator': function (_buffer, m, p1) { return { type_: 'operator', kind_: (p1 || m) }; },\n                'space': function () { return { type_: 'pu-space-1' }; },\n                'output': function (buffer) {\n                    var ret;\n                    var md = _mhchemParser.patterns.match_('{(...)}', buffer.d || \"\");\n                    if (md && md.remainder === '') {\n                        buffer.d = md.match_;\n                    }\n                    var mq = _mhchemParser.patterns.match_('{(...)}', buffer.q || \"\");\n                    if (mq && mq.remainder === '') {\n                        buffer.q = mq.match_;\n                    }\n                    if (buffer.d) {\n                        buffer.d = buffer.d.replace(/\\u00B0C|\\^oC|\\^{o}C/g, \"{}^{\\\\circ}C\");\n                        buffer.d = buffer.d.replace(/\\u00B0F|\\^oF|\\^{o}F/g, \"{}^{\\\\circ}F\");\n                    }\n                    if (buffer.q) {\n                        buffer.q = buffer.q.replace(/\\u00B0C|\\^oC|\\^{o}C/g, \"{}^{\\\\circ}C\");\n                        buffer.q = buffer.q.replace(/\\u00B0F|\\^oF|\\^{o}F/g, \"{}^{\\\\circ}F\");\n                        var b5 = {\n                            d: _mhchemParser.go(buffer.d, 'pu'),\n                            q: _mhchemParser.go(buffer.q, 'pu')\n                        };\n                        if (buffer.o === '//') {\n                            ret = { type_: 'pu-frac', p1: b5.d, p2: b5.q };\n                        }\n                        else {\n                            ret = b5.d;\n                            if (b5.d.length > 1 || b5.q.length > 1) {\n                                ret.push({ type_: ' / ' });\n                            }\n                            else {\n                                ret.push({ type_: '/' });\n                            }\n                            _mhchemParser.concatArray(ret, b5.q);\n                        }\n                    }\n                    else {\n                        ret = _mhchemParser.go(buffer.d, 'pu-2');\n                    }\n                    for (var p in buffer) {\n                        delete buffer[p];\n                    }\n                    return ret;\n                }\n            }\n        },\n        'pu-2': {\n            transitions: _mhchemCreateTransitions({\n                'empty': {\n                    '*': { action_: 'output' }\n                },\n                '*': {\n                    '*': { action_: ['output', 'cdot'], nextState: '0' }\n                },\n                '\\\\x': {\n                    '*': { action_: 'rm=' }\n                },\n                'space': {\n                    '*': { action_: ['output', 'space'], nextState: '0' }\n                },\n                '^{(...)}|^(-1)': {\n                    '1': { action_: '^(-1)' }\n                },\n                '-9.,9': {\n                    '0': { action_: 'rm=', nextState: '0' },\n                    '1': { action_: '^(-1)', nextState: '0' }\n                },\n                '{...}|else': {\n                    '*': { action_: 'rm=', nextState: '1' }\n                }\n            }),\n            actions: {\n                'cdot': function () { return { type_: 'tight cdot' }; },\n                '^(-1)': function (buffer, m) { buffer.rm += \"^{\" + m + \"}\"; return undefined; },\n                'space': function () { return { type_: 'pu-space-2' }; },\n                'output': function (buffer) {\n                    var ret = [];\n                    if (buffer.rm) {\n                        var mrm = _mhchemParser.patterns.match_('{(...)}', buffer.rm || \"\");\n                        if (mrm && mrm.remainder === '') {\n                            ret = _mhchemParser.go(mrm.match_, 'pu');\n                        }\n                        else {\n                            ret = { type_: 'rm', p1: buffer.rm };\n                        }\n                    }\n                    for (var p in buffer) {\n                        delete buffer[p];\n                    }\n                    return ret;\n                }\n            }\n        },\n        'pu-9,9': {\n            transitions: _mhchemCreateTransitions({\n                'empty': {\n                    '0': { action_: 'output-0' },\n                    'o': { action_: 'output-o' }\n                },\n                ',': {\n                    '0': { action_: ['output-0', 'comma'], nextState: 'o' }\n                },\n                '.': {\n                    '0': { action_: ['output-0', 'copy'], nextState: 'o' }\n                },\n                'else': {\n                    '*': { action_: 'text=' }\n                }\n            }),\n            actions: {\n                'comma': function () { return { type_: 'commaDecimal' }; },\n                'output-0': function (buffer) {\n                    var ret = [];\n                    buffer.text_ = buffer.text_ || \"\";\n                    if (buffer.text_.length > 4) {\n                        var a = buffer.text_.length % 3;\n                        if (a === 0) {\n                            a = 3;\n                        }\n                        for (var i = buffer.text_.length - 3; i > 0; i -= 3) {\n                            ret.push(buffer.text_.substr(i, 3));\n                            ret.push({ type_: '1000 separator' });\n                        }\n                        ret.push(buffer.text_.substr(0, a));\n                        ret.reverse();\n                    }\n                    else {\n                        ret.push(buffer.text_);\n                    }\n                    for (var p in buffer) {\n                        delete buffer[p];\n                    }\n                    return ret;\n                },\n                'output-o': function (buffer) {\n                    var ret = [];\n                    buffer.text_ = buffer.text_ || \"\";\n                    if (buffer.text_.length > 4) {\n                        var a = buffer.text_.length - 3;\n                        var i = void 0;\n                        for (i = 0; i < a; i += 3) {\n                            ret.push(buffer.text_.substr(i, 3));\n                            ret.push({ type_: '1000 separator' });\n                        }\n                        ret.push(buffer.text_.substr(i));\n                    }\n                    else {\n                        ret.push(buffer.text_);\n                    }\n                    for (var p in buffer) {\n                        delete buffer[p];\n                    }\n                    return ret;\n                }\n            }\n        }\n    }\n};\nvar _mhchemTexify = {\n    go: function (input, addOuterBraces) {\n        if (!input) {\n            return \"\";\n        }\n        var res = \"\";\n        var cee = false;\n        for (var i = 0; i < input.length; i++) {\n            var inputi = input[i];\n            if (typeof inputi === \"string\") {\n                res += inputi;\n            }\n            else {\n                res += _mhchemTexify._go2(inputi);\n                if (inputi.type_ === '1st-level escape') {\n                    cee = true;\n                }\n            }\n        }\n        if (addOuterBraces && !cee && res) {\n            res = \"{\" + res + \"}\";\n        }\n        return res;\n    },\n    _goInner: function (input) {\n        return _mhchemTexify.go(input, false);\n    },\n    _go2: function (buf) {\n        var res;\n        switch (buf.type_) {\n            case 'chemfive':\n                res = \"\";\n                var b5 = {\n                    a: _mhchemTexify._goInner(buf.a),\n                    b: _mhchemTexify._goInner(buf.b),\n                    p: _mhchemTexify._goInner(buf.p),\n                    o: _mhchemTexify._goInner(buf.o),\n                    q: _mhchemTexify._goInner(buf.q),\n                    d: _mhchemTexify._goInner(buf.d)\n                };\n                if (b5.a) {\n                    if (b5.a.match(/^[+\\-]/)) {\n                        b5.a = \"{\" + b5.a + \"}\";\n                    }\n                    res += b5.a + \"\\\\,\";\n                }\n                if (b5.b || b5.p) {\n                    res += \"{\\\\vphantom{A}}\";\n                    res += \"^{\\\\hphantom{\" + (b5.b || \"\") + \"}}_{\\\\hphantom{\" + (b5.p || \"\") + \"}}\";\n                    res += \"\\\\mkern-1.5mu\";\n                    res += \"{\\\\vphantom{A}}\";\n                    res += \"^{\\\\smash[t]{\\\\vphantom{2}}\\\\llap{\" + (b5.b || \"\") + \"}}\";\n                    res += \"_{\\\\vphantom{2}\\\\llap{\\\\smash[t]{\" + (b5.p || \"\") + \"}}}\";\n                }\n                if (b5.o) {\n                    if (b5.o.match(/^[+\\-]/)) {\n                        b5.o = \"{\" + b5.o + \"}\";\n                    }\n                    res += b5.o;\n                }\n                if (buf.dType === 'kv') {\n                    if (b5.d || b5.q) {\n                        res += \"{\\\\vphantom{A}}\";\n                    }\n                    if (b5.d) {\n                        res += \"^{\" + b5.d + \"}\";\n                    }\n                    if (b5.q) {\n                        res += \"_{\\\\smash[t]{\" + b5.q + \"}}\";\n                    }\n                }\n                else if (buf.dType === 'oxidation') {\n                    if (b5.d) {\n                        res += \"{\\\\vphantom{A}}\";\n                        res += \"^{\" + b5.d + \"}\";\n                    }\n                    if (b5.q) {\n                        res += \"{\\\\vphantom{A}}\";\n                        res += \"_{\\\\smash[t]{\" + b5.q + \"}}\";\n                    }\n                }\n                else {\n                    if (b5.q) {\n                        res += \"{\\\\vphantom{A}}\";\n                        res += \"_{\\\\smash[t]{\" + b5.q + \"}}\";\n                    }\n                    if (b5.d) {\n                        res += \"{\\\\vphantom{A}}\";\n                        res += \"^{\" + b5.d + \"}\";\n                    }\n                }\n                break;\n            case 'rm':\n                res = \"\\\\mathrm{\" + buf.p1 + \"}\";\n                break;\n            case 'text':\n                if (buf.p1.match(/[\\^_]/)) {\n                    buf.p1 = buf.p1.replace(\" \", \"~\").replace(\"-\", \"\\\\text{-}\");\n                    res = \"\\\\mathrm{\" + buf.p1 + \"}\";\n                }\n                else {\n                    res = \"\\\\text{\" + buf.p1 + \"}\";\n                }\n                break;\n            case 'roman numeral':\n                res = \"\\\\mathrm{\" + buf.p1 + \"}\";\n                break;\n            case 'state of aggregation':\n                res = \"\\\\mskip2mu \" + _mhchemTexify._goInner(buf.p1);\n                break;\n            case 'state of aggregation subscript':\n                res = \"\\\\mskip1mu \" + _mhchemTexify._goInner(buf.p1);\n                break;\n            case 'bond':\n                res = _mhchemTexify._getBond(buf.kind_);\n                if (!res) {\n                    throw [\"MhchemErrorBond\", \"mhchem Error. Unknown bond type (\" + buf.kind_ + \")\"];\n                }\n                break;\n            case 'frac':\n                var c = \"\\\\frac{\" + buf.p1 + \"}{\" + buf.p2 + \"}\";\n                res = \"\\\\mathchoice{\\\\textstyle\" + c + \"}{\" + c + \"}{\" + c + \"}{\" + c + \"}\";\n                break;\n            case 'pu-frac':\n                var d = \"\\\\frac{\" + _mhchemTexify._goInner(buf.p1) + \"}{\" + _mhchemTexify._goInner(buf.p2) + \"}\";\n                res = \"\\\\mathchoice{\\\\textstyle\" + d + \"}{\" + d + \"}{\" + d + \"}{\" + d + \"}\";\n                break;\n            case 'tex-math':\n                res = buf.p1 + \" \";\n                break;\n            case 'frac-ce':\n                res = \"\\\\frac{\" + _mhchemTexify._goInner(buf.p1) + \"}{\" + _mhchemTexify._goInner(buf.p2) + \"}\";\n                break;\n            case 'overset':\n                res = \"\\\\overset{\" + _mhchemTexify._goInner(buf.p1) + \"}{\" + _mhchemTexify._goInner(buf.p2) + \"}\";\n                break;\n            case 'underset':\n                res = \"\\\\underset{\" + _mhchemTexify._goInner(buf.p1) + \"}{\" + _mhchemTexify._goInner(buf.p2) + \"}\";\n                break;\n            case 'underbrace':\n                res = \"\\\\underbrace{\" + _mhchemTexify._goInner(buf.p1) + \"}_{\" + _mhchemTexify._goInner(buf.p2) + \"}\";\n                break;\n            case 'color':\n                res = \"{\\\\color{\" + buf.color1 + \"}{\" + _mhchemTexify._goInner(buf.color2) + \"}}\";\n                break;\n            case 'color0':\n                res = \"\\\\color{\" + buf.color + \"}\";\n                break;\n            case 'arrow':\n                var b6 = {\n                    rd: _mhchemTexify._goInner(buf.rd),\n                    rq: _mhchemTexify._goInner(buf.rq)\n                };\n                var arrow = _mhchemTexify._getArrow(buf.r);\n                if (b6.rd || b6.rq) {\n                    if (buf.r === \"<=>\" || buf.r === \"<=>>\" || buf.r === \"<<=>\" || buf.r === \"<-->\") {\n                        arrow = \"\\\\long\" + arrow;\n                        if (b6.rd) {\n                            arrow = \"\\\\overset{\" + b6.rd + \"}{\" + arrow + \"}\";\n                        }\n                        if (b6.rq) {\n                            if (buf.r === \"<-->\") {\n                                arrow = \"\\\\underset{\\\\lower2mu{\" + b6.rq + \"}}{\" + arrow + \"}\";\n                            }\n                            else {\n                                arrow = \"\\\\underset{\\\\lower6mu{\" + b6.rq + \"}}{\" + arrow + \"}\";\n                            }\n                        }\n                        arrow = \" {}\\\\mathrel{\" + arrow + \"}{} \";\n                    }\n                    else {\n                        if (b6.rq) {\n                            arrow += \"[{\" + b6.rq + \"}]\";\n                        }\n                        arrow += \"{\" + b6.rd + \"}\";\n                        arrow = \" {}\\\\mathrel{\\\\x\" + arrow + \"}{} \";\n                    }\n                }\n                else {\n                    arrow = \" {}\\\\mathrel{\\\\long\" + arrow + \"}{} \";\n                }\n                res = arrow;\n                break;\n            case 'operator':\n                res = _mhchemTexify._getOperator(buf.kind_);\n                break;\n            case '1st-level escape':\n                res = buf.p1 + \" \";\n                break;\n            case 'space':\n                res = \" \";\n                break;\n            case 'tinySkip':\n                res = '\\\\mkern2mu';\n                break;\n            case 'entitySkip':\n                res = \"~\";\n                break;\n            case 'pu-space-1':\n                res = \"~\";\n                break;\n            case 'pu-space-2':\n                res = \"\\\\mkern3mu \";\n                break;\n            case '1000 separator':\n                res = \"\\\\mkern2mu \";\n                break;\n            case 'commaDecimal':\n                res = \"{,}\";\n                break;\n            case 'comma enumeration L':\n                res = \"{\" + buf.p1 + \"}\\\\mkern6mu \";\n                break;\n            case 'comma enumeration M':\n                res = \"{\" + buf.p1 + \"}\\\\mkern3mu \";\n                break;\n            case 'comma enumeration S':\n                res = \"{\" + buf.p1 + \"}\\\\mkern1mu \";\n                break;\n            case 'hyphen':\n                res = \"\\\\text{-}\";\n                break;\n            case 'addition compound':\n                res = \"\\\\,{\\\\cdot}\\\\,\";\n                break;\n            case 'electron dot':\n                res = \"\\\\mkern1mu \\\\bullet\\\\mkern1mu \";\n                break;\n            case 'KV x':\n                res = \"{\\\\times}\";\n                break;\n            case 'prime':\n                res = \"\\\\prime \";\n                break;\n            case 'cdot':\n                res = \"\\\\cdot \";\n                break;\n            case 'tight cdot':\n                res = \"\\\\mkern1mu{\\\\cdot}\\\\mkern1mu \";\n                break;\n            case 'times':\n                res = \"\\\\times \";\n                break;\n            case 'circa':\n                res = \"{\\\\sim}\";\n                break;\n            case '^':\n                res = \"uparrow\";\n                break;\n            case 'v':\n                res = \"downarrow\";\n                break;\n            case 'ellipsis':\n                res = \"\\\\ldots \";\n                break;\n            case '/':\n                res = \"/\";\n                break;\n            case ' / ':\n                res = \"\\\\,/\\\\,\";\n                break;\n            default:\n                assertNever(buf);\n                throw [\"MhchemBugT\", \"mhchem bug T. Please report.\"];\n        }\n        return res;\n    },\n    _getArrow: function (a) {\n        switch (a) {\n            case \"->\": return \"rightarrow\";\n            case \"\\u2192\": return \"rightarrow\";\n            case \"\\u27F6\": return \"rightarrow\";\n            case \"<-\": return \"leftarrow\";\n            case \"<->\": return \"leftrightarrow\";\n            case \"<-->\": return \"leftrightarrows\";\n            case \"<=>\": return \"rightleftharpoons\";\n            case \"\\u21CC\": return \"rightleftharpoons\";\n            case \"<=>>\": return \"Rightleftharpoons\";\n            case \"<<=>\": return \"Leftrightharpoons\";\n            default:\n                assertNever(a);\n                throw [\"MhchemBugT\", \"mhchem bug T. Please report.\"];\n        }\n    },\n    _getBond: function (a) {\n        switch (a) {\n            case \"-\": return \"{-}\";\n            case \"1\": return \"{-}\";\n            case \"=\": return \"{=}\";\n            case \"2\": return \"{=}\";\n            case \"#\": return \"{\\\\equiv}\";\n            case \"3\": return \"{\\\\equiv}\";\n            case \"~\": return \"{\\\\tripledash}\";\n            case \"~-\": return \"{\\\\rlap{\\\\lower.1em{-}}\\\\raise.1em{\\\\tripledash}}\";\n            case \"~=\": return \"{\\\\rlap{\\\\lower.2em{-}}\\\\rlap{\\\\raise.2em{\\\\tripledash}}-}\";\n            case \"~--\": return \"{\\\\rlap{\\\\lower.2em{-}}\\\\rlap{\\\\raise.2em{\\\\tripledash}}-}\";\n            case \"-~-\": return \"{\\\\rlap{\\\\lower.2em{-}}\\\\rlap{\\\\raise.2em{-}}\\\\tripledash}\";\n            case \"...\": return \"{{\\\\cdot}{\\\\cdot}{\\\\cdot}}\";\n            case \"....\": return \"{{\\\\cdot}{\\\\cdot}{\\\\cdot}{\\\\cdot}}\";\n            case \"->\": return \"{\\\\rightarrow}\";\n            case \"<-\": return \"{\\\\leftarrow}\";\n            case \"<\": return \"{<}\";\n            case \">\": return \"{>}\";\n            default:\n                assertNever(a);\n                throw [\"MhchemBugT\", \"mhchem bug T. Please report.\"];\n        }\n    },\n    _getOperator: function (a) {\n        switch (a) {\n            case \"+\": return \" {}+{} \";\n            case \"-\": return \" {}-{} \";\n            case \"=\": return \" {}={} \";\n            case \"<\": return \" {}<{} \";\n            case \">\": return \" {}>{} \";\n            case \"<<\": return \" {}\\\\ll{} \";\n            case \">>\": return \" {}\\\\gg{} \";\n            case \"\\\\pm\": return \" {}\\\\pm{} \";\n            case \"\\\\approx\": return \" {}\\\\approx{} \";\n            case \"$\\\\approx$\": return \" {}\\\\approx{} \";\n            case \"v\": return \" \\\\downarrow{} \";\n            case \"(v)\": return \" \\\\downarrow{} \";\n            case \"^\": return \" \\\\uparrow{} \";\n            case \"(^)\": return \" \\\\uparrow{} \";\n            default:\n                assertNever(a);\n                throw [\"MhchemBugT\", \"mhchem bug T. Please report.\"];\n        }\n    }\n};\nfunction assertNever(a) { }\n"], "names": [], "sourceRoot": ""}