{"version": 3, "file": "9442.e301e4179b7c69c125d7.js?v=e301e4179b7c69c125d7", "mappings": ";;;;;;;;;;;;;;;;;AAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACnB6B;;AAE7B;AACA;AACA;;AAEO;AACP;AACA;;AAEO;AACP;AACA;;AAEO;AACP;AACA;;AAEO;AACP;AACA,kCAAkC,GAAG;AACrC;AACA;;;ACtBe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACjBe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACnBe;AACf;AACA;AACA;AACA;;;ACJuC;AACJ;AACE;;AAErC;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,8BAA8B,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEe;AACf,sCAAsC;AACtC,eAAe;AACf,kBAAkB;AAClB;AACA,cAAc,OAAO;AACrB;AACA;AACA;AACA;AACA;;AAEA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,kEAAkE,QAAQ;AAC1E;;AAEA;AACA,qEAAqE,QAAQ;AAC7E;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,qEAAqE,QAAQ;AAC7E;;AAEA;AACA,qEAAqE,QAAQ;AAC7E;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,6BAA6B,aAAa;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,gBAAgB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,0BAA0B;AAC5C;AACA;AACA;AACA;AACA;;AAEA,8BAA8B,MAAM;AACpC;AACA;AACA,qBAAqB,GAAG,2BAA2B,GAAG;AACtD;AACA;AACA;;AAEA,8BAA8B,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,+BAA+B,MAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,8BAA8B,MAAM;AACpC,cAAc,GAAG;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,eAAe,GAAG,iDAAiD,GAAG;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,kBAAkB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,mCAAmC,GAAG;AACtC;AACA,oBAAoB,gBAAgB;AACpC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,wCAAwC,OAAO;AAC/C;AACA;AACA;AACA;AACA,oBAAoB,eAAe;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,4CAA4C,QAAQ;AACpD;AACA;AACA;AACA;AACA,oBAAoB,eAAe;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,WAAW,kBAAkB;AAC7B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;;AAEA,6BAA6B,yBAAyB;AACtD;AACA,kBAAkB,SAAS,cAAc;AACzC;AACA;AACA,kBAAkB,SAAS,cAAc;AACzC;AACA;AACA;AACA;;AAEA;AACA;AACA,kBAAkB,0BAA0B;AAC5C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,gBAAgB,qBAAqB;AACrC;AACA;AACA;AACA,gBAAgB,qBAAqB;AACrC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,gBAAgB,qBAAqB;AACrC;AACA;AACA;AACA,gBAAgB,qBAAqB;AACrC;AACA;AACA;AACA;AACA;;AAEA;AACA;;;AChXA;AACA;AACA;AACA;;AAEA;AACA;AACA,8BAA8B;AAC9B;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA,+CAAe,IAAI,EAAC;;;ACjIb;;;ACAP,6BAAe,sBAAS;AACxB;AACA;AACA;AACA;;;ACJO,SAAS,OAAC;AACjB;AACA;;AAEO,SAAS,OAAC;AACjB;AACA;;;ACN6B;AACK;AACI;AACe;AACT;;AAE5C;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS,SAAI;AACb;AACA;AACA,UAAU,OAAM;AAChB,UAAU,OAAM;AAChB;;AAEA;AACA,uBAAuB,KAAK;AAC5B,qCAAqC,QAAI;AACzC;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,iEAAiE,YAAQ;AACzE;;AAEA;AACA,iEAAiE,YAAQ;AACzE;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP,SAAS,SAAI;AACb;;AAEO;AACP,SAAS,SAAI;AACb;;AAEO;AACP,UAAU,SAAI;AACd;AACA;AACA;AACA;;;ACnFwC;;AAExC;AACA;AACA;;AAEA;AACA;AACA;;AAEA,6BAAe,gCAAW;AAC1B,SAAS,cAAc;AACvB;AACA;AACA;;;ACD8B;;AAE9B;AACA;AACA,0BAA0B,iCAAM;AAChC,sBAAsB,gBAAgB,KAAK;AAC3C;AACA,GAAG;AACH;AACA,2BAA2B,iCAAM;AACjC,KAAK;AACL,UAAU;AACV,gBAAgB,+TAA+T;AAC/U,kBAAkB,uLAAuL;AACzM;AACA,mCAAmC,iCAAM;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,cAAc,iBAAiB,IAAI,QAAQ,IAAI,WAAW,IAAI,mDAAmD,IAAI,+BAA+B,mBAAmB,mBAAmB,KAAK,aAAa,oCAAoC,aAAa,qBAAqB,WAAW,IAAI,WAAW,oBAAoB,oDAAoD,IAAI,wCAAwC,IAAI,aAAa,oBAAoB,aAAa,qBAAqB,wCAAwC;AAC/gB,sBAAsB,wBAAwB;AAC9C,gCAAgC,iCAAM;AACtC;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,KAAK;AACL,2BAA2B,iCAAM;AACjC;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,iCAAM;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,iCAAM;AACZ,iEAAiE;AACjE;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,kCAAkC,iCAAM;AACxC;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,gCAAgC,iCAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,iCAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,iCAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,iCAAM;AAClC;AACA;AACA,OAAO;AACP;AACA,8BAA8B,iCAAM;AACpC;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,OAAO;AACP;AACA,4BAA4B,iCAAM;AAClC;AACA,OAAO;AACP;AACA,iCAAiC,iCAAM;AACvC;AACA;AACA,OAAO;AACP;AACA,qCAAqC,iCAAM;AAC3C;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,oCAAoC,iCAAM;AAC1C;AACA;AACA;AACA,OAAO;AACP;AACA,kCAAkC,iCAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,iCAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,gBAAgB;AAChB;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP;AACA,2BAA2B,iCAAM;AACjC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,6BAA6B,iCAAM;AACnC;AACA,OAAO;AACP;AACA,gCAAgC,iCAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,qCAAqC,iCAAM;AAC3C;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP,4DAA4D;AAC5D,gCAAgC,iCAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,iCAAiC,iCAAM;AACvC;AACA,OAAO;AACP;AACA,sCAAsC,iCAAM;AAC5C;AACA,OAAO;AACP,iBAAiB,0BAA0B;AAC3C,qCAAqC,iCAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,oBAAoB,SAAS,oDAAoD,oBAAoB,qCAAqC,eAAe;AACzJ;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,iCAAM;AACR;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA;AACA;AACA;AACA,6BAA6B,iCAAM;AACnC;AACA;AACA;AACA,EAAE,gCAAK;AACP,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,iCAAM;AACV;AACA;AACA,8BAA8B,iCAAM;AACpC;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,IAAI,iCAAM;AACV;AACA;AACA,uCAAuC,iCAAM;AAC7C,OAAO,qCAAc,kBAAkB,qCAAS;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,+BAA+B,iCAAM;AACrC,+BAA+B,iCAAM;AACrC,+BAA+B,iCAAM;AACrC,gCAAgC,aAAa;AAC7C;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA,6BAA6B,iCAAM,OAAO,qCAAS;AACnD;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,aAAa;AACb,mBAAmB;AACnB,mBAAmB;AACnB,iBAAiB;AACjB,iBAAiB;AACjB;AACA;;AAEA;AAKY;AAQO;;AAEnB;AACA;AACA;AACA,IAAI,iCAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,GAAG;AACvB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,QAAQ,IAAY;AACpB,SAAS,KAAa;AACtB,UAAU,MAAc;AACxB,WAAW,OAAe;AAC1B;AACA,2BAA2B,iCAAM;AACjC,UAAU,8BAA8B,EAAE,qCAAS;AACnD,8BAA8B,qCAAa;AAC3C;AACA;AACA,qBAAqB,sBAAQ;AAC7B;AACA,6CAA6C,sBAAQ,mDAAmD,sBAAQ;AAChH,gEAAgE,GAAG,OAAO,sBAAQ,SAAS,GAAG;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,MAAQ;AACzB;AACA;AACA;AACA;AACA,sBAAsB,6BAAc,CAAC,4BAAiB;AACtD;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH,kCAAkC,iCAAM,IAAI,gBAAgB;AAC5D;AACA;AACA;AACA,cAAc;AACd,EAAE,OAAO,EAAE,8BAA8B,EAAE,OAAO;AAClD,GAAG;AACH,iOAAiO,0BAA0B;AAC3P;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,iCAAM;AACvC;AACA;AACA,iCAAiC,iCAAM;AACvC;AACA;AACA,iCAAiC,iCAAM;AACvC;AACA;AACA;AACA;AACA,gCAAgC,oBAAsB;AACtD,EAAE,4CAAiB;AACnB,CAAC;AACD;AACA;AACA;;AAEA;AACA,4CAA4C,iCAAM;AAClD;AACA;AACA,CAAC;;AAED;AACA,gCAAgC,iCAAM;AACtC,qBAAqB;AACrB,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-sankey/node_modules/d3-array/src/min.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-sankey/src/align.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-sankey/node_modules/d3-array/src/sum.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-sankey/node_modules/d3-array/src/max.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-sankey/src/constant.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-sankey/src/sankey.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-sankey/node_modules/d3-path/src/path.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-sankey/node_modules/d3-shape/src/array.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-sankey/node_modules/d3-shape/src/constant.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-sankey/node_modules/d3-shape/src/point.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-sankey/node_modules/d3-shape/src/link/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-sankey/src/sankeyLinkHorizontal.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/sankeyDiagram-QLVOVGJD.mjs"], "sourcesContent": ["export default function min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n", "import {min} from \"d3-array\";\n\nfunction targetDepth(d) {\n  return d.target.depth;\n}\n\nexport function left(node) {\n  return node.depth;\n}\n\nexport function right(node, n) {\n  return n - 1 - node.height;\n}\n\nexport function justify(node, n) {\n  return node.sourceLinks.length ? node.depth : n - 1;\n}\n\nexport function center(node) {\n  return node.targetLinks.length ? node.depth\n      : node.sourceLinks.length ? min(node.sourceLinks, targetDepth) - 1\n      : 0;\n}\n", "export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n", "export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n", "export default function constant(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {max, min, sum} from \"d3-array\";\nimport {justify} from \"./align.js\";\nimport constant from \"./constant.js\";\n\nfunction ascendingSourceBreadth(a, b) {\n  return ascendingBreadth(a.source, b.source) || a.index - b.index;\n}\n\nfunction ascendingTargetBreadth(a, b) {\n  return ascendingBreadth(a.target, b.target) || a.index - b.index;\n}\n\nfunction ascendingBreadth(a, b) {\n  return a.y0 - b.y0;\n}\n\nfunction value(d) {\n  return d.value;\n}\n\nfunction defaultId(d) {\n  return d.index;\n}\n\nfunction defaultNodes(graph) {\n  return graph.nodes;\n}\n\nfunction defaultLinks(graph) {\n  return graph.links;\n}\n\nfunction find(nodeById, id) {\n  const node = nodeById.get(id);\n  if (!node) throw new Error(\"missing: \" + id);\n  return node;\n}\n\nfunction computeLinkBreadths({nodes}) {\n  for (const node of nodes) {\n    let y0 = node.y0;\n    let y1 = y0;\n    for (const link of node.sourceLinks) {\n      link.y0 = y0 + link.width / 2;\n      y0 += link.width;\n    }\n    for (const link of node.targetLinks) {\n      link.y1 = y1 + link.width / 2;\n      y1 += link.width;\n    }\n  }\n}\n\nexport default function Sankey() {\n  let x0 = 0, y0 = 0, x1 = 1, y1 = 1; // extent\n  let dx = 24; // nodeWidth\n  let dy = 8, py; // nodePadding\n  let id = defaultId;\n  let align = justify;\n  let sort;\n  let linkSort;\n  let nodes = defaultNodes;\n  let links = defaultLinks;\n  let iterations = 6;\n\n  function sankey() {\n    const graph = {nodes: nodes.apply(null, arguments), links: links.apply(null, arguments)};\n    computeNodeLinks(graph);\n    computeNodeValues(graph);\n    computeNodeDepths(graph);\n    computeNodeHeights(graph);\n    computeNodeBreadths(graph);\n    computeLinkBreadths(graph);\n    return graph;\n  }\n\n  sankey.update = function(graph) {\n    computeLinkBreadths(graph);\n    return graph;\n  };\n\n  sankey.nodeId = function(_) {\n    return arguments.length ? (id = typeof _ === \"function\" ? _ : constant(_), sankey) : id;\n  };\n\n  sankey.nodeAlign = function(_) {\n    return arguments.length ? (align = typeof _ === \"function\" ? _ : constant(_), sankey) : align;\n  };\n\n  sankey.nodeSort = function(_) {\n    return arguments.length ? (sort = _, sankey) : sort;\n  };\n\n  sankey.nodeWidth = function(_) {\n    return arguments.length ? (dx = +_, sankey) : dx;\n  };\n\n  sankey.nodePadding = function(_) {\n    return arguments.length ? (dy = py = +_, sankey) : dy;\n  };\n\n  sankey.nodes = function(_) {\n    return arguments.length ? (nodes = typeof _ === \"function\" ? _ : constant(_), sankey) : nodes;\n  };\n\n  sankey.links = function(_) {\n    return arguments.length ? (links = typeof _ === \"function\" ? _ : constant(_), sankey) : links;\n  };\n\n  sankey.linkSort = function(_) {\n    return arguments.length ? (linkSort = _, sankey) : linkSort;\n  };\n\n  sankey.size = function(_) {\n    return arguments.length ? (x0 = y0 = 0, x1 = +_[0], y1 = +_[1], sankey) : [x1 - x0, y1 - y0];\n  };\n\n  sankey.extent = function(_) {\n    return arguments.length ? (x0 = +_[0][0], x1 = +_[1][0], y0 = +_[0][1], y1 = +_[1][1], sankey) : [[x0, y0], [x1, y1]];\n  };\n\n  sankey.iterations = function(_) {\n    return arguments.length ? (iterations = +_, sankey) : iterations;\n  };\n\n  function computeNodeLinks({nodes, links}) {\n    for (const [i, node] of nodes.entries()) {\n      node.index = i;\n      node.sourceLinks = [];\n      node.targetLinks = [];\n    }\n    const nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d]));\n    for (const [i, link] of links.entries()) {\n      link.index = i;\n      let {source, target} = link;\n      if (typeof source !== \"object\") source = link.source = find(nodeById, source);\n      if (typeof target !== \"object\") target = link.target = find(nodeById, target);\n      source.sourceLinks.push(link);\n      target.targetLinks.push(link);\n    }\n    if (linkSort != null) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(linkSort);\n        targetLinks.sort(linkSort);\n      }\n    }\n  }\n\n  function computeNodeValues({nodes}) {\n    for (const node of nodes) {\n      node.value = node.fixedValue === undefined\n          ? Math.max(sum(node.sourceLinks, value), sum(node.targetLinks, value))\n          : node.fixedValue;\n    }\n  }\n\n  function computeNodeDepths({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.depth = x;\n        for (const {target} of node.sourceLinks) {\n          next.add(target);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeHeights({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.height = x;\n        for (const {source} of node.targetLinks) {\n          next.add(source);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeLayers({nodes}) {\n    const x = max(nodes, d => d.depth) + 1;\n    const kx = (x1 - x0 - dx) / (x - 1);\n    const columns = new Array(x);\n    for (const node of nodes) {\n      const i = Math.max(0, Math.min(x - 1, Math.floor(align.call(null, node, x))));\n      node.layer = i;\n      node.x0 = x0 + i * kx;\n      node.x1 = node.x0 + dx;\n      if (columns[i]) columns[i].push(node);\n      else columns[i] = [node];\n    }\n    if (sort) for (const column of columns) {\n      column.sort(sort);\n    }\n    return columns;\n  }\n\n  function initializeNodeBreadths(columns) {\n    const ky = min(columns, c => (y1 - y0 - (c.length - 1) * py) / sum(c, value));\n    for (const nodes of columns) {\n      let y = y0;\n      for (const node of nodes) {\n        node.y0 = y;\n        node.y1 = y + node.value * ky;\n        y = node.y1 + py;\n        for (const link of node.sourceLinks) {\n          link.width = link.value * ky;\n        }\n      }\n      y = (y1 - y + py) / (nodes.length + 1);\n      for (let i = 0; i < nodes.length; ++i) {\n        const node = nodes[i];\n        node.y0 += y * (i + 1);\n        node.y1 += y * (i + 1);\n      }\n      reorderLinks(nodes);\n    }\n  }\n\n  function computeNodeBreadths(graph) {\n    const columns = computeNodeLayers(graph);\n    py = Math.min(dy, (y1 - y0) / (max(columns, c => c.length) - 1));\n    initializeNodeBreadths(columns);\n    for (let i = 0; i < iterations; ++i) {\n      const alpha = Math.pow(0.99, i);\n      const beta = Math.max(1 - alpha, (i + 1) / iterations);\n      relaxRightToLeft(columns, alpha, beta);\n      relaxLeftToRight(columns, alpha, beta);\n    }\n  }\n\n  // Reposition each node based on its incoming (target) links.\n  function relaxLeftToRight(columns, alpha, beta) {\n    for (let i = 1, n = columns.length; i < n; ++i) {\n      const column = columns[i];\n      for (const target of column) {\n        let y = 0;\n        let w = 0;\n        for (const {source, value} of target.targetLinks) {\n          let v = value * (target.layer - source.layer);\n          y += targetTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - target.y0) * alpha;\n        target.y0 += dy;\n        target.y1 += dy;\n        reorderNodeLinks(target);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  // Reposition each node based on its outgoing (source) links.\n  function relaxRightToLeft(columns, alpha, beta) {\n    for (let n = columns.length, i = n - 2; i >= 0; --i) {\n      const column = columns[i];\n      for (const source of column) {\n        let y = 0;\n        let w = 0;\n        for (const {target, value} of source.sourceLinks) {\n          let v = value * (target.layer - source.layer);\n          y += sourceTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - source.y0) * alpha;\n        source.y0 += dy;\n        source.y1 += dy;\n        reorderNodeLinks(source);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  function resolveCollisions(nodes, alpha) {\n    const i = nodes.length >> 1;\n    const subject = nodes[i];\n    resolveCollisionsBottomToTop(nodes, subject.y0 - py, i - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, subject.y1 + py, i + 1, alpha);\n    resolveCollisionsBottomToTop(nodes, y1, nodes.length - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, y0, 0, alpha);\n  }\n\n  // Push any overlapping nodes down.\n  function resolveCollisionsTopToBottom(nodes, y, i, alpha) {\n    for (; i < nodes.length; ++i) {\n      const node = nodes[i];\n      const dy = (y - node.y0) * alpha;\n      if (dy > 1e-6) node.y0 += dy, node.y1 += dy;\n      y = node.y1 + py;\n    }\n  }\n\n  // Push any overlapping nodes up.\n  function resolveCollisionsBottomToTop(nodes, y, i, alpha) {\n    for (; i >= 0; --i) {\n      const node = nodes[i];\n      const dy = (node.y1 - y) * alpha;\n      if (dy > 1e-6) node.y0 -= dy, node.y1 -= dy;\n      y = node.y0 - py;\n    }\n  }\n\n  function reorderNodeLinks({sourceLinks, targetLinks}) {\n    if (linkSort === undefined) {\n      for (const {source: {sourceLinks}} of targetLinks) {\n        sourceLinks.sort(ascendingTargetBreadth);\n      }\n      for (const {target: {targetLinks}} of sourceLinks) {\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  function reorderLinks(nodes) {\n    if (linkSort === undefined) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(ascendingTargetBreadth);\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  // Returns the target.y0 that would produce an ideal link from source to target.\n  function targetTop(source, target) {\n    let y = source.y0 - (source.sourceLinks.length - 1) * py / 2;\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y += width + py;\n    }\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  // Returns the source.y0 that would produce an ideal link from source to target.\n  function sourceTop(source, target) {\n    let y = target.y0 - (target.targetLinks.length - 1) * py / 2;\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y += width + py;\n    }\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  return sankey;\n}\n", "var pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction Path() {\n  this._x0 = this._y0 = // start of current subpath\n  this._x1 = this._y1 = null; // end of current subpath\n  this._ = \"\";\n}\n\nfunction path() {\n  return new Path;\n}\n\nPath.prototype = path.prototype = {\n  constructor: Path,\n  moveTo: function(x, y) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y);\n  },\n  closePath: function() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  },\n  lineTo: function(x, y) {\n    this._ += \"L\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  quadraticCurveTo: function(x1, y1, x, y) {\n    this._ += \"Q\" + (+x1) + \",\" + (+y1) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  bezierCurveTo: function(x1, y1, x2, y2, x, y) {\n    this._ += \"C\" + (+x1) + \",\" + (+y1) + \",\" + (+x2) + \",\" + (+y2) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  arcTo: function(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n    var x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._ += \"M\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._ += \"L\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      var x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._ += \"L\" + (x1 + t01 * x01) + \",\" + (y1 + t01 * y01);\n      }\n\n      this._ += \"A\" + r + \",\" + r + \",0,0,\" + (+(y01 * x20 > x01 * y20)) + \",\" + (this._x1 = x1 + t21 * x21) + \",\" + (this._y1 = y1 + t21 * y21);\n    }\n  },\n  arc: function(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n    var dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._ += \"M\" + x0 + \",\" + y0;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._ += \"L\" + x0 + \",\" + y0;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (x - dx) + \",\" + (y - dy) + \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (this._x1 = x0) + \",\" + (this._y1 = y0);\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,\" + (+(da >= pi)) + \",\" + cw + \",\" + (this._x1 = x + r * Math.cos(a1)) + \",\" + (this._y1 = y + r * Math.sin(a1));\n    }\n  },\n  rect: function(x, y, w, h) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y) + \"h\" + (+w) + \"v\" + (+h) + \"h\" + (-w) + \"Z\";\n  },\n  toString: function() {\n    return this._;\n  }\n};\n\nexport default path;\n", "export var slice = Array.prototype.slice;\n", "export default function(x) {\n  return function constant() {\n    return x;\n  };\n}\n", "export function x(p) {\n  return p[0];\n}\n\nexport function y(p) {\n  return p[1];\n}\n", "import {path} from \"d3-path\";\nimport {slice} from \"../array.js\";\nimport constant from \"../constant.js\";\nimport {x as pointX, y as pointY} from \"../point.js\";\nimport pointRadial from \"../pointRadial.js\";\n\nfunction linkSource(d) {\n  return d.source;\n}\n\nfunction linkTarget(d) {\n  return d.target;\n}\n\nfunction link(curve) {\n  var source = linkSource,\n      target = linkTarget,\n      x = pointX,\n      y = pointY,\n      context = null;\n\n  function link() {\n    var buffer, argv = slice.call(arguments), s = source.apply(this, argv), t = target.apply(this, argv);\n    if (!context) context = buffer = path();\n    curve(context, +x.apply(this, (argv[0] = s, argv)), +y.apply(this, argv), +x.apply(this, (argv[0] = t, argv)), +y.apply(this, argv));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  link.source = function(_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n\n  link.target = function(_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n\n  link.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), link) : x;\n  };\n\n  link.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), link) : y;\n  };\n\n  link.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), link) : context;\n  };\n\n  return link;\n}\n\nfunction curveHorizontal(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0 = (x0 + x1) / 2, y0, x0, y1, x1, y1);\n}\n\nfunction curveVertical(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0, y0 = (y0 + y1) / 2, x1, y0, x1, y1);\n}\n\nfunction curveRadial(context, x0, y0, x1, y1) {\n  var p0 = pointRadial(x0, y0),\n      p1 = pointRadial(x0, y0 = (y0 + y1) / 2),\n      p2 = pointRadial(x1, y0),\n      p3 = pointRadial(x1, y1);\n  context.moveTo(p0[0], p0[1]);\n  context.bezierCurveTo(p1[0], p1[1], p2[0], p2[1], p3[0], p3[1]);\n}\n\nexport function linkHorizontal() {\n  return link(curveHorizontal);\n}\n\nexport function linkVertical() {\n  return link(curveVertical);\n}\n\nexport function linkRadial() {\n  var l = link(curveRadial);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}\n", "import {linkHorizontal} from \"d3-shape\";\n\nfunction horizontalSource(d) {\n  return [d.source.x1, d.y0];\n}\n\nfunction horizontalTarget(d) {\n  return [d.target.x0, d.y1];\n}\n\nexport default function() {\n  return linkHorizontal()\n      .source(horizontalSource)\n      .target(horizontalTarget);\n}\n", "import {\n  __name,\n  clear,\n  common_default,\n  defaultConfig2 as defaultConfig,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n  setupGraphViewbox\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/sankey/parser/sankey.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 9], $V1 = [1, 10], $V2 = [1, 5, 10, 12];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"SANKEY\": 4, \"NEWLINE\": 5, \"csv\": 6, \"opt_eof\": 7, \"record\": 8, \"csv_tail\": 9, \"EOF\": 10, \"field[source]\": 11, \"COMMA\": 12, \"field[target]\": 13, \"field[value]\": 14, \"field\": 15, \"escaped\": 16, \"non_escaped\": 17, \"DQUOTE\": 18, \"ESCAPED_TEXT\": 19, \"NON_ESCAPED_TEXT\": 20, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SANKEY\", 5: \"NEWLINE\", 10: \"EOF\", 11: \"field[source]\", 12: \"COMMA\", 13: \"field[target]\", 14: \"field[value]\", 18: \"DQUOTE\", 19: \"ESCAPED_TEXT\", 20: \"NON_ESCAPED_TEXT\" },\n    productions_: [0, [3, 4], [6, 2], [9, 2], [9, 0], [7, 1], [7, 0], [8, 5], [15, 1], [15, 1], [16, 3], [17, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 7:\n          const source = yy.findOrCreateNode($$[$0 - 4].trim().replaceAll('\"\"', '\"'));\n          const target = yy.findOrCreateNode($$[$0 - 2].trim().replaceAll('\"\"', '\"'));\n          const value = parseFloat($$[$0].trim());\n          yy.addLink(source, target, value);\n          break;\n        case 8:\n        case 9:\n        case 11:\n          this.$ = $$[$0];\n          break;\n        case 10:\n          this.$ = $$[$0 - 1];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, { 5: [1, 3] }, { 6: 4, 8: 5, 15: 6, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 1: [2, 6], 7: 11, 10: [1, 12] }, o($V1, [2, 4], { 9: 13, 5: [1, 14] }), { 12: [1, 15] }, o($V2, [2, 8]), o($V2, [2, 9]), { 19: [1, 16] }, o($V2, [2, 11]), { 1: [2, 1] }, { 1: [2, 5] }, o($V1, [2, 2]), { 6: 17, 8: 5, 15: 6, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 15: 18, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 18: [1, 19] }, o($V1, [2, 3]), { 12: [1, 20] }, o($V2, [2, 10]), { 15: 21, 16: 7, 17: 8, 18: $V0, 20: $V1 }, o([1, 5, 10], [2, 7])],\n    defaultActions: { 11: [2, 1], 12: [2, 5] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.pushState(\"csv\");\n            return 4;\n            break;\n          case 1:\n            return 10;\n            break;\n          case 2:\n            return 5;\n            break;\n          case 3:\n            return 12;\n            break;\n          case 4:\n            this.pushState(\"escaped_text\");\n            return 18;\n            break;\n          case 5:\n            return 20;\n            break;\n          case 6:\n            this.popState(\"escaped_text\");\n            return 18;\n            break;\n          case 7:\n            return 19;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:sankey-beta\\b)/i, /^(?:$)/i, /^(?:((\\u000D\\u000A)|(\\u000A)))/i, /^(?:(\\u002C))/i, /^(?:(\\u0022))/i, /^(?:([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])*)/i, /^(?:(\\u0022)(?!(\\u0022)))/i, /^(?:(([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])|(\\u002C)|(\\u000D)|(\\u000A)|(\\u0022)(\\u0022))*)/i],\n      conditions: { \"csv\": { \"rules\": [1, 2, 3, 4, 5, 6, 7], \"inclusive\": false }, \"escaped_text\": { \"rules\": [6, 7], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar sankey_default = parser;\n\n// src/diagrams/sankey/sankeyDB.ts\nvar links = [];\nvar nodes = [];\nvar nodesMap = /* @__PURE__ */ new Map();\nvar clear2 = /* @__PURE__ */ __name(() => {\n  links = [];\n  nodes = [];\n  nodesMap = /* @__PURE__ */ new Map();\n  clear();\n}, \"clear\");\nvar SankeyLink = class {\n  constructor(source, target, value = 0) {\n    this.source = source;\n    this.target = target;\n    this.value = value;\n  }\n  static {\n    __name(this, \"SankeyLink\");\n  }\n};\nvar addLink = /* @__PURE__ */ __name((source, target, value) => {\n  links.push(new SankeyLink(source, target, value));\n}, \"addLink\");\nvar SankeyNode = class {\n  constructor(ID) {\n    this.ID = ID;\n  }\n  static {\n    __name(this, \"SankeyNode\");\n  }\n};\nvar findOrCreateNode = /* @__PURE__ */ __name((ID) => {\n  ID = common_default.sanitizeText(ID, getConfig());\n  let node = nodesMap.get(ID);\n  if (node === void 0) {\n    node = new SankeyNode(ID);\n    nodesMap.set(ID, node);\n    nodes.push(node);\n  }\n  return node;\n}, \"findOrCreateNode\");\nvar getNodes = /* @__PURE__ */ __name(() => nodes, \"getNodes\");\nvar getLinks = /* @__PURE__ */ __name(() => links, \"getLinks\");\nvar getGraph = /* @__PURE__ */ __name(() => ({\n  nodes: nodes.map((node) => ({ id: node.ID })),\n  links: links.map((link) => ({\n    source: link.source.ID,\n    target: link.target.ID,\n    value: link.value\n  }))\n}), \"getGraph\");\nvar sankeyDB_default = {\n  nodesMap,\n  getConfig: /* @__PURE__ */ __name(() => getConfig().sankey, \"getConfig\"),\n  getNodes,\n  getLinks,\n  getGraph,\n  addLink,\n  findOrCreateNode,\n  getAccTitle,\n  setAccTitle,\n  getAccDescription,\n  setAccDescription,\n  getDiagramTitle,\n  setDiagramTitle,\n  clear: clear2\n};\n\n// src/diagrams/sankey/sankeyRenderer.ts\nimport {\n  select as d3select,\n  scaleOrdinal as d3scaleOrdinal,\n  schemeTableau10 as d3schemeTableau10\n} from \"d3\";\nimport {\n  sankey as d3Sankey,\n  sankeyLinkHorizontal as d3SankeyLinkHorizontal,\n  sankeyLeft as d3SankeyLeft,\n  sankeyRight as d3SankeyRight,\n  sankeyCenter as d3SankeyCenter,\n  sankeyJustify as d3SankeyJustify\n} from \"d3-sankey\";\n\n// src/rendering-util/uid.ts\nvar Uid = class _Uid {\n  static {\n    __name(this, \"Uid\");\n  }\n  static {\n    this.count = 0;\n  }\n  static next(name) {\n    return new _Uid(name + ++_Uid.count);\n  }\n  constructor(id) {\n    this.id = id;\n    this.href = `#${id}`;\n  }\n  toString() {\n    return \"url(\" + this.href + \")\";\n  }\n};\n\n// src/diagrams/sankey/sankeyRenderer.ts\nvar alignmentsMap = {\n  left: d3SankeyLeft,\n  right: d3SankeyRight,\n  center: d3SankeyCenter,\n  justify: d3SankeyJustify\n};\nvar draw = /* @__PURE__ */ __name(function(text, id, _version, diagObj) {\n  const { securityLevel, sankey: conf } = getConfig();\n  const defaultSankeyConfig = defaultConfig.sankey;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = d3select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? d3select(sandboxElement.nodes()[0].contentDocument.body) : d3select(\"body\");\n  const svg = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : d3select(`[id=\"${id}\"]`);\n  const width = conf?.width ?? defaultSankeyConfig.width;\n  const height = conf?.height ?? defaultSankeyConfig.width;\n  const useMaxWidth = conf?.useMaxWidth ?? defaultSankeyConfig.useMaxWidth;\n  const nodeAlignment = conf?.nodeAlignment ?? defaultSankeyConfig.nodeAlignment;\n  const prefix = conf?.prefix ?? defaultSankeyConfig.prefix;\n  const suffix = conf?.suffix ?? defaultSankeyConfig.suffix;\n  const showValues = conf?.showValues ?? defaultSankeyConfig.showValues;\n  const graph = diagObj.db.getGraph();\n  const nodeAlign = alignmentsMap[nodeAlignment];\n  const nodeWidth = 10;\n  const sankey = d3Sankey().nodeId((d) => d.id).nodeWidth(nodeWidth).nodePadding(10 + (showValues ? 15 : 0)).nodeAlign(nodeAlign).extent([\n    [0, 0],\n    [width, height]\n  ]);\n  sankey(graph);\n  const colorScheme = d3scaleOrdinal(d3schemeTableau10);\n  svg.append(\"g\").attr(\"class\", \"nodes\").selectAll(\".node\").data(graph.nodes).join(\"g\").attr(\"class\", \"node\").attr(\"id\", (d) => (d.uid = Uid.next(\"node-\")).id).attr(\"transform\", function(d) {\n    return \"translate(\" + d.x0 + \",\" + d.y0 + \")\";\n  }).attr(\"x\", (d) => d.x0).attr(\"y\", (d) => d.y0).append(\"rect\").attr(\"height\", (d) => {\n    return d.y1 - d.y0;\n  }).attr(\"width\", (d) => d.x1 - d.x0).attr(\"fill\", (d) => colorScheme(d.id));\n  const getText = /* @__PURE__ */ __name(({ id: id2, value }) => {\n    if (!showValues) {\n      return id2;\n    }\n    return `${id2}\n${prefix}${Math.round(value * 100) / 100}${suffix}`;\n  }, \"getText\");\n  svg.append(\"g\").attr(\"class\", \"node-labels\").attr(\"font-size\", 14).selectAll(\"text\").data(graph.nodes).join(\"text\").attr(\"x\", (d) => d.x0 < width / 2 ? d.x1 + 6 : d.x0 - 6).attr(\"y\", (d) => (d.y1 + d.y0) / 2).attr(\"dy\", `${showValues ? \"0\" : \"0.35\"}em`).attr(\"text-anchor\", (d) => d.x0 < width / 2 ? \"start\" : \"end\").text(getText);\n  const link = svg.append(\"g\").attr(\"class\", \"links\").attr(\"fill\", \"none\").attr(\"stroke-opacity\", 0.5).selectAll(\".link\").data(graph.links).join(\"g\").attr(\"class\", \"link\").style(\"mix-blend-mode\", \"multiply\");\n  const linkColor = conf?.linkColor ?? \"gradient\";\n  if (linkColor === \"gradient\") {\n    const gradient = link.append(\"linearGradient\").attr(\"id\", (d) => (d.uid = Uid.next(\"linearGradient-\")).id).attr(\"gradientUnits\", \"userSpaceOnUse\").attr(\"x1\", (d) => d.source.x1).attr(\"x2\", (d) => d.target.x0);\n    gradient.append(\"stop\").attr(\"offset\", \"0%\").attr(\"stop-color\", (d) => colorScheme(d.source.id));\n    gradient.append(\"stop\").attr(\"offset\", \"100%\").attr(\"stop-color\", (d) => colorScheme(d.target.id));\n  }\n  let coloring;\n  switch (linkColor) {\n    case \"gradient\":\n      coloring = /* @__PURE__ */ __name((d) => d.uid, \"coloring\");\n      break;\n    case \"source\":\n      coloring = /* @__PURE__ */ __name((d) => colorScheme(d.source.id), \"coloring\");\n      break;\n    case \"target\":\n      coloring = /* @__PURE__ */ __name((d) => colorScheme(d.target.id), \"coloring\");\n      break;\n    default:\n      coloring = linkColor;\n  }\n  link.append(\"path\").attr(\"d\", d3SankeyLinkHorizontal()).attr(\"stroke\", coloring).attr(\"stroke-width\", (d) => Math.max(1, d.width));\n  setupGraphViewbox(void 0, svg, 0, useMaxWidth);\n}, \"draw\");\nvar sankeyRenderer_default = {\n  draw\n};\n\n// src/diagrams/sankey/sankeyUtils.ts\nvar prepareTextForParsing = /* @__PURE__ */ __name((text) => {\n  const textToParse = text.replaceAll(/^[^\\S\\n\\r]+|[^\\S\\n\\r]+$/g, \"\").replaceAll(/([\\n\\r])+/g, \"\\n\").trim();\n  return textToParse;\n}, \"prepareTextForParsing\");\n\n// src/diagrams/sankey/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `.label {\n      font-family: ${options.fontFamily};\n    }`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/sankey/sankeyDiagram.ts\nvar originalParse = sankey_default.parse.bind(sankey_default);\nsankey_default.parse = (text) => originalParse(prepareTextForParsing(text));\nvar diagram = {\n  styles: styles_default,\n  parser: sankey_default,\n  db: sankeyDB_default,\n  renderer: sankeyRenderer_default\n};\nexport {\n  diagram\n};\n"], "names": [], "sourceRoot": ""}