{"cells": [{"cell_type": "markdown", "id": "f4169bfb-769a-4db3-833e-c827f19024b2", "metadata": {"id": "f4169bfb-769a-4db3-833e-c827f19024b2"}, "source": ["# Build a Planning Agent for Deep Research & Structured Report Generation with LangGraph\n", "\n", "### IMPORTANT: Be Careful this Agent will do a lot of searches so if you run it too many times you can easily exhaust your API limits for Tavily Search if you are on the free tier\n", "\n", "In this project we will be building a Planning Agent for Deep Research and Structured Report Generation in the form of Wiki-style Reports (structured with key sections and section headings)\n", "\n", "\n", "![](https://i.imgur.com/STSC73k.png)\n", "\n", "\n", "### Planning Agent for Deep Research and Structured Report Generation\n", "\n", "This project focuses on building a **Planning Agent for Deep Research and Structured Report Generation**. The agent automates the process of analyzing a user-defined topic, performing web research, and generating a well-structured report. The workflow includes the following components:\n", "\n", "1. **Report Planning**:\n", "   - The agent analyzes the user-provided **topic** and **default report template** to create a custom plan for the report.\n", "   - Sections such as **Introduction**, **Key Sections**, and **Conclusion** are defined based on the topic.\n", "   - A **web search tool** is used to collect information required before deciding the main sections.\n", "\n", "2. **Parallel Execution for Research and Writing**:\n", "   - The agent uses **parallel execution** to efficiently perform:\n", "     - **Web Research**: Queries are generated for each section and executed via the web search tool to retrieve up-to-date information.\n", "     - **Section Writing**: The retrieved data is used to write content for each section, with the following process:\n", "       - The **Researcher** gathers relevant data from the web.\n", "       - The **Section Writer** uses the data to generate structured content for the assigned section.\n", "\n", "3. **Formatting Completed Sections**:\n", "   - Once all sections are written, they are formatted to ensure consistency and adherence to the report structure.\n", "\n", "4. **Introduction and Conclusion Writing**:\n", "   - After the main sections are completed and formatted:\n", "     - The **Introduction** and **Conclusion** are written based on the content of the remaining sections (in parallel)\n", "     - This process ensures that these sections align with the overall flow and insights of the report.\n", "\n", "5. **Final Compilation**:\n", "   - All completed sections are compiled together to generate the **final report**.\n", "   - The final output is a comprehensive and structured document in the style of wiki docs.\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "9hEI3WL328vZ", "metadata": {"id": "9hEI3WL328vZ"}, "source": ["## Install OpenAI, LangGraph and LangChain dependencies"]}, {"cell_type": "code", "execution_count": null, "id": "618eab5c-4ef7-4273-8e0b-a9c847897ed7", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "618eab5c-4ef7-4273-8e0b-a9c847897ed7", "outputId": "1a5d91c7-f950-4224-a9ec-a04cbb87e93f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting langchain==0.3.14\n", "  Downloading langchain-0.3.14-py3-none-any.whl.metadata (7.1 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.11/dist-packages (from langchain==0.3.14) (6.0.2)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.11/dist-packages (from langchain==0.3.14) (2.0.37)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.11/dist-packages (from langchain==0.3.14) (3.11.11)\n", "Requirement already satisfied: langchain-core<0.4.0,>=0.3.29 in /usr/local/lib/python3.11/dist-packages (from langchain==0.3.14) (0.3.31)\n", "Requirement already satisfied: langchain-text-splitters<0.4.0,>=0.3.3 in /usr/local/lib/python3.11/dist-packages (from langchain==0.3.14) (0.3.5)\n", "Collecting langsmith<0.3,>=0.1.17 (from langchain==0.3.14)\n", "  Downloading langsmith-0.2.11-py3-none-any.whl.metadata (14 kB)\n", "Requirement already satisfied: numpy<2,>=1.22.4 in /usr/local/lib/python3.11/dist-packages (from langchain==0.3.14) (1.26.4)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /usr/local/lib/python3.11/dist-packages (from langchain==0.3.14) (2.10.6)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.11/dist-packages (from langchain==0.3.14) (2.32.3)\n", "Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in /usr/local/lib/python3.11/dist-packages (from langchain==0.3.14) (9.0.0)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain==0.3.14) (2.4.4)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain==0.3.14) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain==0.3.14) (25.1.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain==0.3.14) (1.5.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain==0.3.14) (6.1.0)\n", "Requirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain==0.3.14) (0.2.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain==0.3.14) (1.18.3)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.11/dist-packages (from langchain-core<0.4.0,>=0.3.29->langchain==0.3.14) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.11/dist-packages (from langchain-core<0.4.0,>=0.3.29->langchain==0.3.14) (24.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.11/dist-packages (from langchain-core<0.4.0,>=0.3.29->langchain==0.3.14) (4.12.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.3,>=0.1.17->langchain==0.3.14) (0.28.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.3,>=0.1.17->langchain==0.3.14) (3.10.15)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.3,>=0.1.17->langchain==0.3.14) (1.0.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain==0.3.14) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain==0.3.14) (2.27.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain==0.3.14) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain==0.3.14) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain==0.3.14) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain==0.3.14) (2024.12.14)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.11/dist-packages (from SQLAlchemy<3,>=1.4->langchain==0.3.14) (3.1.1)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->langsmith<0.3,>=0.1.17->langchain==0.3.14) (3.7.1)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->langsmith<0.3,>=0.1.17->langchain==0.3.14) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.3,>=0.1.17->langchain==0.3.14) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.11/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.4.0,>=0.3.29->langchain==0.3.14) (3.0.0)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.3,>=0.1.17->langchain==0.3.14) (1.3.1)\n", "Downloading langchain-0.3.14-py3-none-any.whl (1.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m12.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langsmith-0.2.11-py3-none-any.whl (326 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m326.9/326.9 kB\u001b[0m \u001b[31m19.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: langsmith, langchain\n", "  Attempting uninstall: langsmith\n", "    Found existing installation: langsmith 0.3.1\n", "    Uninstalling langsmith-0.3.1:\n", "      Successfully uninstalled langsmith-0.3.1\n", "  Attempting uninstall: langchain\n", "    Found existing installation: langchain 0.3.15\n", "    Uninstalling langchain-0.3.15:\n", "      Successfully uninstalled langchain-0.3.15\n", "Successfully installed langchain-0.3.14 langsmith-0.2.11\n", "Collecting langchain-openai==0.3.0\n", "  Downloading langchain_openai-0.3.0-py3-none-any.whl.metadata (2.7 kB)\n", "Requirement already satisfied: langchain-core<0.4.0,>=0.3.29 in /usr/local/lib/python3.11/dist-packages (from langchain-openai==0.3.0) (0.3.31)\n", "Requirement already satisfied: openai<2.0.0,>=1.58.1 in /usr/local/lib/python3.11/dist-packages (from langchain-openai==0.3.0) (1.59.9)\n", "Collecting tiktoken<1,>=0.7 (from langchain-openai==0.3.0)\n", "  Downloading tiktoken-0.8.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.6 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.11/dist-packages (from langchain-core<0.4.0,>=0.3.29->langchain-openai==0.3.0) (6.0.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.11/dist-packages (from langchain-core<0.4.0,>=0.3.29->langchain-openai==0.3.0) (1.33)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.125 in /usr/local/lib/python3.11/dist-packages (from langchain-core<0.4.0,>=0.3.29->langchain-openai==0.3.0) (0.2.11)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.11/dist-packages (from langchain-core<0.4.0,>=0.3.29->langchain-openai==0.3.0) (24.2)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.5.2 in /usr/local/lib/python3.11/dist-packages (from langchain-core<0.4.0,>=0.3.29->langchain-openai==0.3.0) (2.10.6)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in /usr/local/lib/python3.11/dist-packages (from langchain-core<0.4.0,>=0.3.29->langchain-openai==0.3.0) (9.0.0)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.11/dist-packages (from langchain-core<0.4.0,>=0.3.29->langchain-openai==0.3.0) (4.12.2)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from openai<2.0.0,>=1.58.1->langchain-openai==0.3.0) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/local/lib/python3.11/dist-packages (from openai<2.0.0,>=1.58.1->langchain-openai==0.3.0) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from openai<2.0.0,>=1.58.1->langchain-openai==0.3.0) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from openai<2.0.0,>=1.58.1->langchain-openai==0.3.0) (0.8.2)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.11/dist-packages (from openai<2.0.0,>=1.58.1->langchain-openai==0.3.0) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /usr/local/lib/python3.11/dist-packages (from openai<2.0.0,>=1.58.1->langchain-openai==0.3.0) (4.67.1)\n", "Requirement already satisfied: regex>=2022.1.18 in /usr/local/lib/python3.11/dist-packages (from tiktoken<1,>=0.7->langchain-openai==0.3.0) (2024.11.6)\n", "Requirement already satisfied: requests>=2.26.0 in /usr/local/lib/python3.11/dist-packages (from tiktoken<1,>=0.7->langchain-openai==0.3.0) (2.32.3)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.58.1->langchain-openai==0.3.0) (3.10)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.58.1->langchain-openai==0.3.0) (2024.12.14)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.58.1->langchain-openai==0.3.0) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai<2.0.0,>=1.58.1->langchain-openai==0.3.0) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.11/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.4.0,>=0.3.29->langchain-openai==0.3.0) (3.0.0)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.125->langchain-core<0.4.0,>=0.3.29->langchain-openai==0.3.0) (3.10.15)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.125->langchain-core<0.4.0,>=0.3.29->langchain-openai==0.3.0) (1.0.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.5.2->langchain-core<0.4.0,>=0.3.29->langchain-openai==0.3.0) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.5.2->langchain-core<0.4.0,>=0.3.29->langchain-openai==0.3.0) (2.27.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain-openai==0.3.0) (3.4.1)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain-openai==0.3.0) (2.3.0)\n", "Downloading langchain_openai-0.3.0-py3-none-any.whl (54 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m54.2/54.2 kB\u001b[0m \u001b[31m2.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tiktoken-0.8.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.2/1.2 MB\u001b[0m \u001b[31m16.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: tiktoken, langchain-openai\n", "Successfully installed langchain-openai-0.3.0 tiktoken-0.8.0\n", "Collecting langchain-community==0.3.14\n", "  Downloading langchain_community-0.3.14-py3-none-any.whl.metadata (2.9 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.11/dist-packages (from langchain-community==0.3.14) (6.0.2)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.11/dist-packages (from langchain-community==0.3.14) (2.0.37)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.11/dist-packages (from langchain-community==0.3.14) (3.11.11)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain-community==0.3.14)\n", "  Downloading dataclasses_json-0.6.7-py3-none-any.whl.metadata (25 kB)\n", "Collecting httpx-sse<0.5.0,>=0.4.0 (from langchain-community==0.3.14)\n", "  Downloading httpx_sse-0.4.0-py3-none-any.whl.metadata (9.0 kB)\n", "Requirement already satisfied: langchain<0.4.0,>=0.3.14 in /usr/local/lib/python3.11/dist-packages (from langchain-community==0.3.14) (0.3.14)\n", "Requirement already satisfied: langchain-core<0.4.0,>=0.3.29 in /usr/local/lib/python3.11/dist-packages (from langchain-community==0.3.14) (0.3.31)\n", "Requirement already satisfied: langsmith<0.3,>=0.1.125 in /usr/local/lib/python3.11/dist-packages (from langchain-community==0.3.14) (0.2.11)\n", "Requirement already satisfied: numpy<2,>=1.22.4 in /usr/local/lib/python3.11/dist-packages (from langchain-community==0.3.14) (1.26.4)\n", "Collecting pydantic-settings<3.0.0,>=2.4.0 (from langchain-community==0.3.14)\n", "  Downloading pydantic_settings-2.7.1-py3-none-any.whl.metadata (3.5 kB)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.11/dist-packages (from langchain-community==0.3.14) (2.32.3)\n", "Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in /usr/local/lib/python3.11/dist-packages (from langchain-community==0.3.14) (9.0.0)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community==0.3.14) (2.4.4)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community==0.3.14) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community==0.3.14) (25.1.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community==0.3.14) (1.5.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community==0.3.14) (6.1.0)\n", "Requirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community==0.3.14) (0.2.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community==0.3.14) (1.18.3)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community==0.3.14)\n", "  Downloading marshmallow-3.26.0-py3-none-any.whl.metadata (7.3 kB)\n", "Collecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community==0.3.14)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: langchain-text-splitters<0.4.0,>=0.3.3 in /usr/local/lib/python3.11/dist-packages (from langchain<0.4.0,>=0.3.14->langchain-community==0.3.14) (0.3.5)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /usr/local/lib/python3.11/dist-packages (from langchain<0.4.0,>=0.3.14->langchain-community==0.3.14) (2.10.6)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.11/dist-packages (from langchain-core<0.4.0,>=0.3.29->langchain-community==0.3.14) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.11/dist-packages (from langchain-core<0.4.0,>=0.3.29->langchain-community==0.3.14) (24.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.11/dist-packages (from langchain-core<0.4.0,>=0.3.29->langchain-community==0.3.14) (4.12.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.3,>=0.1.125->langchain-community==0.3.14) (0.28.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.3,>=0.1.125->langchain-community==0.3.14) (3.10.15)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.3,>=0.1.125->langchain-community==0.3.14) (1.0.0)\n", "Collecting python-dotenv>=0.21.0 (from pydantic-settings<3.0.0,>=2.4.0->langchain-community==0.3.14)\n", "  Downloading python_dotenv-1.0.1-py3-none-any.whl.metadata (23 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain-community==0.3.14) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain-community==0.3.14) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain-community==0.3.14) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain-community==0.3.14) (2024.12.14)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.11/dist-packages (from SQLAlchemy<3,>=1.4->langchain-community==0.3.14) (3.1.1)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->langsmith<0.3,>=0.1.125->langchain-community==0.3.14) (3.7.1)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->langsmith<0.3,>=0.1.125->langchain-community==0.3.14) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.3,>=0.1.125->langchain-community==0.3.14) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.11/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.4.0,>=0.3.29->langchain-community==0.3.14) (3.0.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain<0.4.0,>=0.3.14->langchain-community==0.3.14) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain<0.4.0,>=0.3.14->langchain-community==0.3.14) (2.27.2)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community==0.3.14)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl.metadata (1.1 kB)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.3,>=0.1.125->langchain-community==0.3.14) (1.3.1)\n", "Downloading langchain_community-0.3.14-py3-none-any.whl (2.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.5/2.5 MB\u001b[0m \u001b[31m29.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading dataclasses_json-0.6.7-py3-none-any.whl (28 kB)\n", "Downloading httpx_sse-0.4.0-py3-none-any.whl (7.8 kB)\n", "Downloading pydantic_settings-2.7.1-py3-none-any.whl (29 kB)\n", "Downloading marshmallow-3.26.0-py3-none-any.whl (50 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.8/50.8 kB\u001b[0m \u001b[31m3.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading python_dotenv-1.0.1-py3-none-any.whl (19 kB)\n", "Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: python-dotenv, mypy-extensions, marshmallow, httpx-sse, typing-inspect, pydantic-settings, dataclasses-json, langchain-community\n", "Successfully installed dataclasses-json-0.6.7 httpx-sse-0.4.0 langchain-community-0.3.14 marshmallow-3.26.0 mypy-extensions-1.0.0 pydantic-settings-2.7.1 python-dotenv-1.0.1 typing-inspect-0.9.0\n", "Collecting langgraph==0.2.64\n", "  Downloading langgraph-0.2.64-py3-none-any.whl.metadata (16 kB)\n", "Requirement already satisfied: langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.15,!=0.3.16,!=0.3.17,!=0.3.18,!=0.3.19,!=0.3.2,!=0.3.20,!=0.3.21,!=0.3.22,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43 in /usr/local/lib/python3.11/dist-packages (from langgraph==0.2.64) (0.3.31)\n", "Collecting langgraph-checkpoint<3.0.0,>=2.0.10 (from langgraph==0.2.64)\n", "  Downloading langgraph_checkpoint-2.0.10-py3-none-any.whl.metadata (4.6 kB)\n", "Collecting langgraph-sdk<0.2.0,>=0.1.42 (from langgraph==0.2.64)\n", "  Downloading langgraph_sdk-0.1.51-py3-none-any.whl.metadata (1.8 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.11/dist-packages (from langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.15,!=0.3.16,!=0.3.17,!=0.3.18,!=0.3.19,!=0.3.2,!=0.3.20,!=0.3.21,!=0.3.22,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langgraph==0.2.64) (6.0.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.11/dist-packages (from langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.15,!=0.3.16,!=0.3.17,!=0.3.18,!=0.3.19,!=0.3.2,!=0.3.20,!=0.3.21,!=0.3.22,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langgraph==0.2.64) (1.33)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.125 in /usr/local/lib/python3.11/dist-packages (from langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.15,!=0.3.16,!=0.3.17,!=0.3.18,!=0.3.19,!=0.3.2,!=0.3.20,!=0.3.21,!=0.3.22,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langgraph==0.2.64) (0.2.11)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.11/dist-packages (from langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.15,!=0.3.16,!=0.3.17,!=0.3.18,!=0.3.19,!=0.3.2,!=0.3.20,!=0.3.21,!=0.3.22,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langgraph==0.2.64) (24.2)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.5.2 in /usr/local/lib/python3.11/dist-packages (from langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.15,!=0.3.16,!=0.3.17,!=0.3.18,!=0.3.19,!=0.3.2,!=0.3.20,!=0.3.21,!=0.3.22,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langgraph==0.2.64) (2.10.6)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in /usr/local/lib/python3.11/dist-packages (from langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.15,!=0.3.16,!=0.3.17,!=0.3.18,!=0.3.19,!=0.3.2,!=0.3.20,!=0.3.21,!=0.3.22,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langgraph==0.2.64) (9.0.0)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.11/dist-packages (from langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.15,!=0.3.16,!=0.3.17,!=0.3.18,!=0.3.19,!=0.3.2,!=0.3.20,!=0.3.21,!=0.3.22,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langgraph==0.2.64) (4.12.2)\n", "Requirement already satisfied: msgpack<2.0.0,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from langgraph-checkpoint<3.0.0,>=2.0.10->langgraph==0.2.64) (1.1.0)\n", "Requirement already satisfied: httpx>=0.25.2 in /usr/local/lib/python3.11/dist-packages (from langgraph-sdk<0.2.0,>=0.1.42->langgraph==0.2.64) (0.28.1)\n", "Requirement already satisfied: orjson>=3.10.1 in /usr/local/lib/python3.11/dist-packages (from langgraph-sdk<0.2.0,>=0.1.42->langgraph==0.2.64) (3.10.15)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.11/dist-packages (from httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph==0.2.64) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph==0.2.64) (2024.12.14)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph==0.2.64) (1.0.7)\n", "Requirement already satisfied: idna in /usr/local/lib/python3.11/dist-packages (from httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph==0.2.64) (3.10)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph==0.2.64) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.11/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.15,!=0.3.16,!=0.3.17,!=0.3.18,!=0.3.19,!=0.3.2,!=0.3.20,!=0.3.21,!=0.3.22,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langgraph==0.2.64) (3.0.0)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.125->langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.15,!=0.3.16,!=0.3.17,!=0.3.18,!=0.3.19,!=0.3.2,!=0.3.20,!=0.3.21,!=0.3.22,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langgraph==0.2.64) (2.32.3)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.125->langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.15,!=0.3.16,!=0.3.17,!=0.3.18,!=0.3.19,!=0.3.2,!=0.3.20,!=0.3.21,!=0.3.22,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langgraph==0.2.64) (1.0.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.5.2->langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.15,!=0.3.16,!=0.3.17,!=0.3.18,!=0.3.19,!=0.3.2,!=0.3.20,!=0.3.21,!=0.3.22,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langgraph==0.2.64) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.5.2->langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.15,!=0.3.16,!=0.3.17,!=0.3.18,!=0.3.19,!=0.3.2,!=0.3.20,!=0.3.21,!=0.3.22,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langgraph==0.2.64) (2.27.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langsmith<0.4,>=0.1.125->langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.15,!=0.3.16,!=0.3.17,!=0.3.18,!=0.3.19,!=0.3.2,!=0.3.20,!=0.3.21,!=0.3.22,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langgraph==0.2.64) (3.4.1)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langsmith<0.4,>=0.1.125->langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.15,!=0.3.16,!=0.3.17,!=0.3.18,!=0.3.19,!=0.3.2,!=0.3.20,!=0.3.21,!=0.3.22,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langgraph==0.2.64) (2.3.0)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio->httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph==0.2.64) (1.3.1)\n", "Downloading langgraph-0.2.64-py3-none-any.whl (142 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m142.6/142.6 kB\u001b[0m \u001b[31m4.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langgraph_checkpoint-2.0.10-py3-none-any.whl (37 kB)\n", "Downloading langgraph_sdk-0.1.51-py3-none-any.whl (44 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m44.7/44.7 kB\u001b[0m \u001b[31m3.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: langgraph-sdk, langgraph-checkpoint, langgraph\n", "Successfully installed langgraph-0.2.64 langgraph-checkpoint-2.0.10 langgraph-sdk-0.1.51\n"]}], "source": ["!pip install langchain==0.3.14\n", "!pip install langchain-openai==0.3.0\n", "!pip install langchain-community==0.3.14\n", "!pip install langgraph==0.2.64\n", "!pip install rich"]}, {"cell_type": "markdown", "id": "H9c37cLnSrbg", "metadata": {"id": "H9c37cLnSrbg"}, "source": ["## Enter Open AI API Key"]}, {"cell_type": "code", "execution_count": null, "id": "cv3JzCEx_PAd", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cv3JzCEx_PAd", "outputId": "fb2a7058-9c1a-4ae2-ceda-5b12d193b2f5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter Open AI API Key: ··········\n"]}], "source": ["from getpass import getpass\n", "\n", "OPENAI_KEY = getpass('Enter Open AI API Key: ')"]}, {"cell_type": "markdown", "id": "ucWRRI3QztL2", "metadata": {"id": "ucWRRI3QztL2"}, "source": ["## Enter Tavily Search API Key\n", "\n", "Get a free API key from [here](https://tavily.com/#api)"]}, {"cell_type": "code", "execution_count": null, "id": "mK-1WLzOrJdb", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mK-1WLzOrJdb", "outputId": "8b103693-f7d9-45c4-9728-47ae91763132"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter Tavily Search API Key: ··········\n"]}], "source": ["TAVILY_API_KEY = getpass('Enter Tavily Search API Key: ')"]}, {"cell_type": "markdown", "id": "1T0s0um5Svfa", "metadata": {"id": "1T0s0um5Svfa"}, "source": ["## Setup Environment Variables"]}, {"cell_type": "code", "execution_count": null, "id": "x1YSuHNF_lbh", "metadata": {"id": "x1YSuHNF_lbh"}, "outputs": [], "source": ["import os\n", "\n", "os.environ['OPENAI_API_KEY'] = OPENAI_KEY\n", "os.environ['TAVILY_API_KEY'] = TAVILY_API_KEY"]}, {"cell_type": "markdown", "id": "1Anj_VT7b4Rt", "metadata": {"id": "1Anj_VT7b4Rt"}, "source": ["## Define Agent State Schema\n", "\n", "Each specific set of operations (nodes) will have their own schema as defined below. You can customize this further based on your own style of report generation"]}, {"cell_type": "code", "execution_count": null, "id": "o7EnucYkRb6f", "metadata": {"id": "o7EnucYkRb6f"}, "outputs": [], "source": ["from typing_extensions import TypedDict\n", "from pydantic import BaseModel, Field\n", "import operator\n", "from typing import  Annotated, List, Optional, Literal\n", "\n", "class Section(BaseModel):\n", "    name: str = Field(\n", "        description=\"Name for a particular section of the report.\",\n", "    )\n", "    description: str = Field(\n", "        description=\"Brief overview of the main topics and concepts to be covered in this section.\",\n", "    )\n", "    research: bool = Field(\n", "        description=\"Whether to perform web search for this section of the report.\"\n", "    )\n", "    content: str = Field(\n", "        description=\"The content for this section.\"\n", "    )\n", "\n", "class Sections(BaseModel):\n", "    sections: List[Section] = Field(\n", "        description=\"All the Sections of the overall report.\",\n", "    )\n", "\n", "class SearchQuery(BaseModel):\n", "    search_query: str = Field(None, description=\"Query for web search.\")\n", "\n", "class Queries(BaseModel):\n", "    queries: List[SearchQuery] = Field(\n", "        description=\"List of web search queries.\",\n", "    )\n", "\n", "class ReportStateInput(TypedDict):\n", "    topic: str # Report topic\n", "\n", "class ReportStateOutput(TypedDict):\n", "    final_report: str # Final report\n", "\n", "class ReportState(TypedDict):\n", "    topic: str # Report topic\n", "    sections: list[Section] # List of report sections\n", "    completed_sections: Annotated[list, operator.add] # Send() API\n", "    report_sections_from_research: str # String of any completed sections from research to write final sections\n", "    final_report: str # Final report\n", "\n", "class SectionState(TypedDict):\n", "    section: Section # Report section\n", "    search_queries: list[SearchQuery] # List of search queries\n", "    source_str: str # String of formatted source content from web search\n", "    report_sections_from_research: str # String of any completed sections from research to write final sections\n", "    completed_sections: list[Section] # Final key we duplicate in outer state for Send() API\n", "\n", "class SectionOutputState(TypedDict):\n", "    completed_sections: list[Section] # Final key we duplicate in outer state for Send() API\n"]}, {"cell_type": "markdown", "id": "J8gh0PeLnoD8", "metadata": {"id": "J8gh0PeLnoD8"}, "source": ["## Utility Functions\n", "\n", "- __`run_search_queries(...)`__ : This will asynchronously run tavily search queries for specific list of queries and return back the search results. This is async so it is non blocking and can be executed in parallel."]}, {"cell_type": "code", "execution_count": null, "id": "x90h-0URszgf", "metadata": {"id": "x90h-0URszgf"}, "outputs": [], "source": ["from langchain_community.utilities.tavily_search import TavilySearchAPIWrapper\n", "import asyncio\n", "from dataclasses import asdict, dataclass\n", "\n", "\n", "# just to handle objects created from LLM reponses\n", "@dataclass\n", "class SearchQuery:\n", "    search_query: str\n", "\n", "    def to_dict(self) -> Dict[str, Any]:\n", "        return as<PERSON>(self)\n", "\n", "\n", "tavily_search = TavilySearchAPIWrapper()\n", "\n", "\n", "async def run_search_queries(\n", "    search_queries: List[Union[str, SearchQuery]],\n", "    num_results: int = 5,\n", "    include_raw_content: bool = False\n", ") -> List[Dict]:\n", "\n", "    search_tasks = []\n", "\n", "    for query in search_queries:\n", "        # Handle both string and SearchQuery objects\n", "        # Just in case LLM fails to generate queries as:\n", "        # class SearchQuery(BaseModel):\n", "        #     search_query: str\n", "        query_str = query.search_query if isinstance(query, SearchQuery) else str(query) # text query\n", "\n", "        try:\n", "            # get results from tavily asynchronously (in parallel) for each search query\n", "            search_tasks.append(\n", "                tavily_search.raw_results_async(\n", "                    query=query_str,\n", "                    max_results=num_results,\n", "                    search_depth='advanced',\n", "                    include_answer=False,\n", "                    include_raw_content=include_raw_content\n", "                )\n", "            )\n", "        except Exception as e:\n", "            print(f\"Error creating search task for query '{query_str}': {e}\")\n", "            continue\n", "\n", "    # Execute all searches concurrently and await results\n", "    try:\n", "        if not search_tasks:\n", "            return []\n", "        search_docs = await asyncio.gather(*search_tasks, return_exceptions=True)\n", "        # Filter out any exceptions from the results\n", "        valid_results = [\n", "            doc for doc in search_docs\n", "            if not isinstance(doc, Exception)\n", "        ]\n", "        return valid_results\n", "    except Exception as e:\n", "        print(f\"Error during search queries: {e}\")\n", "        return []"]}, {"cell_type": "markdown", "id": "Db-RKn5MCi47", "metadata": {"id": "Db-RKn5MCi47"}, "source": ["- __`format_search_query_results(...)`__ : This will extract the context from tavily search results, make sure content is not duplicated from same urls and format it to show the Source, URL, relevant content (and optionally raw content which can be truncated based on number of tokens)"]}, {"cell_type": "code", "execution_count": null, "id": "E9YSh5pAxW5r", "metadata": {"id": "E9YSh5pAxW5r"}, "outputs": [], "source": ["import tiktoken\n", "from typing import List, Dict, Union, Any\n", "\n", "def format_search_query_results(\n", "    search_response: Union[Dict[str, Any], List[Any]],\n", "    max_tokens: int = 2000,\n", "    include_raw_content: bool = False\n", ") -> str:\n", "    encoding = tiktoken.encoding_for_model(\"gpt-4\")\n", "    sources_list = []\n", "\n", "    # Handle different response formats\n", "    # if search results is a dict\n", "    if isinstance(search_response, dict):\n", "        if 'results' in search_response:\n", "            sources_list.extend(search_response['results'])\n", "        else:\n", "            sources_list.append(search_response)\n", "    # if search results is a list\n", "    elif isinstance(search_response, list):\n", "        for response in search_response:\n", "            if isinstance(response, dict):\n", "                if 'results' in response:\n", "                    sources_list.extend(response['results'])\n", "                else:\n", "                    sources_list.append(response)\n", "            elif isinstance(response, list):\n", "                sources_list.extend(response)\n", "\n", "    if not sources_list:\n", "        return \"No search results found.\"\n", "\n", "    # Deduplicate by URL and keep unique sources (website urls)\n", "    unique_sources = {}\n", "    for source in sources_list:\n", "        if isinstance(source, dict) and 'url' in source:\n", "            if source['url'] not in unique_sources:\n", "                unique_sources[source['url']] = source\n", "\n", "    # Format output\n", "    formatted_text = \"Content from web search:\\n\\n\"\n", "    for i, source in enumerate(unique_sources.values(), 1):\n", "        formatted_text += f\"Source {source.get('title', 'Untitled')}:\\n===\\n\"\n", "        formatted_text += f\"URL: {source['url']}\\n===\\n\"\n", "        formatted_text += f\"Most relevant content from source: {source.get('content', 'No content available')}\\n===\\n\"\n", "\n", "        if include_raw_content:\n", "            # truncate raw webpage content to a certain number of tokens to prevent exceeding LLM max token window\n", "            raw_content = source.get(\"raw_content\", \"\")\n", "            if raw_content:\n", "                tokens = encoding.encode(raw_content)\n", "                truncated_tokens = tokens[:max_tokens]\n", "                truncated_content = encoding.decode(truncated_tokens)\n", "                formatted_text += f\"Raw Content: {truncated_content}\\n\\n\"\n", "\n", "    return formatted_text.strip()"]}, {"cell_type": "markdown", "id": "lM0iTr7iC-17", "metadata": {"id": "lM0iTr7iC-17"}, "source": ["## Test Sample Utility Functions"]}, {"cell_type": "code", "execution_count": null, "id": "60GtL630zXH4", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "60GtL630zXH4", "outputId": "829b617b-c306-4fe5-9cb8-db67c56ab497"}, "outputs": [{"data": {"text/plain": ["[{'query': 'langgraph',\n", "  'follow_up_questions': None,\n", "  'answer': None,\n", "  'images': [],\n", "  'results': [{'title': 'Introduction - GitHub Pages',\n", "    'url': 'https://langchain-ai.github.io/langgraphjs/',\n", "    'content': 'Overview¶. LangGraph is a library for building stateful, multi-actor applications with LLMs, used to create agent and multi-agent workflows. Check out an introductory tutorial here.. LangGraph is inspired by <PERSON><PERSON> and Apache Beam.The public interface draws inspiration from NetworkX.LangGraph is built by LangChain Inc, the creators of LangChain, but can be used without LangChain.',\n", "    'score': 0.93511593,\n", "    'raw_content': \"🦜🕸️LangGraph.js¶\\n⚡ Building language agents as graphs ⚡\\nLooking for the Python version? Click\\nhere ( docs).\\nOverview¶\\nSuppose you're building a customer support assistant. You want your assistant to be able to:\\nLangGraph makes this all easy. First install:\\nThen define your assistant:\\nNow, run the graph:\\n<PERSON>e configured the graph to wait before executing the action. The SqliteSaver persists the state. Resume at any time.\\nThe graph orchestrates everything:\\nWith LangGraph, you can build complex, stateful agents without getting bogged down in manual state and interrupt management. Just define your nodes, edges, and state schema - and let the graph take care of the rest.\\nTutorials¶\\nConsult the Tutorials to learn more about building with LangGraph, including advanced use cases.\\nHow-To Guides¶\\nCheck out the How-To Guides for instructions on handling common tasks with LangGraph.\\nReference¶\\nFor documentation on the core APIs, check out the Reference docs.\\nConceptual Guides¶\\nOnce you've learned the basics, if you want to further understand LangGraph's core abstractions, check out the Conceptual Guides.\\nWhy LangGraph?¶\\nLangGraph is framework agnostic (each node is a regular JavaScript function). It extends the core Runnable API (shared interface for streaming, async, and batch calls) to make it easy to:\\nIf you're building a straightforward DAG, Runnables are a great fit. But for more complex, stateful applications with nonlinear flows, LangGraph is the perfect tool for the job.\"},\n", "   {'title': '️LangGraph - GitHub Pages',\n", "    'url': 'https://langchain-ai.github.io/langgraph/',\n", "    'content': 'Overview¶. LangGraph is a library for building stateful, multi-actor applications with LLMs, used to create agent and multi-agent workflows. Check out an introductory tutorial here.. LangGraph is inspired by <PERSON><PERSON> and Apache Beam.The public interface draws inspiration from NetworkX.LangGraph is built by LangChain Inc, the creators of LangChain, but can be used without LangChain.',\n", "    'score': 0.9325087,\n", "    'raw_content': \"🦜🕸️LangGraph¶\\n\\n\\n\\n\\n⚡ Building language agents as graphs ⚡\\nNote\\nLooking for the JS version? See the JS repo and the JS docs.\\nOverview¶\\nLangGraph is a library for building\\nstateful, multi-actor applications with LLMs, used to create agent and multi-agent\\nworkflows. Check out an introductory tutorial here.\\nLangGraph is inspired by Pregel and Apache Beam. The public interface draws inspiration from NetworkX. LangGraph is built by LangChain Inc, the creators of LangChain, but can be used without LangChain.\\nWhy use LangGraph?¶\\nLangGraph powers production-grade agents, trusted by Linkedin, Uber, Klarna, GitLab, and many more. LangGraph provides fine-grained control over both the flow and state of your agent applications. It implements a central persistence layer, enabling features that are common to most agent architectures:\\nStandardizing these components allows individuals and teams to focus on the behavior\\nof their agent, instead of its supporting infrastructure.\\nThrough LangGraph Platform, LangGraph also provides tooling for\\nthe development, deployment, debugging, and monitoring of your applications.\\nLangGraph integrates seamlessly with\\nLangChain and\\nLangSmith (but does not require them).\\nTo learn more about LangGraph, check out our first LangChain Academy\\ncourse, Introduction to LangGraph, available for free\\nhere.\\nLangGraph Platform¶\\nLangGraph Platform is infrastructure for deploying LangGraph agents. It is a commercial solution for deploying agentic applications to production, built on the open-source LangGraph framework. The LangGraph Platform consists of several components that work together to support the development, deployment, debugging, and monitoring of LangGraph applications: LangGraph Server (APIs), LangGraph SDKs (clients for the APIs), LangGraph CLI (command line tool for building the server), and LangGraph Studio (UI/debugger).\\nSee deployment options here\\n(includes a free tier).\\nHere are some common issues that arise in complex deployments, which LangGraph Platform addresses:\\nInstallation¶\\nExample¶\\nLet's build a tool-calling ReAct-style agent that uses a search tool!\\nOptionally, we can set up LangSmith for best-in-class observability.\\nThe simplest way to create a tool-calling agent in LangGraph is to use create_react_agent:\\nTip\\nLangGraph is a low-level framework that allows you to implement any custom agent\\narchitectures. Click on the low-level implementation below to see how to implement a\\ntool-calling agent from scratch.\\nDocumentation¶\\nContributing¶\\nFor more information on how to contribute, see here.\\n\"},\n", "   {'title': 'LangGraph Tutorial: What Is LangGraph and How to Use It?',\n", "    'url': 'https://www.datacamp.com/tutorial/langgraph-tutorial',\n", "    'content': 'LangGraph is a library within the LangChain ecosystem that simplifies the development of complex, multi-agent large language model (LLM) applications. Learn how to use LangGraph to create stateful, flexible, and scalable systems with nodes, edges, and state management.',\n", "    'score': 0.93161833,\n", "    'raw_content': 'Discover content by tools and technology\\nDiscover content by data science topics\\nLangGraph Tutorial: What Is LangGraph and How to Use It?\\nImagine you\\'re building a complex, multi-agent large language model (LLM) application. It\\'s exciting, but it comes with challenges: managing the state of various agents, coordinating their interactions, and handling errors effectively. This is where LangGraph can help.\\nLangGraph is a library within the LangChain ecosystem designed to tackle these challenges head-on. LangGraph provides a framework for defining, coordinating, and executing multiple LLM agents (or chains) in a structured manner.\\nIt simplifies the development process by enabling the creation of cyclical graphs, which are essential for developing agent runtimes. With LangGraph, we can easily build robust, scalable, and flexible multi-agent systems.\\nIf you want to learn more about the LangChain ecosystem, I recommend this introduction to LangChain.\\nWhat Is LangGraph?\\nLangGraph enables us to create stateful, multi-actor applications utilizing LLMs as easily as possible. It extends the capabilities of LangChain, introducing the ability to create and manage cyclical graphs, which are pivotal for developing sophisticated agent runtimes. The core concepts of LangGraph include: graph structure, state management, and coordination.\\nGraph structure\\nImagine your application as a directed graph. In LangGraph, each node represents an LLM agent, and the edges are the communication channels between these agents. This structure allows for clear and manageable workflows, where each agent performs specific tasks and passes information to other agents as needed.\\nState management\\nOne of LangGraph\\'s standout features is its automatic state management. This feature enables us to track and persist information across multiple interactions. As agents perform their tasks, the state is dynamically updated, ensuring the system maintains context and responds appropriately to new inputs.\\nCoordination\\nLangGraph ensures agents execute in the correct order and that necessary information is exchanged seamlessly. This coordination is vital for complex applications where multiple agents need to work together to achieve a common goal. By managing the flow of data and the sequence of operations, LangGraph allows developers to focus on the high-level logic of their applications rather than the intricacies of agent coordination.\\nWhy LangGraph?\\nAs I mentioned above, LangGraph offers several significant advantages for developers working with complex LLM applications. Here are some of the real-world benefits LangGraph offers.\\nSimplified development\\nLangGraph abstracts away the complexities associated with state management and agent coordination. This means developers can define their workflows and logic without worrying about the underlying mechanisms that ensure data consistency and proper execution order. This simplification accelerates the development process and reduces the likelihood of errors. It’s a game-changer!\\nFlexibility\\nWith LangGraph, developers have the flexibility to define their own agent logic and communication protocols. This allows for highly customized applications tailored to specific use cases. Whether you need a chatbot that can handle various types of user requests or a multi-agent system that performs complex tasks, LangGraph provides the tools to build exactly what you need. It’s all about giving you the power to create.\\nScalability\\nLangGraph is built to support the execution of large-scale multi-agent applications. Its robust architecture can handle a high volume of interactions and complex workflows, enabling the development of scalable systems that can grow with your needs. This makes it suitable for enterprise-level applications and scenarios where performance and reliability are critical.\\nFault tolerance\\nReliability is a core consideration in the design of LangGraph. The library includes mechanisms for gracefully handling errors, ensuring that your application can continue to operate even when individual agents encounter issues. This fault tolerance is essential for maintaining the stability and robustness of complex multi-agent systems. Peace of mind is just a feature away.\\nGetting Started With LangGraph\\nLet’s see how we can set up LangGraph and what the basic concepts are.\\nInstallation\\nTo install LangGraph, you can use pip:\\nBasic Concepts\\nNodes: Nodes represent units of work within your LangGraph. They are typically Python functions that perform a specific task, such as:\\nIn LangGraph, you can add nodes using the graph.add_node(name, value) syntax.\\nEdges: Edges are communication channels between nodes. They define the flow of information and the order of execution. You can add edges using the graph.add_edge(node1, node2) syntax.\\nState: The state is a central object updated over time by the nodes in the graph. It manages the internal state of your application and can be overridden or added to, depending on the application\\'s requirements. This state can hold things such as:\\nBuilding a Simple LangGraph Application\\nHere’s a step-by-step example of creating a basic chatbot application using LangGraph.\\nStep 1: Define the StateGraph\\nDefine a StateGraph object to structure the chatbot as a state machine. The State is a class object defined with a single key messages of type List and uses the add_messages() function to append new messages rather than overwrite them.\\nStep 2: Initialize an LLM and add it as a Chatbot node\\nHere, we initialize the AzureChatOpenAI model and create a simple chatbot function that takes in the state messages as input and generates a message response (which is subsequently appended to the state).\\nThis chatbot function is added as a node named “chatbot” to the graph.\\nStep 3: Set edges\\nSince we are building a simple chatbot, we set the chatbot node as both the entry and finish points of the graph to indicate where to start and end the process.\\nStep 4: Compile and Visualize the Graph\\nCompile the graph to create a CompiledGraph object, and optionally, we can visualize the graph structure using the code below:\\nStep 5: Run the chatbot\\nFinally, we implement a loop to continuously prompt the user for input, process it through the graph, and print the assistant\\'s response. The loop exits when the user types \"quit\", \"exit\", or \"q\".\\nAdvanced LangGraph Features\\nNow that we covered the basics, let’s take a look at some advanced features.\\nCustom node types\\nLangGraph allows you to create custom node types to implement complex agent logic. This provides flexibility and control over your application\\'s behavior.\\nHere, we define a class MyCustomNode that encapsulates custom logic and interacts with the LLM. This provides a more structured and maintainable way to implement complex node behaviors.\\nEdge types\\nLangGraph supports different edge types to handle various communication patterns between nodes. One useful type is the conditional edge, which allows for decision-making based on a node\\'s output.\\nTo create a conditional edge, you need three components:\\nHere\\'s an example in pseudocode:\\nHere, after the “model” node is called, we can either exit the graph (”end”) and return to the user, or we can continue (”continue”) and call a tool—depending on what the user decides!\\nState management\\nLangGraph offers powerful state management techniques, which include using external databases like SQLite, PostgreSQL, and MongoDB, or cloud storage solutions like Amazon S3, Google Cloud Storage, and Azure Blob Storage to store and retrieve your agent\\'s state, enabling reliability and scalability.\\nHere\\'s an example of using a SQLite database for state management:\\nError handling\\nLangGraph also provides mechanisms for error handling:\\nReal-World Applications of LangGraph\\nLangGraph can be used to build a wide range of applications.\\nChatbots\\nLangGraph is ideal for developing sophisticated chatbots that can handle a wide array of user requests. By leveraging multiple LLM agents, these chatbots can process natural language queries, provide accurate responses, and seamlessly switch between different conversation topics. The ability to manage state and coordinate interactions ensures that the chatbot maintains context and delivers a coherent user experience.\\nAutonomous agents\\nFor applications requiring autonomous decision-making, LangGraph enables the creation of agents that can perform tasks independently based on user inputs and predefined logic.\\nThese agents can execute complex workflows, interact with other systems, and adapt to new information dynamically. LangGraph\\'s structured framework ensures that each agent operates efficiently and effectively, making it suitable for tasks like automated customer support, data processing, and system monitoring.\\nMulti-Agent systems\\nLangGraph excels in building applications where multiple agents collaborate to achieve a common goal. For example, different agents can manage inventory, process orders, and coordinate deliveries in a supply chain management system. LangGraph\\'s coordination capabilities ensure that each agent communicates effectively, sharing information and making decisions in a synchronized manner. This leads to more efficient operations and better overall system performance.\\nWorkflow automation tools\\nWith LangGraph, automating business processes and workflows becomes straightforward. Intelligent agents can be designed to handle tasks such as document processing, approval workflows, and data analysis. By defining clear workflows and leveraging LangGraph\\'s state management, these tools can execute complex sequences of actions without human intervention, reducing errors and increasing productivity.\\nRecommendation systems\\nPersonalized recommendation systems can greatly benefit from LangGraph\\'s capabilities. By employing multiple agents to analyze user behavior, preferences, and contextual data, these systems can deliver tailored suggestions for products, content, or services. LangGraph\\'s flexibility allows for integrating various data sources and algorithms, enhancing the accuracy and relevance of recommendations.\\nPersonalized learning environments\\nIn educational platforms, LangGraph can be used to create adaptive learning environments that cater to individual learning styles and needs. Multiple agents can assess a student\\'s progress, provide customized exercises, and offer real-time feedback. The stateful nature of LangGraph ensures that the system retains information about each learner\\'s performance and preferences, enabling a more personalized and effective educational experience.\\nConclusion\\nLangGraph significantly simplifies the development of complex LLM applications by providing a structured framework for managing state and coordinating agent interactions.\\nPotential developments for LangGraph include integration with other LangChain components, support for new LLM models, and the introduction of more advanced agent runtimes from academia.\\nIf you want to learn more about developing applications within the LangChain ecosystem, I recommend this course on developing LLM applications with LangChain.\\nRyan is a lead data scientist specialising in building AI applications using LLMs. He is a PhD candidate in Natural Language Processing and Knowledge Graphs at Imperial College London, where he also completed his Master’s degree in Computer Science. Outside of data science, he writes a weekly Substack newsletter,\\xa0The Limitless Playbook, where he shares one actionable idea from the world\\'s top thinkers and occasionally writes about core AI concepts.\\nLangChain vs LlamaIndex: A Detailed Comparison\\nIntroduction to LangChain for Data Engineering & Data Applications\\nHow to Build LLM Applications with LangChain Tutorial\\nBuilding Context-Aware Chatbots: Leveraging LangChain Framework for ChatGPT\\nBuilding AI Applications with LangChain and GPT\\nIntroduction to Large Language Models with GPT & LangChain\\nLearn AI with these courses!\\ncourse\\nDeveloping LLM Applications with LangChain\\ncourse\\nDeveloping LLM Applications with LangChain\\ntrack\\nAI Business Fundamentals\\nblog\\nLangChain vs LlamaIndex: A Detailed Comparison\\nIva Vrtaric\\n13 min\\ntutorial\\nIntroduction to LangChain for Data Engineering & Data Applications\\nRichie Cotton\\n11 min\\ntutorial\\nHow to Build LLM Applications with LangChain Tutorial\\nMoez Ali\\n12 min\\ntutorial\\nBuilding Context-Aware Chatbots: Leveraging LangChain Framework for ChatGPT\\nAndrea Valenzuela\\n15 min\\ncode-along\\nBuilding AI Applications with LangChain and GPT\\nEmmanuel Pire\\ncode-along\\nIntroduction to Large Language Models with GPT & LangChain\\nRichie Cotton\\nGrow your data skills with DataCamp for Mobile\\nMake progress on the go with our mobile courses and daily 5-minute coding challenges.\\n© 2024 DataCamp, Inc. All Rights Reserved.'},\n", "   {'title': 'LangG<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>',\n", "    'url': 'https://www.langchain.com/langgraph',\n", "    'content': 'LangGraph is a framework for building and scaling agentic applications with LangChain Platform. Learn how to design agents that handle complex tasks, collaborate with humans, and stream intermediate steps with LangGraph.',\n", "    'score': 0.92176014,\n", "    'raw_content': 'Balance agent control with agency\\nGain precision and control with LangGraph to build agents that reliably handle complex tasks.\\nLangGraph Cloud is now in Beta\\nDeploy LangGraph agents at scale with LangGraph Cloud (available for Python). Get 1-click deployment, scalable servers and task queues, and integrated monitoring to streamline custom workflows.\\nControllable cognitive architecure for any task\\nLangGraph\\'s flexible API supports diverse control flows – single agent, multi-agent, hierarchical, sequential – and robustly handles realistic, complex scenarios. Ensure reliability with easy-to-add moderation and quality loops that prevent agents from veering off course.\\nDesigned for human-agent collaboration\\nWith built-in statefulness, LangGraph agents seamlessly collaborate with humans by writing drafts for review and awaiting approval before acting. Easily inspect the agent’s actions and \"time-travel\" to take a different action at a specific time.\\nFirst class streaming support for better UX responsiveness\\nBridge user expectations and agent capabilities with native token-by-token streaming and streaming of intermediate steps, delivering dynamic and interactive user experiences.\\nDeploy agents at scale, monitor carefully, iterate boldly\\nWith LangGraph Cloud, quickly deploy and scale your application, with infrastructure purpose-built for agents. Sign up for the beta.\\nFault-tolerant scalability\\nOptimized for real-world interactions\\nIntegrated developer experience\\nTrusted by companies taking agency in AI\\xa0innovation:\\nLangGraph helps teams of all sizes, across all industries, from ambitious\\nstartups to established enterprises.\\n“LangChain is streets ahead with what they\\'ve put forward with LangGraph. LangGraph sets the foundation for how we can build and scale AI workloads — from conversational agents, complex task automation, to custom LLM-backed experiences that \\'just work\\'. The next chapter in building complex production-ready features with LLMs is agentic, and with LangGraph and LangSmith, LangChain delivers an out-of-the-box solution to iterate quickly, debug immediately, and scale effortlessly.”\\n“LangGraph has been instrumental for our AI development. Its robust framework for building stateful, multi-actor applications with LLMs has transformed how we evaluate and optimize the performance of our AI guest-facing solutions. LangGraph enables granular control over the agent\\'s thought process, which has empowered us to make data-driven and deliberate decisions to meet the diverse needs of our guests.”\\n“It\\'s easy to build the prototype of a coding agent, but deceptively hard to improve its reliability. Replit wants to give a coding agent to millions of users — reliability is our top priority, and will remain so for a long time. LangGraph is giving us the control and ergonomics we need to build and ship powerful coding agents.”\\n“As Ally advances its exploration of Generative AI,\\n“As Ally advances its exploration of Generative AI, our tech labs is excited by LangGraph, the new library from LangChain, which is central to our experiments with multi-actor agentic workflows. We are committed to deepening our partnership with LangChain.”\\n“As Ally advances its exploration of Generative AI,\\nLangGraph FAQs\\nNo. LangGraph is an orchestration framework for complex agentic systems and is more low-level and controllable than LangChain agents.\\xa0On the other hand, LangChain provides a standard interface to interact with models and other components, useful for straight-forward chains and retrieval flows.\\nOther agentic frameworks can work for simple, generic tasks but fall short for complex tasks bespoke to a company’s needs. LangGraph provides a more expressive framework to handle companies’ unique tasks without restricting users to a single black-box cognitive architecture.\\nLangGraph will not add any overhead to your code and is specifically designed with streaming workflows in mind.\\nYes. LangGraph is an MIT-licensed open-source library and is free to use.\\nNo. LangGraph Cloud is proprietary software that will eventually be a paid service for certain tiers of usage. We will always give ample notice before charging for a service and reward our early adopters with preferential pricing.\\nFor now, LangGraph Cloud is in closed beta. You can join this waitlist to try it out for free, and we’ll notify you with deployment instructions once you’re off the waitlist. Check out the docs.\\nLangGraph is a stateful, orchestration framework that brings added control to agent workflows. LangGraph Cloud is a service for deploying and scaling LangGraph applications, with a built-in Studio for prototyping, debugging, and sharing LangGraph applications.\\nReady to start shipping\\nreliable GenAI apps faster?\\nGet started with LangChain, LangSmith, and LangGraph to enhance your LLM\\xa0app development, from prototype to production.'},\n", "   {'title': 'langgraph · PyPI',\n", "    'url': 'https://pypi.org/project/langgraph/',\n", "    'content': \"Project details\\nProject links\\nStatistics\\nView statistics for this project via Libraries.io, or by using our public dataset on Google BigQuery\\nMeta\\nLicense: Other/Proprietary License (LangGraph License)\\nRequires: Python >=3.9.0, <4.0\\nMaintainers\\nClassifiers\\nRelease history\\nRelease notifications |\\nRSS feed\\n0.0.24\\nFeb 8, 2024\\n0.0.23\\nFeb 4, 2024\\n0.0.22\\nFeb 4, 2024\\n0.0.21\\nJan 31, 2024\\n0.0.20\\nJan 27, 2024\\n0.0.19\\nJan 23, 2024\\n0.0.18\\nJan 23, 2024\\n0.0.17\\nJan 23, 2024\\n0.0.16\\nJan 21, 2024\\n0.0.15\\nJan 19, 2024\\n0.0.14\\nJan 18, 2024\\n0.0.13\\nJan 17, 2024\\n0.0.12\\nJan 17, 2024\\n0.0.11\\nJan 16, 2024\\n0.0.10\\nJan 9, 2024\\n0.0.9\\nJan 8, 2024\\n0.0.8\\nyanked\\nJan 8, 2024\\nDownload files\\nDownload the file for your platform. langgraph 0.0.24\\npip install langgraph\\nCopy PIP instructions\\nReleased:\\nFeb 8, 2024\\nlanggraph\\nNavigation\\nProject links\\nStatistics\\nView statistics for this project via Libraries.io, or by using our public dataset on Google BigQuery\\nMeta\\nLicense: Other/Proprietary License (LangGraph License)\\nRequires: Python >=3.9.0, <4.0\\nMaintainers\\nClassifiers\\nProject description\\n🦜🕸️LangGraph\\n⚡ Building language agents as graphs ⚡\\nOverview\\nLangGraph is a library for building stateful, multi-actor applications with LLMs, built on top of (and intended to be used with) LangChain.\\n Source Distribution\\nUploaded\\nFeb 8, 2024\\nsource\\nBuilt Distribution\\nUploaded\\nFeb 8, 2024\\npy3\\nHashes for langgraph-0.0.24.tar.gz\\nHashes for langgraph-0.0.24-py3-none-any.whl\\nHelp\\nAbout PyPI\\n It only has one argument:\\nNote: This does not need to be called if at any point you previously created an edge (conditional or normal) to END\\nGraph\\nThis has the same interface as StateGraph with the exception that it doesn't update a state object over time, and rather relies on passing around the full state from each step.\\n If the agent said that it was finished, then it should finish\\nNormal Edge: after the tools are invoked, it should always go back to the agent to decide what to do next\\nLet's define the nodes, as well as a function to decide how what conditional edge to take.\\n\",\n", "    'score': 0.8668671,\n", "    'raw_content': 'langgraph 0.0.24\\npip install langgraph\\nCopy PIP instructions\\nReleased:\\nFeb 8, 2024\\nlanggraph\\nNavigation\\nProject links\\nStatistics\\nView statistics for this project via Libraries.io, or by using our public dataset on Google BigQuery\\nMeta\\nLicense: Other/Proprietary License (LangGraph License)\\nRequires: Python >=3.9.0, <4.0\\nMaintainers\\nClassifiers\\nProject description\\n🦜🕸️LangGraph\\n⚡ Building language agents as graphs ⚡\\nOverview\\nLangGraph is a library for building stateful, multi-actor applications with LLMs, built on top of (and intended to be used with) LangChain.\\nIt extends the LangChain Expression Language with the ability to coordinate multiple chains (or actors) across multiple steps of computation in a cyclic manner.\\nIt is inspired by Pregel and Apache Beam.\\nThe current interface exposed is one inspired by NetworkX.\\nThe main use is for adding cycles to your LLM application.\\nCrucially, this is NOT a DAG framework.\\nIf you want to build a DAG, you should just use LangChain Expression Language.\\nCycles are important for agent-like behaviors, where you call an LLM in a loop, asking it what action to take next.\\nInstallation\\nQuick Start\\nHere we will go over an example of creating a simple agent that uses chat models and function calling.\\nThis agent will represent all its state as a list of messages.\\nWe will need to install some LangChain packages, as well as Tavily to use as an example tool.\\nWe also need to export some environment variables for OpenAI and Tavily API access.\\nOptionally, we can set up LangSmith for best-in-class observability.\\nSet up the tools\\nWe will first define the tools we want to use.\\nFor this simple example, we will use a built-in search tool via Tavily.\\nHowever, it is really easy to create your own tools - see documentation here on how to do that.\\nWe can now wrap these tools in a simple LangGraph ToolExecutor.\\nThis is a simple class that receives ToolInvocation objects, calls that tool, and returns the output.\\nToolInvocation is any class with tool and tool_input attributes.\\nSet up the model\\nNow we need to load the chat model we want to use.\\nImportantly, this should satisfy two criteria:\\nNote: these model requirements are not requirements for using LangGraph - they are just requirements for this one example.\\nAfter we\\'ve done this, we should make sure the model knows that it has these tools available to call.\\nWe can do this by converting the LangChain tools into the format for OpenAI function calling, and then bind them to the model class.\\nDefine the agent state\\nThe main type of graph in langgraph is the StatefulGraph.\\nThis graph is parameterized by a state object that it passes around to each node.\\nEach node then returns operations to update that state.\\nThese operations can either SET specific attributes on the state (e.g. overwrite the existing values) or ADD to the existing attribute.\\nWhether to set or add is denoted by annotating the state object you construct the graph with.\\nFor this example, the state we will track will just be a list of messages.\\nWe want each node to just add messages to that list.\\nTherefore, we will use a TypedDict with one key (messages) and annotate it so that the messages attribute is always added to.\\nDefine the nodes\\nWe now need to define a few different nodes in our graph.\\nIn langgraph, a node can be either a function or a runnable.\\nThere are two main nodes we need for this:\\nWe will also need to define some edges.\\nSome of these edges may be conditional.\\nThe reason they are conditional is that based on the output of a node, one of several paths may be taken.\\nThe path that is taken is not known until that node is run (the LLM decides).\\nConditional Edge: after the agent is called, we should either:\\na. If the agent said to take an action, then the function to invoke tools should be called\\nb. If the agent said that it was finished, then it should finish\\nNormal Edge: after the tools are invoked, it should always go back to the agent to decide what to do next\\nLet\\'s define the nodes, as well as a function to decide how what conditional edge to take.\\nDefine the graph\\nWe can now put it all together and define the graph!\\nUse it!\\nWe can now use it!\\nThis now exposes the same interface as all other LangChain runnables.\\nThis runnable accepts a list of messages.\\nThis may take a little bit - it\\'s making a few calls behind the scenes.\\nIn order to start seeing some intermediate results as they happen, we can use streaming - see below for more information on that.\\nStreaming\\nLangGraph has support for several different types of streaming.\\nStreaming Node Output\\nOne of the benefits of using LangGraph is that it is easy to stream output as it\\'s produced by each node.\\nStreaming LLM Tokens\\nYou can also access the LLM tokens as they are produced by each node.\\nIn this case only the \"agent\" node produces LLM tokens.\\nIn order for this to work properly, you must be using an LLM that supports streaming as well as have set it when constructing the LLM (e.g. ChatOpenAI(model=\"gpt-3.5-turbo-1106\", streaming=True))\\nWhen to Use\\nWhen should you use this versus LangChain Expression Language?\\nIf you need cycles.\\nLangchain Expression Language allows you to easily define chains (DAGs) but does not have a good mechanism for adding in cycles.\\nlanggraph adds that syntax.\\nExamples\\nChatAgentExecutor: with function calling\\nThis agent executor takes a list of messages as input and outputs a list of messages.\\nAll agent state is represented as a list of messages.\\nThis specifically uses OpenAI function calling.\\nThis is recommended agent executor for newer chat based models that support function calling.\\nModifications\\nWe also have a lot of examples highlighting how to slightly modify the base chat agent executor. These all build off the getting started notebook so it is recommended you start with that first.\\nAgentExecutor\\nThis agent executor uses existing LangChain agents.\\nModifications\\nWe also have a lot of examples highlighting how to slightly modify the base chat agent executor. These all build off the getting started notebook so it is recommended you start with that first.\\nMulti-agent Examples\\nChatbot Evaluation via Simulation\\nIt can often be tough to evaluation chat bots in multi-turn situations. One way to do this is with simulations.\\nAsync\\nIf you are running LangGraph in async workflows, you may want to create the nodes to be async by default.\\nFor a walkthrough on how to do that, see this documentation\\nStreaming Tokens\\nSometimes language models take a while to respond and you may want to stream tokens to end users.\\nFor a guide on how to do this, see this documentation\\nPersistence\\nLangGraph comes with built-in persistence, allowing you to save the state of the graph at point and resume from there.\\nFor a walkthrough on how to do that, see this documentation\\nHuman-in-the-loop\\nLangGraph comes with built-in support for human-in-the-loop workflows. This is useful when you want to have a human review the current state before proceeding to a particular node.\\nFor a walkthrough on how to do that, see this documentation\\nDocumentation\\nThere are only a few new APIs to use.\\nStateGraph\\nThe main entrypoint is StateGraph.\\nThis class is responsible for constructing the graph.\\nIt exposes an interface inspired by NetworkX.\\nThis graph is parameterized by a state object that it passes around to each node.\\nWhen constructing the graph, you need to pass in a schema for a state.\\nEach node then returns operations to update that state.\\nThese operations can either SET specific attributes on the state (e.g. overwrite the existing values) or ADD to the existing attribute.\\nWhether to set or add is denoted by annotating the state object you construct the graph with.\\nThe recommended way to specify the schema is with a typed dictionary: from typing import TypedDict\\nYou can then annotate the different attributes using from typing imoport Annotated.\\nCurrently, the only supported annotation is import operator; operator.add.\\nThis annotation will make it so that any node that returns this attribute ADDS that new result to the existing value.\\nLet\\'s take a look at an example:\\nWe can then use this like:\\nThis method adds a node to the graph.\\nIt takes two arguments:\\nCreates an edge from one node to the next.\\nThis means that output of the first node will be passed to the next node.\\nIt takes two arguments.\\nThis method adds conditional edges.\\nWhat this means is that only one of the downstream edges will be taken, and which one that is depends on the results of the start node.\\nThis takes three arguments:\\nThe entrypoint to the graph.\\nThis is the node that is first called.\\nIt only takes one argument:\\nThis is the exit point of the graph.\\nWhen this node is called, the results will be the final result from the graph.\\nIt only has one argument:\\nNote: This does not need to be called if at any point you previously created an edge (conditional or normal) to END\\nGraph\\nThis has the same interface as StateGraph with the exception that it doesn\\'t update a state object over time, and rather relies on passing around the full state from each step.\\nThis means that whatever is returned from one node is the input to the next as is.\\nEND\\nThis is a special node representing the end of the graph.\\nThis means that anything passed to this node will be the final output of the graph.\\nIt can be used in two places:\\nPrebuilt Examples\\nThere are also a few methods we\\'ve added to make it easy to use common, prebuilt graphs and components.\\nToolExecutor\\nThis is a simple helper class to help with calling tools.\\nIt is parameterized by a list of tools:\\nIt then exposes a runnable interface.\\nIt can be used to call tools: you can pass in an AgentAction and it will look up the relevant tool and call it with the appropriate input.\\nchat_agent_executor.create_function_calling_executor\\nThis is a helper function for creating a graph that works with a chat model that utilizes function calling.\\nCan be created by passing in a model and a list of tools.\\nThe model must be one that supports OpenAI function calling.\\ncreate_agent_executor\\nThis is a helper function for creating a graph that works with LangChain Agents.\\nCan be created by passing in an agent and a list of tools.\\nProject details\\nProject links\\nStatistics\\nView statistics for this project via Libraries.io, or by using our public dataset on Google BigQuery\\nMeta\\nLicense: Other/Proprietary License (LangGraph License)\\nRequires: Python >=3.9.0, <4.0\\nMaintainers\\nClassifiers\\nRelease history\\nRelease notifications |\\nRSS feed\\n0.0.24\\nFeb 8, 2024\\n0.0.23\\nFeb 4, 2024\\n0.0.22\\nFeb 4, 2024\\n0.0.21\\nJan 31, 2024\\n0.0.20\\nJan 27, 2024\\n0.0.19\\nJan 23, 2024\\n0.0.18\\nJan 23, 2024\\n0.0.17\\nJan 23, 2024\\n0.0.16\\nJan 21, 2024\\n0.0.15\\nJan 19, 2024\\n0.0.14\\nJan 18, 2024\\n0.0.13\\nJan 17, 2024\\n0.0.12\\nJan 17, 2024\\n0.0.11\\nJan 16, 2024\\n0.0.10\\nJan 9, 2024\\n0.0.9\\nJan 8, 2024\\n0.0.8\\nyanked\\nJan 8, 2024\\nDownload files\\nDownload the file for your platform. If you\\'re not sure which to choose, learn more about installing packages.\\nSource Distribution\\nUploaded\\nFeb 8, 2024\\nsource\\nBuilt Distribution\\nUploaded\\nFeb 8, 2024\\npy3\\nHashes for langgraph-0.0.24.tar.gz\\nHashes for langgraph-0.0.24-py3-none-any.whl\\nHelp\\nAbout PyPI\\nContributing to PyPI\\nUsing PyPI\\nStatus:\\nall systems operational\\nDeveloped and maintained by the Python community, for the Python community.\\nDonate today!\\n\"PyPI\", \"Python Package Index\", and the blocks logos are registered trademarks of the Python Software Foundation.\\n© 2024 Python Software Foundation\\nSite map\\nSupported by'}],\n", "  'response_time': 1.86}]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["docs = await run_search_queries(['langgraph'], include_raw_content=True)\n", "docs"]}, {"cell_type": "code", "execution_count": null, "id": "zFNI1k9bKmBP", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zFNI1k9bKmBP", "outputId": "8b59c514-2c61-436e-bd71-54d5d06ae8fc"}, "outputs": [{"data": {"text/plain": ["{'query': 'langgraph',\n", " 'follow_up_questions': None,\n", " 'answer': None,\n", " 'images': [],\n", " 'results': [{'title': 'Introduction - GitHub Pages',\n", "   'url': 'https://langchain-ai.github.io/langgraphjs/',\n", "   'content': 'Overview¶. LangGraph is a library for building stateful, multi-actor applications with LLMs, used to create agent and multi-agent workflows. Check out an introductory tutorial here.. LangGraph is inspired by <PERSON><PERSON> and Apache Beam.The public interface draws inspiration from NetworkX.LangGraph is built by LangChain Inc, the creators of LangChain, but can be used without LangChain.',\n", "   'score': 0.93511593,\n", "   'raw_content': \"🦜🕸️LangGraph.js¶\\n⚡ Building language agents as graphs ⚡\\nLooking for the Python version? Click\\nhere ( docs).\\nOverview¶\\nSuppose you're building a customer support assistant. You want your assistant to be able to:\\nLangGraph makes this all easy. First install:\\nThen define your assistant:\\nNow, run the graph:\\n<PERSON>e configured the graph to wait before executing the action. The SqliteSaver persists the state. Resume at any time.\\nThe graph orchestrates everything:\\nWith LangGraph, you can build complex, stateful agents without getting bogged down in manual state and interrupt management. Just define your nodes, edges, and state schema - and let the graph take care of the rest.\\nTutorials¶\\nConsult the Tutorials to learn more about building with LangGraph, including advanced use cases.\\nHow-To Guides¶\\nCheck out the How-To Guides for instructions on handling common tasks with LangGraph.\\nReference¶\\nFor documentation on the core APIs, check out the Reference docs.\\nConceptual Guides¶\\nOnce you've learned the basics, if you want to further understand LangGraph's core abstractions, check out the Conceptual Guides.\\nWhy LangGraph?¶\\nLangGraph is framework agnostic (each node is a regular JavaScript function). It extends the core Runnable API (shared interface for streaming, async, and batch calls) to make it easy to:\\nIf you're building a straightforward DAG, Runnables are a great fit. But for more complex, stateful applications with nonlinear flows, LangGraph is the perfect tool for the job.\"},\n", "  {'title': '️LangGraph - GitHub Pages',\n", "   'url': 'https://langchain-ai.github.io/langgraph/',\n", "   'content': 'Overview¶. LangGraph is a library for building stateful, multi-actor applications with LLMs, used to create agent and multi-agent workflows. Check out an introductory tutorial here.. LangGraph is inspired by <PERSON><PERSON> and Apache Beam.The public interface draws inspiration from NetworkX.LangGraph is built by LangChain Inc, the creators of LangChain, but can be used without LangChain.',\n", "   'score': 0.9325087,\n", "   'raw_content': \"🦜🕸️LangGraph¶\\n\\n\\n\\n\\n⚡ Building language agents as graphs ⚡\\nNote\\nLooking for the JS version? See the JS repo and the JS docs.\\nOverview¶\\nLangGraph is a library for building\\nstateful, multi-actor applications with LLMs, used to create agent and multi-agent\\nworkflows. Check out an introductory tutorial here.\\nLangGraph is inspired by Pregel and Apache Beam. The public interface draws inspiration from NetworkX. LangGraph is built by LangChain Inc, the creators of LangChain, but can be used without LangChain.\\nWhy use LangGraph?¶\\nLangGraph powers production-grade agents, trusted by Linkedin, Uber, Klarna, GitLab, and many more. LangGraph provides fine-grained control over both the flow and state of your agent applications. It implements a central persistence layer, enabling features that are common to most agent architectures:\\nStandardizing these components allows individuals and teams to focus on the behavior\\nof their agent, instead of its supporting infrastructure.\\nThrough LangGraph Platform, LangGraph also provides tooling for\\nthe development, deployment, debugging, and monitoring of your applications.\\nLangGraph integrates seamlessly with\\nLangChain and\\nLangSmith (but does not require them).\\nTo learn more about LangGraph, check out our first LangChain Academy\\ncourse, Introduction to LangGraph, available for free\\nhere.\\nLangGraph Platform¶\\nLangGraph Platform is infrastructure for deploying LangGraph agents. It is a commercial solution for deploying agentic applications to production, built on the open-source LangGraph framework. The LangGraph Platform consists of several components that work together to support the development, deployment, debugging, and monitoring of LangGraph applications: LangGraph Server (APIs), LangGraph SDKs (clients for the APIs), LangGraph CLI (command line tool for building the server), and LangGraph Studio (UI/debugger).\\nSee deployment options here\\n(includes a free tier).\\nHere are some common issues that arise in complex deployments, which LangGraph Platform addresses:\\nInstallation¶\\nExample¶\\nLet's build a tool-calling ReAct-style agent that uses a search tool!\\nOptionally, we can set up LangSmith for best-in-class observability.\\nThe simplest way to create a tool-calling agent in LangGraph is to use create_react_agent:\\nTip\\nLangGraph is a low-level framework that allows you to implement any custom agent\\narchitectures. Click on the low-level implementation below to see how to implement a\\ntool-calling agent from scratch.\\nDocumentation¶\\nContributing¶\\nFor more information on how to contribute, see here.\\n\"},\n", "  {'title': 'LangGraph Tutorial: What Is LangGraph and How to Use It?',\n", "   'url': 'https://www.datacamp.com/tutorial/langgraph-tutorial',\n", "   'content': 'LangGraph is a library within the LangChain ecosystem that simplifies the development of complex, multi-agent large language model (LLM) applications. Learn how to use LangGraph to create stateful, flexible, and scalable systems with nodes, edges, and state management.',\n", "   'score': 0.93161833,\n", "   'raw_content': 'Discover content by tools and technology\\nDiscover content by data science topics\\nLangGraph Tutorial: What Is LangGraph and How to Use It?\\nImagine you\\'re building a complex, multi-agent large language model (LLM) application. It\\'s exciting, but it comes with challenges: managing the state of various agents, coordinating their interactions, and handling errors effectively. This is where LangGraph can help.\\nLangGraph is a library within the LangChain ecosystem designed to tackle these challenges head-on. LangGraph provides a framework for defining, coordinating, and executing multiple LLM agents (or chains) in a structured manner.\\nIt simplifies the development process by enabling the creation of cyclical graphs, which are essential for developing agent runtimes. With LangGraph, we can easily build robust, scalable, and flexible multi-agent systems.\\nIf you want to learn more about the LangChain ecosystem, I recommend this introduction to LangChain.\\nWhat Is LangGraph?\\nLangGraph enables us to create stateful, multi-actor applications utilizing LLMs as easily as possible. It extends the capabilities of LangChain, introducing the ability to create and manage cyclical graphs, which are pivotal for developing sophisticated agent runtimes. The core concepts of LangGraph include: graph structure, state management, and coordination.\\nGraph structure\\nImagine your application as a directed graph. In LangGraph, each node represents an LLM agent, and the edges are the communication channels between these agents. This structure allows for clear and manageable workflows, where each agent performs specific tasks and passes information to other agents as needed.\\nState management\\nOne of LangGraph\\'s standout features is its automatic state management. This feature enables us to track and persist information across multiple interactions. As agents perform their tasks, the state is dynamically updated, ensuring the system maintains context and responds appropriately to new inputs.\\nCoordination\\nLangGraph ensures agents execute in the correct order and that necessary information is exchanged seamlessly. This coordination is vital for complex applications where multiple agents need to work together to achieve a common goal. By managing the flow of data and the sequence of operations, LangGraph allows developers to focus on the high-level logic of their applications rather than the intricacies of agent coordination.\\nWhy LangGraph?\\nAs I mentioned above, LangGraph offers several significant advantages for developers working with complex LLM applications. Here are some of the real-world benefits LangGraph offers.\\nSimplified development\\nLangGraph abstracts away the complexities associated with state management and agent coordination. This means developers can define their workflows and logic without worrying about the underlying mechanisms that ensure data consistency and proper execution order. This simplification accelerates the development process and reduces the likelihood of errors. It’s a game-changer!\\nFlexibility\\nWith LangGraph, developers have the flexibility to define their own agent logic and communication protocols. This allows for highly customized applications tailored to specific use cases. Whether you need a chatbot that can handle various types of user requests or a multi-agent system that performs complex tasks, LangGraph provides the tools to build exactly what you need. It’s all about giving you the power to create.\\nScalability\\nLangGraph is built to support the execution of large-scale multi-agent applications. Its robust architecture can handle a high volume of interactions and complex workflows, enabling the development of scalable systems that can grow with your needs. This makes it suitable for enterprise-level applications and scenarios where performance and reliability are critical.\\nFault tolerance\\nReliability is a core consideration in the design of LangGraph. The library includes mechanisms for gracefully handling errors, ensuring that your application can continue to operate even when individual agents encounter issues. This fault tolerance is essential for maintaining the stability and robustness of complex multi-agent systems. Peace of mind is just a feature away.\\nGetting Started With LangGraph\\nLet’s see how we can set up LangGraph and what the basic concepts are.\\nInstallation\\nTo install LangGraph, you can use pip:\\nBasic Concepts\\nNodes: Nodes represent units of work within your LangGraph. They are typically Python functions that perform a specific task, such as:\\nIn LangGraph, you can add nodes using the graph.add_node(name, value) syntax.\\nEdges: Edges are communication channels between nodes. They define the flow of information and the order of execution. You can add edges using the graph.add_edge(node1, node2) syntax.\\nState: The state is a central object updated over time by the nodes in the graph. It manages the internal state of your application and can be overridden or added to, depending on the application\\'s requirements. This state can hold things such as:\\nBuilding a Simple LangGraph Application\\nHere’s a step-by-step example of creating a basic chatbot application using LangGraph.\\nStep 1: Define the StateGraph\\nDefine a StateGraph object to structure the chatbot as a state machine. The State is a class object defined with a single key messages of type List and uses the add_messages() function to append new messages rather than overwrite them.\\nStep 2: Initialize an LLM and add it as a Chatbot node\\nHere, we initialize the AzureChatOpenAI model and create a simple chatbot function that takes in the state messages as input and generates a message response (which is subsequently appended to the state).\\nThis chatbot function is added as a node named “chatbot” to the graph.\\nStep 3: Set edges\\nSince we are building a simple chatbot, we set the chatbot node as both the entry and finish points of the graph to indicate where to start and end the process.\\nStep 4: Compile and Visualize the Graph\\nCompile the graph to create a CompiledGraph object, and optionally, we can visualize the graph structure using the code below:\\nStep 5: Run the chatbot\\nFinally, we implement a loop to continuously prompt the user for input, process it through the graph, and print the assistant\\'s response. The loop exits when the user types \"quit\", \"exit\", or \"q\".\\nAdvanced LangGraph Features\\nNow that we covered the basics, let’s take a look at some advanced features.\\nCustom node types\\nLangGraph allows you to create custom node types to implement complex agent logic. This provides flexibility and control over your application\\'s behavior.\\nHere, we define a class MyCustomNode that encapsulates custom logic and interacts with the LLM. This provides a more structured and maintainable way to implement complex node behaviors.\\nEdge types\\nLangGraph supports different edge types to handle various communication patterns between nodes. One useful type is the conditional edge, which allows for decision-making based on a node\\'s output.\\nTo create a conditional edge, you need three components:\\nHere\\'s an example in pseudocode:\\nHere, after the “model” node is called, we can either exit the graph (”end”) and return to the user, or we can continue (”continue”) and call a tool—depending on what the user decides!\\nState management\\nLangGraph offers powerful state management techniques, which include using external databases like SQLite, PostgreSQL, and MongoDB, or cloud storage solutions like Amazon S3, Google Cloud Storage, and Azure Blob Storage to store and retrieve your agent\\'s state, enabling reliability and scalability.\\nHere\\'s an example of using a SQLite database for state management:\\nError handling\\nLangGraph also provides mechanisms for error handling:\\nReal-World Applications of LangGraph\\nLangGraph can be used to build a wide range of applications.\\nChatbots\\nLangGraph is ideal for developing sophisticated chatbots that can handle a wide array of user requests. By leveraging multiple LLM agents, these chatbots can process natural language queries, provide accurate responses, and seamlessly switch between different conversation topics. The ability to manage state and coordinate interactions ensures that the chatbot maintains context and delivers a coherent user experience.\\nAutonomous agents\\nFor applications requiring autonomous decision-making, LangGraph enables the creation of agents that can perform tasks independently based on user inputs and predefined logic.\\nThese agents can execute complex workflows, interact with other systems, and adapt to new information dynamically. LangGraph\\'s structured framework ensures that each agent operates efficiently and effectively, making it suitable for tasks like automated customer support, data processing, and system monitoring.\\nMulti-Agent systems\\nLangGraph excels in building applications where multiple agents collaborate to achieve a common goal. For example, different agents can manage inventory, process orders, and coordinate deliveries in a supply chain management system. LangGraph\\'s coordination capabilities ensure that each agent communicates effectively, sharing information and making decisions in a synchronized manner. This leads to more efficient operations and better overall system performance.\\nWorkflow automation tools\\nWith LangGraph, automating business processes and workflows becomes straightforward. Intelligent agents can be designed to handle tasks such as document processing, approval workflows, and data analysis. By defining clear workflows and leveraging LangGraph\\'s state management, these tools can execute complex sequences of actions without human intervention, reducing errors and increasing productivity.\\nRecommendation systems\\nPersonalized recommendation systems can greatly benefit from LangGraph\\'s capabilities. By employing multiple agents to analyze user behavior, preferences, and contextual data, these systems can deliver tailored suggestions for products, content, or services. LangGraph\\'s flexibility allows for integrating various data sources and algorithms, enhancing the accuracy and relevance of recommendations.\\nPersonalized learning environments\\nIn educational platforms, LangGraph can be used to create adaptive learning environments that cater to individual learning styles and needs. Multiple agents can assess a student\\'s progress, provide customized exercises, and offer real-time feedback. The stateful nature of LangGraph ensures that the system retains information about each learner\\'s performance and preferences, enabling a more personalized and effective educational experience.\\nConclusion\\nLangGraph significantly simplifies the development of complex LLM applications by providing a structured framework for managing state and coordinating agent interactions.\\nPotential developments for LangGraph include integration with other LangChain components, support for new LLM models, and the introduction of more advanced agent runtimes from academia.\\nIf you want to learn more about developing applications within the LangChain ecosystem, I recommend this course on developing LLM applications with LangChain.\\nRyan is a lead data scientist specialising in building AI applications using LLMs. He is a PhD candidate in Natural Language Processing and Knowledge Graphs at Imperial College London, where he also completed his Master’s degree in Computer Science. Outside of data science, he writes a weekly Substack newsletter,\\xa0The Limitless Playbook, where he shares one actionable idea from the world\\'s top thinkers and occasionally writes about core AI concepts.\\nLangChain vs LlamaIndex: A Detailed Comparison\\nIntroduction to LangChain for Data Engineering & Data Applications\\nHow to Build LLM Applications with LangChain Tutorial\\nBuilding Context-Aware Chatbots: Leveraging LangChain Framework for ChatGPT\\nBuilding AI Applications with LangChain and GPT\\nIntroduction to Large Language Models with GPT & LangChain\\nLearn AI with these courses!\\ncourse\\nDeveloping LLM Applications with LangChain\\ncourse\\nDeveloping LLM Applications with LangChain\\ntrack\\nAI Business Fundamentals\\nblog\\nLangChain vs LlamaIndex: A Detailed Comparison\\nIva Vrtaric\\n13 min\\ntutorial\\nIntroduction to LangChain for Data Engineering & Data Applications\\nRichie Cotton\\n11 min\\ntutorial\\nHow to Build LLM Applications with LangChain Tutorial\\nMoez Ali\\n12 min\\ntutorial\\nBuilding Context-Aware Chatbots: Leveraging LangChain Framework for ChatGPT\\nAndrea Valenzuela\\n15 min\\ncode-along\\nBuilding AI Applications with LangChain and GPT\\nEmmanuel Pire\\ncode-along\\nIntroduction to Large Language Models with GPT & LangChain\\nRichie Cotton\\nGrow your data skills with DataCamp for Mobile\\nMake progress on the go with our mobile courses and daily 5-minute coding challenges.\\n© 2024 DataCamp, Inc. All Rights Reserved.'},\n", "  {'title': 'LangG<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>',\n", "   'url': 'https://www.langchain.com/langgraph',\n", "   'content': 'LangGraph is a framework for building and scaling agentic applications with LangChain Platform. Learn how to design agents that handle complex tasks, collaborate with humans, and stream intermediate steps with LangGraph.',\n", "   'score': 0.92176014,\n", "   'raw_content': 'Balance agent control with agency\\nGain precision and control with LangGraph to build agents that reliably handle complex tasks.\\nLangGraph Cloud is now in Beta\\nDeploy LangGraph agents at scale with LangGraph Cloud (available for Python). Get 1-click deployment, scalable servers and task queues, and integrated monitoring to streamline custom workflows.\\nControllable cognitive architecure for any task\\nLangGraph\\'s flexible API supports diverse control flows – single agent, multi-agent, hierarchical, sequential – and robustly handles realistic, complex scenarios. Ensure reliability with easy-to-add moderation and quality loops that prevent agents from veering off course.\\nDesigned for human-agent collaboration\\nWith built-in statefulness, LangGraph agents seamlessly collaborate with humans by writing drafts for review and awaiting approval before acting. Easily inspect the agent’s actions and \"time-travel\" to take a different action at a specific time.\\nFirst class streaming support for better UX responsiveness\\nBridge user expectations and agent capabilities with native token-by-token streaming and streaming of intermediate steps, delivering dynamic and interactive user experiences.\\nDeploy agents at scale, monitor carefully, iterate boldly\\nWith LangGraph Cloud, quickly deploy and scale your application, with infrastructure purpose-built for agents. Sign up for the beta.\\nFault-tolerant scalability\\nOptimized for real-world interactions\\nIntegrated developer experience\\nTrusted by companies taking agency in AI\\xa0innovation:\\nLangGraph helps teams of all sizes, across all industries, from ambitious\\nstartups to established enterprises.\\n“LangChain is streets ahead with what they\\'ve put forward with LangGraph. LangGraph sets the foundation for how we can build and scale AI workloads — from conversational agents, complex task automation, to custom LLM-backed experiences that \\'just work\\'. The next chapter in building complex production-ready features with LLMs is agentic, and with LangGraph and LangSmith, LangChain delivers an out-of-the-box solution to iterate quickly, debug immediately, and scale effortlessly.”\\n“LangGraph has been instrumental for our AI development. Its robust framework for building stateful, multi-actor applications with LLMs has transformed how we evaluate and optimize the performance of our AI guest-facing solutions. LangGraph enables granular control over the agent\\'s thought process, which has empowered us to make data-driven and deliberate decisions to meet the diverse needs of our guests.”\\n“It\\'s easy to build the prototype of a coding agent, but deceptively hard to improve its reliability. Replit wants to give a coding agent to millions of users — reliability is our top priority, and will remain so for a long time. LangGraph is giving us the control and ergonomics we need to build and ship powerful coding agents.”\\n“As Ally advances its exploration of Generative AI,\\n“As Ally advances its exploration of Generative AI, our tech labs is excited by LangGraph, the new library from LangChain, which is central to our experiments with multi-actor agentic workflows. We are committed to deepening our partnership with LangChain.”\\n“As Ally advances its exploration of Generative AI,\\nLangGraph FAQs\\nNo. LangGraph is an orchestration framework for complex agentic systems and is more low-level and controllable than LangChain agents.\\xa0On the other hand, LangChain provides a standard interface to interact with models and other components, useful for straight-forward chains and retrieval flows.\\nOther agentic frameworks can work for simple, generic tasks but fall short for complex tasks bespoke to a company’s needs. LangGraph provides a more expressive framework to handle companies’ unique tasks without restricting users to a single black-box cognitive architecture.\\nLangGraph will not add any overhead to your code and is specifically designed with streaming workflows in mind.\\nYes. LangGraph is an MIT-licensed open-source library and is free to use.\\nNo. LangGraph Cloud is proprietary software that will eventually be a paid service for certain tiers of usage. We will always give ample notice before charging for a service and reward our early adopters with preferential pricing.\\nFor now, LangGraph Cloud is in closed beta. You can join this waitlist to try it out for free, and we’ll notify you with deployment instructions once you’re off the waitlist. Check out the docs.\\nLangGraph is a stateful, orchestration framework that brings added control to agent workflows. LangGraph Cloud is a service for deploying and scaling LangGraph applications, with a built-in Studio for prototyping, debugging, and sharing LangGraph applications.\\nReady to start shipping\\nreliable GenAI apps faster?\\nGet started with LangChain, LangSmith, and LangGraph to enhance your LLM\\xa0app development, from prototype to production.'},\n", "  {'title': 'langgraph · PyPI',\n", "   'url': 'https://pypi.org/project/langgraph/',\n", "   'content': \"Project details\\nProject links\\nStatistics\\nView statistics for this project via Libraries.io, or by using our public dataset on Google BigQuery\\nMeta\\nLicense: Other/Proprietary License (LangGraph License)\\nRequires: Python >=3.9.0, <4.0\\nMaintainers\\nClassifiers\\nRelease history\\nRelease notifications |\\nRSS feed\\n0.0.24\\nFeb 8, 2024\\n0.0.23\\nFeb 4, 2024\\n0.0.22\\nFeb 4, 2024\\n0.0.21\\nJan 31, 2024\\n0.0.20\\nJan 27, 2024\\n0.0.19\\nJan 23, 2024\\n0.0.18\\nJan 23, 2024\\n0.0.17\\nJan 23, 2024\\n0.0.16\\nJan 21, 2024\\n0.0.15\\nJan 19, 2024\\n0.0.14\\nJan 18, 2024\\n0.0.13\\nJan 17, 2024\\n0.0.12\\nJan 17, 2024\\n0.0.11\\nJan 16, 2024\\n0.0.10\\nJan 9, 2024\\n0.0.9\\nJan 8, 2024\\n0.0.8\\nyanked\\nJan 8, 2024\\nDownload files\\nDownload the file for your platform. langgraph 0.0.24\\npip install langgraph\\nCopy PIP instructions\\nReleased:\\nFeb 8, 2024\\nlanggraph\\nNavigation\\nProject links\\nStatistics\\nView statistics for this project via Libraries.io, or by using our public dataset on Google BigQuery\\nMeta\\nLicense: Other/Proprietary License (LangGraph License)\\nRequires: Python >=3.9.0, <4.0\\nMaintainers\\nClassifiers\\nProject description\\n🦜🕸️LangGraph\\n⚡ Building language agents as graphs ⚡\\nOverview\\nLangGraph is a library for building stateful, multi-actor applications with LLMs, built on top of (and intended to be used with) LangChain.\\n Source Distribution\\nUploaded\\nFeb 8, 2024\\nsource\\nBuilt Distribution\\nUploaded\\nFeb 8, 2024\\npy3\\nHashes for langgraph-0.0.24.tar.gz\\nHashes for langgraph-0.0.24-py3-none-any.whl\\nHelp\\nAbout PyPI\\n It only has one argument:\\nNote: This does not need to be called if at any point you previously created an edge (conditional or normal) to END\\nGraph\\nThis has the same interface as StateGraph with the exception that it doesn't update a state object over time, and rather relies on passing around the full state from each step.\\n If the agent said that it was finished, then it should finish\\nNormal Edge: after the tools are invoked, it should always go back to the agent to decide what to do next\\nLet's define the nodes, as well as a function to decide how what conditional edge to take.\\n\",\n", "   'score': 0.8668671,\n", "   'raw_content': 'langgraph 0.0.24\\npip install langgraph\\nCopy PIP instructions\\nReleased:\\nFeb 8, 2024\\nlanggraph\\nNavigation\\nProject links\\nStatistics\\nView statistics for this project via Libraries.io, or by using our public dataset on Google BigQuery\\nMeta\\nLicense: Other/Proprietary License (LangGraph License)\\nRequires: Python >=3.9.0, <4.0\\nMaintainers\\nClassifiers\\nProject description\\n🦜🕸️LangGraph\\n⚡ Building language agents as graphs ⚡\\nOverview\\nLangGraph is a library for building stateful, multi-actor applications with LLMs, built on top of (and intended to be used with) LangChain.\\nIt extends the LangChain Expression Language with the ability to coordinate multiple chains (or actors) across multiple steps of computation in a cyclic manner.\\nIt is inspired by Pregel and Apache Beam.\\nThe current interface exposed is one inspired by NetworkX.\\nThe main use is for adding cycles to your LLM application.\\nCrucially, this is NOT a DAG framework.\\nIf you want to build a DAG, you should just use LangChain Expression Language.\\nCycles are important for agent-like behaviors, where you call an LLM in a loop, asking it what action to take next.\\nInstallation\\nQuick Start\\nHere we will go over an example of creating a simple agent that uses chat models and function calling.\\nThis agent will represent all its state as a list of messages.\\nWe will need to install some LangChain packages, as well as Tavily to use as an example tool.\\nWe also need to export some environment variables for OpenAI and Tavily API access.\\nOptionally, we can set up LangSmith for best-in-class observability.\\nSet up the tools\\nWe will first define the tools we want to use.\\nFor this simple example, we will use a built-in search tool via Tavily.\\nHowever, it is really easy to create your own tools - see documentation here on how to do that.\\nWe can now wrap these tools in a simple LangGraph ToolExecutor.\\nThis is a simple class that receives ToolInvocation objects, calls that tool, and returns the output.\\nToolInvocation is any class with tool and tool_input attributes.\\nSet up the model\\nNow we need to load the chat model we want to use.\\nImportantly, this should satisfy two criteria:\\nNote: these model requirements are not requirements for using LangGraph - they are just requirements for this one example.\\nAfter we\\'ve done this, we should make sure the model knows that it has these tools available to call.\\nWe can do this by converting the LangChain tools into the format for OpenAI function calling, and then bind them to the model class.\\nDefine the agent state\\nThe main type of graph in langgraph is the StatefulGraph.\\nThis graph is parameterized by a state object that it passes around to each node.\\nEach node then returns operations to update that state.\\nThese operations can either SET specific attributes on the state (e.g. overwrite the existing values) or ADD to the existing attribute.\\nWhether to set or add is denoted by annotating the state object you construct the graph with.\\nFor this example, the state we will track will just be a list of messages.\\nWe want each node to just add messages to that list.\\nTherefore, we will use a TypedDict with one key (messages) and annotate it so that the messages attribute is always added to.\\nDefine the nodes\\nWe now need to define a few different nodes in our graph.\\nIn langgraph, a node can be either a function or a runnable.\\nThere are two main nodes we need for this:\\nWe will also need to define some edges.\\nSome of these edges may be conditional.\\nThe reason they are conditional is that based on the output of a node, one of several paths may be taken.\\nThe path that is taken is not known until that node is run (the LLM decides).\\nConditional Edge: after the agent is called, we should either:\\na. If the agent said to take an action, then the function to invoke tools should be called\\nb. If the agent said that it was finished, then it should finish\\nNormal Edge: after the tools are invoked, it should always go back to the agent to decide what to do next\\nLet\\'s define the nodes, as well as a function to decide how what conditional edge to take.\\nDefine the graph\\nWe can now put it all together and define the graph!\\nUse it!\\nWe can now use it!\\nThis now exposes the same interface as all other LangChain runnables.\\nThis runnable accepts a list of messages.\\nThis may take a little bit - it\\'s making a few calls behind the scenes.\\nIn order to start seeing some intermediate results as they happen, we can use streaming - see below for more information on that.\\nStreaming\\nLangGraph has support for several different types of streaming.\\nStreaming Node Output\\nOne of the benefits of using LangGraph is that it is easy to stream output as it\\'s produced by each node.\\nStreaming LLM Tokens\\nYou can also access the LLM tokens as they are produced by each node.\\nIn this case only the \"agent\" node produces LLM tokens.\\nIn order for this to work properly, you must be using an LLM that supports streaming as well as have set it when constructing the LLM (e.g. ChatOpenAI(model=\"gpt-3.5-turbo-1106\", streaming=True))\\nWhen to Use\\nWhen should you use this versus LangChain Expression Language?\\nIf you need cycles.\\nLangchain Expression Language allows you to easily define chains (DAGs) but does not have a good mechanism for adding in cycles.\\nlanggraph adds that syntax.\\nExamples\\nChatAgentExecutor: with function calling\\nThis agent executor takes a list of messages as input and outputs a list of messages.\\nAll agent state is represented as a list of messages.\\nThis specifically uses OpenAI function calling.\\nThis is recommended agent executor for newer chat based models that support function calling.\\nModifications\\nWe also have a lot of examples highlighting how to slightly modify the base chat agent executor. These all build off the getting started notebook so it is recommended you start with that first.\\nAgentExecutor\\nThis agent executor uses existing LangChain agents.\\nModifications\\nWe also have a lot of examples highlighting how to slightly modify the base chat agent executor. These all build off the getting started notebook so it is recommended you start with that first.\\nMulti-agent Examples\\nChatbot Evaluation via Simulation\\nIt can often be tough to evaluation chat bots in multi-turn situations. One way to do this is with simulations.\\nAsync\\nIf you are running LangGraph in async workflows, you may want to create the nodes to be async by default.\\nFor a walkthrough on how to do that, see this documentation\\nStreaming Tokens\\nSometimes language models take a while to respond and you may want to stream tokens to end users.\\nFor a guide on how to do this, see this documentation\\nPersistence\\nLangGraph comes with built-in persistence, allowing you to save the state of the graph at point and resume from there.\\nFor a walkthrough on how to do that, see this documentation\\nHuman-in-the-loop\\nLangGraph comes with built-in support for human-in-the-loop workflows. This is useful when you want to have a human review the current state before proceeding to a particular node.\\nFor a walkthrough on how to do that, see this documentation\\nDocumentation\\nThere are only a few new APIs to use.\\nStateGraph\\nThe main entrypoint is StateGraph.\\nThis class is responsible for constructing the graph.\\nIt exposes an interface inspired by NetworkX.\\nThis graph is parameterized by a state object that it passes around to each node.\\nWhen constructing the graph, you need to pass in a schema for a state.\\nEach node then returns operations to update that state.\\nThese operations can either SET specific attributes on the state (e.g. overwrite the existing values) or ADD to the existing attribute.\\nWhether to set or add is denoted by annotating the state object you construct the graph with.\\nThe recommended way to specify the schema is with a typed dictionary: from typing import TypedDict\\nYou can then annotate the different attributes using from typing imoport Annotated.\\nCurrently, the only supported annotation is import operator; operator.add.\\nThis annotation will make it so that any node that returns this attribute ADDS that new result to the existing value.\\nLet\\'s take a look at an example:\\nWe can then use this like:\\nThis method adds a node to the graph.\\nIt takes two arguments:\\nCreates an edge from one node to the next.\\nThis means that output of the first node will be passed to the next node.\\nIt takes two arguments.\\nThis method adds conditional edges.\\nWhat this means is that only one of the downstream edges will be taken, and which one that is depends on the results of the start node.\\nThis takes three arguments:\\nThe entrypoint to the graph.\\nThis is the node that is first called.\\nIt only takes one argument:\\nThis is the exit point of the graph.\\nWhen this node is called, the results will be the final result from the graph.\\nIt only has one argument:\\nNote: This does not need to be called if at any point you previously created an edge (conditional or normal) to END\\nGraph\\nThis has the same interface as StateGraph with the exception that it doesn\\'t update a state object over time, and rather relies on passing around the full state from each step.\\nThis means that whatever is returned from one node is the input to the next as is.\\nEND\\nThis is a special node representing the end of the graph.\\nThis means that anything passed to this node will be the final output of the graph.\\nIt can be used in two places:\\nPrebuilt Examples\\nThere are also a few methods we\\'ve added to make it easy to use common, prebuilt graphs and components.\\nToolExecutor\\nThis is a simple helper class to help with calling tools.\\nIt is parameterized by a list of tools:\\nIt then exposes a runnable interface.\\nIt can be used to call tools: you can pass in an AgentAction and it will look up the relevant tool and call it with the appropriate input.\\nchat_agent_executor.create_function_calling_executor\\nThis is a helper function for creating a graph that works with a chat model that utilizes function calling.\\nCan be created by passing in a model and a list of tools.\\nThe model must be one that supports OpenAI function calling.\\ncreate_agent_executor\\nThis is a helper function for creating a graph that works with LangChain Agents.\\nCan be created by passing in an agent and a list of tools.\\nProject details\\nProject links\\nStatistics\\nView statistics for this project via Libraries.io, or by using our public dataset on Google BigQuery\\nMeta\\nLicense: Other/Proprietary License (LangGraph License)\\nRequires: Python >=3.9.0, <4.0\\nMaintainers\\nClassifiers\\nRelease history\\nRelease notifications |\\nRSS feed\\n0.0.24\\nFeb 8, 2024\\n0.0.23\\nFeb 4, 2024\\n0.0.22\\nFeb 4, 2024\\n0.0.21\\nJan 31, 2024\\n0.0.20\\nJan 27, 2024\\n0.0.19\\nJan 23, 2024\\n0.0.18\\nJan 23, 2024\\n0.0.17\\nJan 23, 2024\\n0.0.16\\nJan 21, 2024\\n0.0.15\\nJan 19, 2024\\n0.0.14\\nJan 18, 2024\\n0.0.13\\nJan 17, 2024\\n0.0.12\\nJan 17, 2024\\n0.0.11\\nJan 16, 2024\\n0.0.10\\nJan 9, 2024\\n0.0.9\\nJan 8, 2024\\n0.0.8\\nyanked\\nJan 8, 2024\\nDownload files\\nDownload the file for your platform. If you\\'re not sure which to choose, learn more about installing packages.\\nSource Distribution\\nUploaded\\nFeb 8, 2024\\nsource\\nBuilt Distribution\\nUploaded\\nFeb 8, 2024\\npy3\\nHashes for langgraph-0.0.24.tar.gz\\nHashes for langgraph-0.0.24-py3-none-any.whl\\nHelp\\nAbout PyPI\\nContributing to PyPI\\nUsing PyPI\\nStatus:\\nall systems operational\\nDeveloped and maintained by the Python community, for the Python community.\\nDonate today!\\n\"PyPI\", \"Python Package Index\", and the blocks logos are registered trademarks of the Python Software Foundation.\\n© 2024 Python Software Foundation\\nSite map\\nSupported by'}],\n", " 'response_time': 1.86}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["docs[0]"]}, {"cell_type": "code", "execution_count": null, "id": "naEyCquUxiFT", "metadata": {"id": "naEyCquUxiFT"}, "outputs": [], "source": ["output = format_search_query_results(docs, max_tokens=500, include_raw_content=True)"]}, {"cell_type": "code", "execution_count": null, "id": "qHJ7CU1ixoLI", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qHJ7CU1ixoLI", "outputId": "8535a4c1-bac0-425e-bd5b-c9d1a61f79c6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Content from web search:\n", "\n", "Source Introduction - GitHub Pages:\n", "===\n", "URL: https://langchain-ai.github.io/langgraphjs/\n", "===\n", "Most relevant content from source: Overview¶. LangGraph is a library for building stateful, multi-actor applications with LLMs, used to create agent and multi-agent workflows. Check out an introductory tutorial here.. LangGraph is inspired by <PERSON>gel and Apache Beam.The public interface draws inspiration from NetworkX.LangGraph is built by LangChain Inc, the creators of LangChain, but can be used without LangChain.\n", "===\n", "Raw Content: 🦜🕸️LangGraph.js¶\n", "⚡ Building language agents as graphs ⚡\n", "Looking for the Python version? Click\n", "here ( docs).\n", "Overview¶\n", "Suppose you're building a customer support assistant. You want your assistant to be able to:\n", "LangGraph makes this all easy. First install:\n", "Then define your assistant:\n", "Now, run the graph:\n", "We configured the graph to wait before executing the action. The SqliteSaver persists the state. Resume at any time.\n", "The graph orchestrates everything:\n", "With LangGraph, you can build complex, stateful agents without getting bogged down in manual state and interrupt management. Just define your nodes, edges, and state schema - and let the graph take care of the rest.\n", "Tutorials¶\n", "Consult the Tutorials to learn more about building with LangGraph, including advanced use cases.\n", "How-To Guides¶\n", "Check out the How-To Guides for instructions on handling common tasks with LangGraph.\n", "Reference¶\n", "For documentation on the core APIs, check out the Reference docs.\n", "Conceptual Guides¶\n", "Once you've learned the basics, if you want to further understand LangGraph's core abstractions, check out the Conceptual Guides.\n", "Why LangGrap<PERSON>?¶\n", "LangGraph is framework agnostic (each node is a regular JavaScript function). It extends the core Runnable API (shared interface for streaming, async, and batch calls) to make it easy to:\n", "If you're building a straightforward DAG, Runnables are a great fit. But for more complex, stateful applications with nonlinear flows, LangGraph is the perfect tool for the job.\n", "\n", "Source ️LangGraph - GitHub Pages:\n", "===\n", "URL: https://langchain-ai.github.io/langgraph/\n", "===\n", "Most relevant content from source: Overview¶. LangGraph is a library for building stateful, multi-actor applications with LLMs, used to create agent and multi-agent workflows. Check out an introductory tutorial here.. LangGraph is inspired by <PERSON>gel and Apache Beam.The public interface draws inspiration from NetworkX.LangGraph is built by LangChain Inc, the creators of LangChain, but can be used without LangChain.\n", "===\n", "Raw Content: 🦜🕸️LangGraph¶\n", "\n", "\n", "\n", "\n", "⚡ Building language agents as graphs ⚡\n", "Note\n", "Looking for the JS version? See the JS repo and the JS docs.\n", "Overview¶\n", "LangGraph is a library for building\n", "stateful, multi-actor applications with LLMs, used to create agent and multi-agent\n", "workflows. Check out an introductory tutorial here.\n", "LangGraph is inspired by <PERSON><PERSON> and Apache Beam. The public interface draws inspiration from NetworkX. LangGraph is built by LangChain Inc, the creators of LangChain, but can be used without LangChain.\n", "Why use LangGraph?¶\n", "LangGraph powers production-grade agents, trusted by Linkedin, Uber, Klarna, GitLab, and many more. LangGraph provides fine-grained control over both the flow and state of your agent applications. It implements a central persistence layer, enabling features that are common to most agent architectures:\n", "Standardizing these components allows individuals and teams to focus on the behavior\n", "of their agent, instead of its supporting infrastructure.\n", "Through LangGraph Platform, LangGraph also provides tooling for\n", "the development, deployment, debugging, and monitoring of your applications.\n", "LangGraph integrates seamlessly with\n", "<PERSON><PERSON><PERSON><PERSON> and\n", "Lang<PERSON><PERSON> (but does not require them).\n", "To learn more about LangGrap<PERSON>, check out our first LangChain Academy\n", "course, Introduction to LangGraph, available for free\n", "here.\n", "LangGraph Platform¶\n", "LangGraph Platform is infrastructure for deploying LangGraph agents. It is a commercial solution for deploying agentic applications to production, built on the open-source LangGraph framework. The LangGraph Platform consists of several components that work together to support the development, deployment, debugging, and monitoring of LangGraph applications: LangGraph Server (APIs), LangGraph SDKs (clients for the APIs), LangGraph CLI (command line tool for building the server), and LangGraph Studio (UI/debugger).\n", "See deployment options here\n", "(includes a free tier).\n", "Here are some common issues that arise in complex deployments, which LangGraph Platform addresses:\n", "Installation¶\n", "Example¶\n", "Let's build a tool-calling ReAct-style agent that uses a search tool!\n", "Optionally, we can set up LangSmith for best-in-class observability.\n", "The simplest way to create a tool-calling agent in LangGraph is to use create_react_agent:\n", "Tip\n", "LangGraph is a low-level framework that allows you to implement any custom agent\n", "architectures.\n", "\n", "Source LangGraph Tutorial: What Is LangGraph and How to Use It?:\n", "===\n", "URL: https://www.datacamp.com/tutorial/langgraph-tutorial\n", "===\n", "Most relevant content from source: LangGraph is a library within the LangChain ecosystem that simplifies the development of complex, multi-agent large language model (LLM) applications. Learn how to use LangGraph to create stateful, flexible, and scalable systems with nodes, edges, and state management.\n", "===\n", "Raw Content: Discover content by tools and technology\n", "Discover content by data science topics\n", "LangGraph Tutorial: What Is LangGraph and How to Use It?\n", "Imagine you're building a complex, multi-agent large language model (LLM) application. It's exciting, but it comes with challenges: managing the state of various agents, coordinating their interactions, and handling errors effectively. This is where LangGraph can help.\n", "LangGraph is a library within the LangChain ecosystem designed to tackle these challenges head-on. LangGraph provides a framework for defining, coordinating, and executing multiple LLM agents (or chains) in a structured manner.\n", "It simplifies the development process by enabling the creation of cyclical graphs, which are essential for developing agent runtimes. With LangGraph, we can easily build robust, scalable, and flexible multi-agent systems.\n", "If you want to learn more about the LangChain ecosystem, I recommend this introduction to LangChain.\n", "What Is LangGrap<PERSON>?\n", "LangGraph enables us to create stateful, multi-actor applications utilizing LLMs as easily as possible. It extends the capabilities of LangChain, introducing the ability to create and manage cyclical graphs, which are pivotal for developing sophisticated agent runtimes. The core concepts of LangGraph include: graph structure, state management, and coordination.\n", "Graph structure\n", "Imagine your application as a directed graph. In LangGraph, each node represents an LLM agent, and the edges are the communication channels between these agents. This structure allows for clear and manageable workflows, where each agent performs specific tasks and passes information to other agents as needed.\n", "State management\n", "One of LangGraph's standout features is its automatic state management. This feature enables us to track and persist information across multiple interactions. As agents perform their tasks, the state is dynamically updated, ensuring the system maintains context and responds appropriately to new inputs.\n", "Coordination\n", "LangGraph ensures agents execute in the correct order and that necessary information is exchanged seamlessly. This coordination is vital for complex applications where multiple agents need to work together to achieve a common goal. By managing the flow of data and the sequence of operations, LangGraph allows developers to focus on the high-level logic of their applications rather than the intricacies of agent coordination.\n", "Why LangGrap<PERSON>?\n", "As I mentioned above, LangGraph offers several significant advantages for developers working with complex LLM applications. Here are some of the real-world benefits LangGraph offers.\n", "Simplified development\n", "LangGraph abstracts away the complexities associated with state management and agent coordination. This\n", "\n", "Source LangGraph - Lang<PERSON>hain:\n", "===\n", "URL: https://www.langchain.com/langgraph\n", "===\n", "Most relevant content from source: LangGraph is a framework for building and scaling agentic applications with LangChain Platform. Learn how to design agents that handle complex tasks, collaborate with humans, and stream intermediate steps with LangGraph.\n", "===\n", "Raw Content: Balance agent control with agency\n", "Gain precision and control with LangGraph to build agents that reliably handle complex tasks.\n", "LangGraph Cloud is now in Beta\n", "Deploy LangGraph agents at scale with LangGraph Cloud (available for Python). Get 1-click deployment, scalable servers and task queues, and integrated monitoring to streamline custom workflows.\n", "Controllable cognitive architecure for any task\n", "LangGraph's flexible API supports diverse control flows – single agent, multi-agent, hierarchical, sequential – and robustly handles realistic, complex scenarios. Ensure reliability with easy-to-add moderation and quality loops that prevent agents from veering off course.\n", "Designed for human-agent collaboration\n", "With built-in statefulness, LangGraph agents seamlessly collaborate with humans by writing drafts for review and awaiting approval before acting. Easily inspect the agent’s actions and \"time-travel\" to take a different action at a specific time.\n", "First class streaming support for better UX responsiveness\n", "Bridge user expectations and agent capabilities with native token-by-token streaming and streaming of intermediate steps, delivering dynamic and interactive user experiences.\n", "Deploy agents at scale, monitor carefully, iterate boldly\n", "With LangGraph Cloud, quickly deploy and scale your application, with infrastructure purpose-built for agents. Sign up for the beta.\n", "Fault-tolerant scalability\n", "Optimized for real-world interactions\n", "Integrated developer experience\n", "Trusted by companies taking agency in AI innovation:\n", "LangGraph helps teams of all sizes, across all industries, from ambitious\n", "startups to established enterprises.\n", "“LangChain is streets ahead with what they've put forward with LangGraph. LangGraph sets the foundation for how we can build and scale AI workloads — from conversational agents, complex task automation, to custom LLM-backed experiences that 'just work'. The next chapter in building complex production-ready features with LLMs is agentic, and with LangGraph and LangSmith, LangChain delivers an out-of-the-box solution to iterate quickly, debug immediately, and scale effortlessly.”\n", "“LangGraph has been instrumental for our AI development. Its robust framework for building stateful, multi-actor applications with LLMs has transformed how we evaluate and optimize the performance of our AI guest-facing solutions. LangGraph enables granular control over the agent's thought process, which has empowered us to make data-driven and deliberate decisions to meet the diverse needs of our guests.”\n", "“It's easy to build the prototype of a coding agent, but deceptively hard to improve its reliability. <PERSON><PERSON> wants to give a coding agent to\n", "\n", "Source langgraph · PyPI:\n", "===\n", "URL: https://pypi.org/project/langgraph/\n", "===\n", "Most relevant content from source: Project details\n", "Project links\n", "Statistics\n", "View statistics for this project via Libraries.io, or by using our public dataset on Google BigQuery\n", "Meta\n", "License: Other/Proprietary License (LangGraph License)\n", "Requires: Python >=3.9.0, <4.0\n", "Maintainers\n", "Classifiers\n", "Release history\n", "Release notifications |\n", "RSS feed\n", "0.0.24\n", "Feb 8, 2024\n", "0.0.23\n", "Feb 4, 2024\n", "0.0.22\n", "Feb 4, 2024\n", "0.0.21\n", "Jan 31, 2024\n", "0.0.20\n", "Jan 27, 2024\n", "0.0.19\n", "Jan 23, 2024\n", "0.0.18\n", "Jan 23, 2024\n", "0.0.17\n", "Jan 23, 2024\n", "0.0.16\n", "Jan 21, 2024\n", "0.0.15\n", "Jan 19, 2024\n", "0.0.14\n", "Jan 18, 2024\n", "0.0.13\n", "Jan 17, 2024\n", "0.0.12\n", "Jan 17, 2024\n", "0.0.11\n", "Jan 16, 2024\n", "0.0.10\n", "Jan 9, 2024\n", "0.0.9\n", "Jan 8, 2024\n", "0.0.8\n", "yanked\n", "Jan 8, 2024\n", "Download files\n", "Download the file for your platform. langgraph 0.0.24\n", "pip install langgraph\n", "Copy PIP instructions\n", "Released:\n", "Feb 8, 2024\n", "langgraph\n", "Navigation\n", "Project links\n", "Statistics\n", "View statistics for this project via Libraries.io, or by using our public dataset on Google BigQuery\n", "Meta\n", "License: Other/Proprietary License (LangGraph License)\n", "Requires: Python >=3.9.0, <4.0\n", "Maintainers\n", "Classifiers\n", "Project description\n", "🦜🕸️LangGraph\n", "⚡ Building language agents as graphs ⚡\n", "Overview\n", "LangGraph is a library for building stateful, multi-actor applications with LLMs, built on top of (and intended to be used with) LangChain.\n", " Source Distribution\n", "Uploaded\n", "Feb 8, 2024\n", "source\n", "Built Distribution\n", "Uploaded\n", "Feb 8, 2024\n", "py3\n", "Hashes for langgraph-0.0.24.tar.gz\n", "Hashes for langgraph-0.0.24-py3-none-any.whl\n", "Help\n", "About PyPI\n", " It only has one argument:\n", "Note: This does not need to be called if at any point you previously created an edge (conditional or normal) to END\n", "Graph\n", "This has the same interface as StateGraph with the exception that it doesn't update a state object over time, and rather relies on passing around the full state from each step.\n", " If the agent said that it was finished, then it should finish\n", "Normal Edge: after the tools are invoked, it should always go back to the agent to decide what to do next\n", "Let's define the nodes, as well as a function to decide how what conditional edge to take.\n", "\n", "===\n", "Raw Content: langgraph 0.0.24\n", "pip install langgraph\n", "Copy PIP instructions\n", "Released:\n", "Feb 8, 2024\n", "langgraph\n", "Navigation\n", "Project links\n", "Statistics\n", "View statistics for this project via Libraries.io, or by using our public dataset on Google BigQuery\n", "Meta\n", "License: Other/Proprietary License (LangGraph License)\n", "Requires: Python >=3.9.0, <4.0\n", "Maintainers\n", "Classifiers\n", "Project description\n", "🦜🕸️LangGraph\n", "⚡ Building language agents as graphs ⚡\n", "Overview\n", "LangGraph is a library for building stateful, multi-actor applications with LLMs, built on top of (and intended to be used with) LangChain.\n", "It extends the LangChain Expression Language with the ability to coordinate multiple chains (or actors) across multiple steps of computation in a cyclic manner.\n", "It is inspired by <PERSON><PERSON> and Apache Beam.\n", "The current interface exposed is one inspired by NetworkX.\n", "The main use is for adding cycles to your LLM application.\n", "Crucially, this is NOT a DAG framework.\n", "If you want to build a DAG, you should just use LangChain Expression Language.\n", "Cycles are important for agent-like behaviors, where you call an LLM in a loop, asking it what action to take next.\n", "Installation\n", "Quick Start\n", "Here we will go over an example of creating a simple agent that uses chat models and function calling.\n", "This agent will represent all its state as a list of messages.\n", "We will need to install some LangChain packages, as well as Tavi<PERSON> to use as an example tool.\n", "We also need to export some environment variables for OpenAI and Tavily API access.\n", "Optionally, we can set up LangSmith for best-in-class observability.\n", "Set up the tools\n", "We will first define the tools we want to use.\n", "For this simple example, we will use a built-in search tool via Tavily.\n", "However, it is really easy to create your own tools - see documentation here on how to do that.\n", "We can now wrap these tools in a simple LangGraph ToolExecutor.\n", "This is a simple class that receives ToolInvocation objects, calls that tool, and returns the output.\n", "ToolInvocation is any class with tool and tool_input attributes.\n", "Set up the model\n", "Now we need to load the chat model we want to use.\n", "Importantly, this should satisfy two criteria:\n", "Note: these\n"]}], "source": ["print(output)"]}, {"cell_type": "markdown", "id": "TVjGRN57M1Zp", "metadata": {"id": "TVjGRN57M1Zp"}, "source": ["## Default Report Template\n", "\n", "This is the starting point for the LLM to get an idea of how to build a general report and it will use this to build a custom report structure"]}, {"cell_type": "code", "execution_count": null, "id": "_Mub1ld70yih", "metadata": {"id": "_Mub1ld70yih"}, "outputs": [], "source": ["# Structure\n", "DEFAULT_REPORT_STRUCTURE = \"\"\"The report structure should focus on breaking-down the user-provided topic\n", "                              and building a comprehensive report in markdown using the following format:\n", "\n", "                              1. Introduction (no web search needed)\n", "                                    - Brief overview of the topic area\n", "\n", "                              2. Main Body Sections:\n", "                                    - Each section should focus on a sub-topic of the user-provided topic\n", "                                    - Include any key concepts and definitions\n", "                                    - Provide real-world examples or case studies where applicable\n", "\n", "                              3. Conclusion (no web search needed)\n", "                                    - Aim for 1 structural element (either a list of table) that distills the main body sections\n", "                                    - Provide a concise summary of the report\n", "\n", "                              When generating the final response in markdown, if there are special characters in the text,\n", "                              such as the dollar symbol, ensure they are escaped properly for correct rendering e.g $25.5 should become \\$25.5\n", "                          \"\"\""]}, {"cell_type": "markdown", "id": "w-R-V_1SM-Rn", "metadata": {"id": "w-R-V_1SM-Rn"}, "source": ["## Instruction Prompts for Report Planner\n", "\n", "There are two main instruction prompts:\n", "\n", "- __REPORT_PLAN_QUERY_GENERATOR_PROMPT:__ Helps the LLM to generate an initial list of questions based on the topic to get more information from the web about that topic so that it can plan the overall sections and structure of the report\n", "\n", "- __REPORT_PLAN_SECTION_GENERATOR_PROMPT:__ Here we feed the LLM with the default report template, the topic name and the search results from the intial queries generated to create a detailed structure for the report. The LLM will generate a structured response of the following fields for each major section which will be in the report (this is just the report structure - no content is created at this step):\n", "    - Name - Name for this section of the report.\n", "    - Description - Brief overview of the main topics and concepts to be covered in this section.\n", "    - Research - Whether to perform web search for this section of the report or not.\n", "    - Content - The content of the section, which you will leave blank for now."]}, {"cell_type": "code", "execution_count": null, "id": "vjoRG-IP0zkx", "metadata": {"id": "vjoRG-IP0zkx"}, "outputs": [], "source": ["REPORT_PLAN_QUERY_GENERATOR_PROMPT = \"\"\"You are an expert technical report writer, helping to plan a report.\n", "\n", "The report will be focused on the following topic:\n", "{topic}\n", "\n", "The report structure will follow these guidelines:\n", "{report_organization}\n", "\n", "Your goal is to generate {number_of_queries} search queries that will help gather comprehensive information for planning the report sections.\n", "\n", "The query should:\n", "1. Be related to the topic\n", "2. Help satisfy the requirements specified in the report organization\n", "\n", "Make the query specific enough to find high-quality, relevant sources while covering the depth and breadth needed for the report structure.\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "qBjAzBjd2pYa", "metadata": {"id": "qBjAzBjd2pYa"}, "outputs": [], "source": ["REPORT_PLAN_SECTION_GENERATOR_PROMPT = \"\"\"You are an expert technical report writer, helping to plan a report.\n", "\n", "Your goal is to generate the outline of the sections of the report.\n", "\n", "The overall topic of the report is:\n", "{topic}\n", "\n", "The report should follow this organizational structure:\n", "{report_organization}\n", "\n", "You should reflect on this additional context information from web searches to plan the main sections of the report:\n", "{search_context}\n", "\n", "Now, generate the sections of the report. Each section should have the following fields:\n", "- Name - Name for this section of the report.\n", "- Description - Brief overview of the main topics and concepts to be covered in this section.\n", "- Research - Whether to perform web search for this section of the report or not.\n", "- Content - The content of the section, which you will leave blank for now.\n", "\n", "Consider which sections require web search.\n", "For example, introduction and conclusion will not require research because they will distill information from other parts of the report.\n", "\"\"\"\n"]}, {"cell_type": "markdown", "id": "BcGmRLcQOE-U", "metadata": {"id": "BcGmRLcQOE-U"}, "source": ["## Node Function for Report Planner\n", "\n", "![](https://i.imgur.com/54Jyv71.png)\n", "\n", "This function uses the two prompts created above to:\n", " - First generate some queries based on the user topic\n", " - Search the web and get some information on these queries\n", " - Use this information to generate the overall structure of the report with the key sections necessary to be created"]}, {"cell_type": "code", "execution_count": null, "id": "OH8ihSnZ0hHf", "metadata": {"id": "OH8ihSnZ0hHf"}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "from langchain_core.messages import HumanMessage, SystemMessage\n", "\n", "llm = ChatOpenAI(model_name=\"gpt-4o\", temperature=0)\n", "\n", "async def generate_report_plan(state: ReportState):\n", "    \"\"\"Generate the overall plan for building the report\"\"\"\n", "    topic = state[\"topic\"]\n", "    print('--- Generating Report Plan ---')\n", "\n", "    report_structure = DEFAULT_REPORT_STRUCTURE\n", "    number_of_queries = 8\n", "\n", "    structured_llm = llm.with_structured_output(Queries)\n", "\n", "    system_instructions_query = REPORT_PLAN_QUERY_GENERATOR_PROMPT.format(\n", "        topic=topic,\n", "        report_organization=report_structure,\n", "        number_of_queries=number_of_queries\n", "    )\n", "\n", "    try:\n", "        # Generate queries\n", "        results = structured_llm.invoke([\n", "            SystemMessage(content=system_instructions_query),\n", "            HumanMessage(content='Generate search queries that will help with planning the sections of the report.')\n", "        ])\n", "\n", "        # Convert SearchQuery objects to strings\n", "        query_list = [\n", "            query.search_query if isinstance(query, SearchQuery) else str(query)\n", "            for query in results.queries\n", "        ]\n", "\n", "        # Search web and ensure we wait for results\n", "        search_docs = await run_search_queries(\n", "            query_list,\n", "            num_results=5,\n", "            include_raw_content=False\n", "        )\n", "\n", "        if not search_docs:\n", "            print(\"Warning: No search results returned\")\n", "            search_context = \"No search results available.\"\n", "        else:\n", "            search_context = format_search_query_results(\n", "                search_docs,\n", "                include_raw_content=False\n", "            )\n", "\n", "        # Generate sections\n", "        system_instructions_sections = REPORT_PLAN_SECTION_GENERATOR_PROMPT.format(\n", "            topic=topic,\n", "            report_organization=report_structure,\n", "            search_context=search_context\n", "        )\n", "\n", "        structured_llm = llm.with_structured_output(Sections)\n", "        report_sections = structured_llm.invoke([\n", "            SystemMessage(content=system_instructions_sections),\n", "            HumanMessage(content=\"Generate the sections of the report. Your response must include a 'sections' field containing a list of sections. Each section must have: name, description, plan, research, and content fields.\")\n", "        ])\n", "\n", "        print('--- Generating Report Plan Completed ---')\n", "        return {\"sections\": report_sections.sections}\n", "\n", "    except Exception as e:\n", "        print(f\"Error in generate_report_plan: {e}\")\n", "        return {\"sections\": []}"]}, {"cell_type": "markdown", "id": "j8zCsQwuQbeO", "metadata": {"id": "j8zCsQwuQbeO"}, "source": ["## Instruction Prompts for Section Builder - Query Generator\n", "\n", "There is one main instruction prompt:\n", "\n", "- __REPORT_SECTION_QUERY_GENERATOR_PROMPT:__ Helps the LLM to generate a comprehensive list of questions for the topic of that specific section which needs to be built"]}, {"cell_type": "code", "execution_count": null, "id": "uxLrzsyY5Mdl", "metadata": {"id": "uxLrzsyY5Mdl"}, "outputs": [], "source": ["REPORT_SECTION_QUERY_GENERATOR_PROMPT = \"\"\"Your goal is to generate targeted web search queries that will gather comprehensive information for writing a technical report section.\n", "\n", "Topic for this section:\n", "{section_topic}\n", "\n", "When generating {number_of_queries} search queries, ensure that they:\n", "1. Cover different aspects of the topic (e.g., core features, real-world applications, technical architecture)\n", "2. Include specific technical terms related to the topic\n", "3. Target recent information by including year markers where relevant (e.g., \"2024\")\n", "4. Look for comparisons or differentiators from similar technologies/approaches\n", "5. Search for both official documentation and practical implementation examples\n", "\n", "Your queries should be:\n", "- Specific enough to avoid generic results\n", "- Technical enough to capture detailed implementation information\n", "- Diverse enough to cover all aspects of the section plan\n", "- Focused on authoritative sources (documentation, technical blogs, academic papers)\"\"\""]}, {"cell_type": "markdown", "id": "JP6AVB1YRqpG", "metadata": {"id": "JP6AVB1YRqpG"}, "source": ["## Node Function for Section Builder - Generate Queries (Query Generator)\n", "\n", "This uses the section topic and the instruction prompt above to generate some questions for researching on the web for getting useful information on the section topic"]}, {"cell_type": "code", "execution_count": null, "id": "1tdPfB6m3taO", "metadata": {"id": "1tdPfB6m3taO"}, "outputs": [], "source": ["def generate_queries(state: SectionState):\n", "    \"\"\" Generate search queries for a specific report section \"\"\"\n", "\n", "\n", "\n", "    # Get state\n", "    section = state[\"section\"]\n", "    print('--- Generating Search Queries for Section: '+ section.name +' ---')\n", "\n", "    # Get configuration\n", "    number_of_queries = 5\n", "\n", "    # Generate queries\n", "    structured_llm = llm.with_structured_output(Queries)\n", "\n", "    # Format system instructions\n", "    system_instructions = REPORT_SECTION_QUERY_GENERATOR_PROMPT.format(section_topic=section.description,\n", "                                                                       number_of_queries=number_of_queries)\n", "\n", "    # Generate queries\n", "    user_instruction = \"Generate search queries on the provided topic.\"\n", "    search_queries = structured_llm.invoke([SystemMessage(content=system_instructions),\n", "                                     HumanMessage(content=user_instruction)])\n", "\n", "    print('--- Generating Search Queries for Section: '+ section.name +' Completed ---')\n", "\n", "    return {\"search_queries\": search_queries.queries}"]}, {"cell_type": "markdown", "id": "6G_8doZxR8QC", "metadata": {"id": "6G_8doZxR8QC"}, "source": ["## Node Function for Section Builder - Search Web\n", "\n", "Takes the queries generated by `generate_queries(...)`for a specific section, searches the web and formats the search results using the utility functions we defined earlier"]}, {"cell_type": "code", "execution_count": null, "id": "Te1lHnkqRcBH", "metadata": {"id": "Te1lHnkqRcBH"}, "outputs": [], "source": ["async def search_web(state: SectionState):\n", "    \"\"\" Search the web for each query, then return a list of raw sources and a formatted string of sources.\"\"\"\n", "\n", "    # Get state\n", "    search_queries = state[\"search_queries\"]\n", "\n", "    print('--- Searching Web for Queries ---')\n", "\n", "    # Web search\n", "    query_list = [query.search_query for query in search_queries]\n", "    search_docs = await run_search_queries(search_queries, num_results=6, include_raw_content=True)\n", "\n", "    # Deduplicate and format sources\n", "    search_context = format_search_query_results(search_docs, max_tokens=4000, include_raw_content=True)\n", "\n", "    print('--- Searching Web for Queries Completed ---')\n", "\n", "    return {\"source_str\": search_context}\n"]}, {"cell_type": "markdown", "id": "5_ytIcDFUQMZ", "metadata": {"id": "5_ytIcDFUQMZ"}, "source": ["## Instruction Prompts for Section Builder - Section Writer\n", "\n", "There is one main instruction prompt:\n", "\n", "- __SECTION_WRITER_PROMPT:__ Constrains the LLM to generate and write the content for a specific section using certain guidelines on style, structure, length, approach and the documents obtained from the web earlier using the `search_web(...)` function are also sent."]}, {"cell_type": "code", "execution_count": null, "id": "Fc8VGgaK-UkT", "metadata": {"id": "Fc8VGgaK-UkT"}, "outputs": [], "source": ["SECTION_WRITER_PROMPT = \"\"\"You are an expert technical writer crafting one specific section of a technical report.\n", "\n", "Title for the section:\n", "{section_title}\n", "\n", "Topic for this section:\n", "{section_topic}\n", "\n", "Guidelines for writing:\n", "\n", "1. Technical Accuracy:\n", "- Include specific version numbers\n", "- Reference concrete metrics/benchmarks\n", "- Cite official documentation\n", "- Use technical terminology precisely\n", "\n", "2. Length and Style:\n", "- Strict 150-200 word limit\n", "- No marketing language\n", "- Technical focus\n", "- Write in simple, clear language do not use complex words unnecessarily\n", "- Start with your most important insight in **bold**\n", "- Use short paragraphs (2-3 sentences max)\n", "\n", "3. Structure:\n", "- Use ## for section title (Markdown format)\n", "- Only use ONE structural element IF it helps clarify your point:\n", "  * Either a focused table comparing 2-3 key items (using Markdown table syntax)\n", "  * Or a short list (3-5 items) using proper Markdown list syntax:\n", "    - Use `*` or `-` for unordered lists\n", "    - Use `1.` for ordered lists\n", "    - Ensure proper indentation and spacing\n", "- End with ### Sources that references the below source material formatted as:\n", "  * List each source with title, date, and URL\n", "  * Format: `- Title : URL`\n", "\n", "3. Writing Approach:\n", "- Include at least one specific example or case study if available\n", "- Use concrete details over general statements\n", "- Make every word count\n", "- No preamble prior to creating the section content\n", "- Focus on your single most important point\n", "\n", "4. Use this source material obtained from web searches to help write the section:\n", "{context}\n", "\n", "5. Quality Checks:\n", "- Format should be Markdown\n", "- Exactly 150-200 words (excluding title and sources)\n", "- Careful use of only ONE structural element (table or bullet list) and only if it helps clarify your point\n", "- One specific example / case study if available\n", "- Starts with bold insight\n", "- No preamble prior to creating the section content\n", "- Sources cited at end\n", "- If there are special characters in the text, such as the dollar symbol,\n", "  ensure they are escaped properly for correct rendering e.g $25.5 should become \\$25.5\n", "\"\"\""]}, {"cell_type": "markdown", "id": "5Vg97bh3USLp", "metadata": {"id": "5Vg97bh3USLp"}, "source": ["## Node Function for Section Builder - Write Section (Section Writer)\n", "\n", "Uses the SECTION_WRITER_PROMPT from above and feeds it with the section name, description and web search documents and passes it to an LLM to write the content for that section"]}, {"cell_type": "code", "execution_count": null, "id": "mSgrxeeJ8I-O", "metadata": {"id": "mSgrxeeJ8I-O"}, "outputs": [], "source": ["def write_section(state: SectionState):\n", "    \"\"\" Write a section of the report \"\"\"\n", "\n", "    # Get state\n", "    section = state[\"section\"]\n", "    source_str = state[\"source_str\"]\n", "\n", "    print('--- Writing Section : '+ section.name +' ---')\n", "\n", "    # Format system instructions\n", "    system_instructions = SECTION_WRITER_PROMPT.format(section_title=section.name,\n", "                                                       section_topic=section.description,\n", "                                                       context=source_str)\n", "\n", "    # Generate section\n", "    user_instruction = \"Generate a report section based on the provided sources.\"\n", "    section_content = llm.invoke([SystemMessage(content=system_instructions),\n", "                                  HumanMessage(content=user_instruction)])\n", "\n", "    # Write content to the section object\n", "    section.content = section_content.content\n", "\n", "    print('--- Writing Section : '+ section.name +' Completed ---')\n", "\n", "    # Write the updated section to completed sections\n", "    return {\"completed_sections\": [section]}"]}, {"cell_type": "markdown", "id": "JIvVGOPwSNgQ", "metadata": {"id": "JIvVGOPwSNgQ"}, "source": ["## Create the Section Builder Sub-Agent\n", "\n", "![](https://i.imgur.com/5VEYGrQ.png)\n", "\n", "This agent (or to be more specific, sub-agent) will be called several times in parallel, once for each section to search the web, get content and then write up that specific section"]}, {"cell_type": "code", "execution_count": null, "id": "UYB9crmZRcDD", "metadata": {"id": "UYB9crmZRcDD"}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "\n", "# Add nodes and edges\n", "section_builder = StateGraph(SectionState, output=SectionOutputState)\n", "section_builder.add_node(\"generate_queries\", generate_queries)\n", "section_builder.add_node(\"search_web\", search_web)\n", "section_builder.add_node(\"write_section\", write_section)\n", "\n", "section_builder.add_edge(START, \"generate_queries\")\n", "section_builder.add_edge(\"generate_queries\", \"search_web\")\n", "section_builder.add_edge(\"search_web\", \"write_section\")\n", "section_builder.add_edge(\"write_section\", END)\n", "section_builder_subagent = section_builder.compile()"]}, {"cell_type": "code", "execution_count": null, "id": "kkGDMBRTRcFx", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 449}, "id": "kkGDMBRTRcFx", "outputId": "c070cdba-dfef-40d5-cd45-00ab705662af"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display the graph\n", "from IPython.display import display, Image\n", "Image(section_builder_subagent.get_graph().draw_mermaid_png())"]}, {"cell_type": "markdown", "id": "jrw1YPEHWD12", "metadata": {"id": "jrw1YPEHWD12"}, "source": ["### Create Dynamic Parallelization Node Function - Parallelize Section Writing\n", "\n", "`Send(...)` is used to parallelize and call the `section_builder_subagent` once for each section to write up the content (in parallel)"]}, {"cell_type": "code", "execution_count": null, "id": "W7_5uWR2DkgN", "metadata": {"id": "W7_5uWR2DkgN"}, "outputs": [], "source": ["from langgraph.constants import Send\n", "\n", "def parallelize_section_writing(state: ReportState):\n", "    \"\"\" This is the \"map\" step when we kick off web research for some sections of the report in parallel and then write the section\"\"\"\n", "\n", "    # Kick off section writing in parallel via Send() API for any sections that require research\n", "    return [\n", "        Send(\"section_builder_with_web_search\", # name of the subagent node\n", "             {\"section\": s})\n", "            for s in state[\"sections\"]\n", "              if s.research\n", "    ]"]}, {"cell_type": "markdown", "id": "YDquAhSNYz06", "metadata": {"id": "YDquAhSNYz06"}, "source": ["## Create Format Sections Node Function\n", "\n", "This is basically the section where all the sections are formatted and combined together into one big document.\n", "\n", "![](https://i.imgur.com/6e5ZWK4.png)"]}, {"cell_type": "code", "execution_count": null, "id": "uNlTvupUCHI9", "metadata": {"id": "uNlTvupUCHI9"}, "outputs": [], "source": ["def format_sections(sections: list[Section]) -> str:\n", "    \"\"\" Format a list of report sections into a single text string \"\"\"\n", "    formatted_str = \"\"\n", "    for idx, section in enumerate(sections, 1):\n", "        formatted_str += f\"\"\"\n", "{'='*60}\n", "Section {idx}: {section.name}\n", "{'='*60}\n", "Description:\n", "{section.description}\n", "Requires Research:\n", "{section.research}\n", "\n", "Content:\n", "{section.content if section.content else '[Not yet written]'}\n", "\n", "\"\"\"\n", "    return formatted_str\n", "\n", "\n", "def format_completed_sections(state: ReportState):\n", "    \"\"\" <PERSON><PERSON> completed sections from research and format them as context for writing the final sections \"\"\"\n", "\n", "    print('--- Formatting Completed Sections ---')\n", "\n", "    # List of completed sections\n", "    completed_sections = state[\"completed_sections\"]\n", "\n", "    # Format completed section to str to use as context for final sections\n", "    completed_report_sections = format_sections(completed_sections)\n", "\n", "    print('--- Formatting Completed Sections is Done ---')\n", "\n", "    return {\"report_sections_from_research\": completed_report_sections}\n"]}, {"cell_type": "markdown", "id": "l22EjIp1ZUTj", "metadata": {"id": "l22EjIp1ZUTj"}, "source": ["## Instruction Prompts for Final Section\n", "\n", "There is one main instruction prompt:\n", "\n", "- __FINAL_SECTION_WRITER_PROMPT:__ Constrains the LLM to generate and write the content for either the introduction OR conclusion using certain guidelines on style, structure, length, approach and the content of the already written sections are also sent."]}, {"cell_type": "code", "execution_count": null, "id": "OEZqj7PFA2U_", "metadata": {"id": "OEZqj7PFA2U_"}, "outputs": [], "source": ["FINAL_SECTION_WRITER_PROMPT = \"\"\"You are an expert technical writer crafting a section that synthesizes information from the rest of the report.\n", "\n", "Title for the section:\n", "{section_title}\n", "\n", "Topic for this section:\n", "{section_topic}\n", "\n", "Available report content of already completed sections:\n", "{context}\n", "\n", "1. Section-Specific Approach:\n", "\n", "For Introduction:\n", "- Use # for report title (Markdown format)\n", "- 50-100 word limit\n", "- Write in simple and clear language\n", "- Focus on the core motivation for the report in 1-2 paragraphs\n", "- Use a clear narrative arc to introduce the report\n", "- Include NO structural elements (no lists or tables)\n", "- No sources section needed\n", "\n", "For Conclusion/Summary:\n", "- Use ## for section title (Markdown format)\n", "- 100-150 word limit\n", "- For comparative reports:\n", "    * Must include a focused comparison table using Markdown table syntax\n", "    * Table should distill insights from the report\n", "    * Keep table entries clear and concise\n", "- For non-comparative reports:\n", "    * Only use ONE structural element IF it helps distill the points made in the report:\n", "    * Either a focused table comparing items present in the report (using Markdown table syntax)\n", "    * Or a short list using proper Markdown list syntax:\n", "      - Use `*` or `-` for unordered lists\n", "      - Use `1.` for ordered lists\n", "      - Ensure proper indentation and spacing\n", "- End with specific next steps or implications\n", "- No sources section needed\n", "\n", "3. Writing Approach:\n", "- Use concrete details over general statements\n", "- Make every word count\n", "- Focus on your single most important point\n", "\n", "4. Quality Checks:\n", "- For introduction: 50-100 word limit, # for report title, no structural elements, no sources section\n", "- For conclusion: 100-150 word limit, ## for section title, only ONE structural element at most, no sources section\n", "- Markdown format\n", "- Do not include word count or any preamble in your response\n", "- If there are special characters in the text, such as the dollar symbol,\n", "  ensure they are escaped properly for correct rendering e.g $25.5 should become \\$25.5\"\"\""]}, {"cell_type": "markdown", "id": "Myztb9Eeb-88", "metadata": {"id": "Myztb9Eeb-88"}, "source": ["## Create Write Final Sections Node Function\n", "\n", "This function uses the instruction prompot FINAL_SECTION_WRITER_PROMPT mentioned above to write up the introduction and conclusion. This function will be executed in parallel using `Send(...)` below\n", "\n", "![](https://i.imgur.com/pRv4PX8.png)"]}, {"cell_type": "code", "execution_count": null, "id": "rRSoWr_MAIun", "metadata": {"id": "rRSoWr_MAIun"}, "outputs": [], "source": ["def write_final_sections(state: SectionState):\n", "    \"\"\" Write the final sections of the report, which do not require web search and use the completed sections as context\"\"\"\n", "\n", "    # Get state\n", "    section = state[\"section\"]\n", "    completed_report_sections = state[\"report_sections_from_research\"]\n", "\n", "    print('--- Writing Final Section: '+ section.name + ' ---')\n", "\n", "    # Format system instructions\n", "    system_instructions = FINAL_SECTION_WRITER_PROMPT.format(section_title=section.name,\n", "                                                             section_topic=section.description,\n", "                                                             context=completed_report_sections)\n", "\n", "    # Generate section\n", "    user_instruction = \"Craft a report section based on the provided sources.\"\n", "    section_content = llm.invoke([SystemMessage(content=system_instructions),\n", "                                  HumanMessage(content=user_instruction)])\n", "\n", "    # Write content to section\n", "    section.content = section_content.content\n", "\n", "    print('--- Writing Final Section: '+ section.name + ' Completed ---')\n", "\n", "    # Write the updated section to completed sections\n", "    return {\"completed_sections\": [section]}"]}, {"cell_type": "markdown", "id": "XIHk7dkbdGdn", "metadata": {"id": "XIHk7dkbdGdn"}, "source": ["### Create Dynamic Parallelization Node Function - Parallelize Final Section Writing\n", "\n", "`Send(...)` is used to parallelize and call the `write_final_sections` once for each of the introduction and conclusion to write up the content (in parallel)"]}, {"cell_type": "code", "execution_count": null, "id": "gaXMkCuZDP9h", "metadata": {"id": "gaXMkCuZDP9h"}, "outputs": [], "source": ["from langgraph.constants import Send\n", "\n", "def parallelize_final_section_writing(state: ReportState):\n", "    \"\"\" Write any final sections using the Send API to parallelize the process \"\"\"\n", "\n", "    # Kick off section writing in parallel via Send() API for any sections that do not require research\n", "    return [\n", "        Send(\"write_final_sections\",\n", "             {\"section\": s, \"report_sections_from_research\": state[\"report_sections_from_research\"]})\n", "                 for s in state[\"sections\"]\n", "                    if not s.research\n", "    ]"]}, {"cell_type": "markdown", "id": "fCwKY0o_dWeM", "metadata": {"id": "fCwKY0o_dWeM"}, "source": ["## Compile Final Report Node Function\n", "\n", "This function combines all the sections of the report together and compiles it into the final report document\n", "\n", "![](https://i.imgur.com/wLxCNZ5.png)"]}, {"cell_type": "code", "execution_count": null, "id": "PPlIQZl2Ddrk", "metadata": {"id": "PPlIQZl2Ddrk"}, "outputs": [], "source": ["def compile_final_report(state: ReportState):\n", "    \"\"\" Compile the final report \"\"\"\n", "\n", "    # Get sections\n", "    sections = state[\"sections\"]\n", "    completed_sections = {s.name: s.content for s in state[\"completed_sections\"]}\n", "\n", "    print('--- Compiling Final Report ---')\n", "\n", "    # Update sections with completed content while maintaining original order\n", "    for section in sections:\n", "        section.content = completed_sections[section.name]\n", "\n", "    # Compile final report\n", "    all_sections = \"\\n\\n\".join([s.content for s in sections])\n", "    # Escape unescaped $ symbols to display properly in Markdown\n", "    formatted_sections = all_sections.replace(\"\\\\$\", \"TEMP_PLACEHOLDER\")  # Temporarily mark already escaped $\n", "    formatted_sections = formatted_sections.replace(\"$\", \"\\\\$\")  # Escape all $\n", "    formatted_sections = formatted_sections.replace(\"TEMP_PLACEHOLDER\", \"\\\\$\")  # Restore originally escaped $\n", "\n", "# Now escaped_sections contains the properly escaped Markdown text\n", "\n", "\n", "    print('--- Compiling Final Report Done ---')\n", "\n", "    return {\"final_report\": formatted_sections}\n"]}, {"cell_type": "markdown", "id": "i10VLrxKePMo", "metadata": {"id": "i10VLrxKePMo"}, "source": ["## Build our Report Writer Planning Agent\n", "\n", "We now bring all the defined components and sub-agent together and build our planning agent\n", "\n", "![](https://i.imgur.com/STSC73k.png)"]}, {"cell_type": "code", "execution_count": null, "id": "8WhUNRSJDLGX", "metadata": {"id": "8WhUNRSJDLGX"}, "outputs": [], "source": ["builder = StateGraph(ReportState, input=ReportStateInput, output=ReportStateOutput)\n", "\n", "builder.add_node(\"generate_report_plan\", generate_report_plan)\n", "builder.add_node(\"section_builder_with_web_search\", section_builder_subagent)\n", "builder.add_node(\"format_completed_sections\", format_completed_sections)\n", "builder.add_node(\"write_final_sections\", write_final_sections)\n", "builder.add_node(\"compile_final_report\", compile_final_report)\n", "\n", "builder.add_edge(START, \"generate_report_plan\")\n", "builder.add_conditional_edges(\"generate_report_plan\",\n", "                              parallelize_section_writing,\n", "                              [\"section_builder_with_web_search\"])\n", "builder.add_edge(\"section_builder_with_web_search\", \"format_completed_sections\")\n", "builder.add_conditional_edges(\"format_completed_sections\",\n", "                              parallelize_final_section_writing,\n", "                              [\"write_final_sections\"])\n", "builder.add_edge(\"write_final_sections\", \"compile_final_report\")\n", "builder.add_edge(\"compile_final_report\", END)\n", "\n", "reporter_agent = builder.compile()"]}, {"cell_type": "code", "execution_count": null, "id": "d0owAmm_j5I-", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 896}, "id": "d0owAmm_j5I-", "outputId": "ab374eaf-b9bd-46ab-ff2a-b52e063af8dc"}, "outputs": [{"data": {"image/png": "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***************************************************************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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(Image(reporter_agent.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "LfIg-JDVearE", "metadata": {"id": "LfIg-JDVearE"}, "source": ["## Run and Test our Agent"]}, {"cell_type": "code", "execution_count": null, "id": "ddra7VuHbiwn", "metadata": {"id": "ddra7VuHbiwn"}, "outputs": [], "source": ["from IPython.display import display\n", "from rich.console import Console\n", "from rich.markdown import Markdown as RichMarkdown\n", "\n", "async def call_planner_agent(agent, prompt, config={\"recursion_limit\": 50}, verbose=False):\n", "    events = agent.astream(\n", "        {'topic' : prompt},\n", "        config,\n", "        stream_mode=\"values\",\n", "    )\n", "\n", "    async for event in events:\n", "        for k, v in event.items():\n", "            if verbose:\n", "                if k != \"__end__\":\n", "                    display(RichMarkdown(repr(k) + ' -> ' + repr(v)))\n", "            if k == 'final_report':\n", "                print('='*50)\n", "                print('Final Report:')\n", "                md = RichMarkdown(v)\n", "                display(md)"]}, {"cell_type": "code", "execution_count": null, "id": "WlDHdzLpFPSO", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "WlDHdzLpFPSO", "outputId": "f6ea071c-3cac-4e8a-cece-8a94ecd8cb4b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Generating Report Plan ---\n", "--- Generating Report Plan Completed ---\n", "--- Generating Search Queries for Section: Foundational Architecture of Agentic AI ------ Generating Search Queries for Section: Design Principles for Agentic AI Systems ------ Generating Search Queries for Section: Agentic AI Design Patterns ---\n", "\n", "--- Generating Search Queries for Section: Current Frameworks for Building Agentic AI ---\n", "\n", "--- Generating Search Queries for Section: Real-World Applications and Case Studies ---\n", "--- Generating Search Queries for Section: Challenges and Future Directions ---\n", "--- Generating Search Queries for Section: Agentic AI Design Patterns Completed ---\n", "--- Searching Web for Queries ---\n", "--- Generating Search Queries for Section: Foundational Architecture of Agentic AI Completed ---\n", "--- Searching Web for Queries ---\n", "--- Generating Search Queries for Section: Current Frameworks for Building Agentic AI Completed ---\n", "--- Searching Web for Queries ---\n", "--- Generating Search Queries for Section: Real-World Applications and Case Studies Completed ---\n", "--- Generating Search Queries for Section: Challenges and Future Directions Completed ---\n", "--- Generating Search Queries for Section: Design Principles for Agentic AI Systems Completed ---\n", "--- Searching Web for Queries ---\n", "--- Searching Web for Queries ---\n", "--- Searching Web for Queries ---\n", "--- Searching Web for Queries Completed ---\n", "--- Writing Section : Current Frameworks for Building Agentic AI ---\n", "--- Searching Web for Queries Completed ---\n", "--- Writing Section : Real-World Applications and Case Studies ---\n", "--- Searching Web for Queries Completed ---\n", "--- Writing Section : Challenges and Future Directions ---\n", "--- Searching Web for Queries Completed ---\n", "--- Writing Section : Agentic AI Design Patterns ---\n", "--- Searching Web for Queries Completed ---\n", "--- Writing Section : Design Principles for Agentic AI Systems ---\n", "--- Searching Web for Queries Completed ---\n", "--- Writing Section : Foundational Architecture of Agentic AI ---\n", "--- Writing Section : Design Principles for Agentic AI Systems Completed ---\n", "--- Writing Section : Real-World Applications and Case Studies Completed ---\n", "--- Writing Section : Current Frameworks for Building Agentic AI Completed ---\n", "--- Writing Section : Agentic AI Design Patterns Completed ---\n", "--- Writing Section : Challenges and Future Directions Completed ---\n", "--- Writing Section : Foundational Architecture of Agentic AI Completed ---\n", "--- Formatting Completed Sections ---\n", "--- Formatting Completed Sections is Done ---\n", "--- Writing Final Section: Introduction ------ Writing Final Section: Conclusion ---\n", "\n", "--- Writing Final Section: Introduction Completed ---\n", "--- Writing Final Section: Conclusion Completed ---\n", "--- Compiling Final Report ---\n", "--- Compiling Final Report Done ---\n", "==================================================\n", "Final Report:\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃                                                  <span style=\"font-weight: bold\">Introduction</span>                                                   ┃\n", "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n", "\n", "Agentic AI systems represent a significant evolution in artificial intelligence, transitioning from traditional    \n", "models to autonomous agents capable of complex decision-making. These systems are designed to operate              \n", "independently, adapting to dynamic environments without constant human oversight. Unlike traditional AI, which     \n", "often requires human intervention, Agentic AI systems prioritize autonomy, adaptability, and collaboration,        \n", "enabling them to perceive, reason, and act towards specific goals. This shift is transforming industries by        \n", "enhancing operational efficiency and enabling complex task execution across various domains, such as healthcare,   \n", "finance, and logistics. As Agentic AI continues to evolve, it promises to drive innovation and efficiency,         \n", "reshaping the future of technology and its applications.                                                           \n", "\n", "\n", "                                      <span style=\"font-weight: bold; text-decoration: underline\">Foundational Architecture of Agentic AI</span>                                      \n", "\n", "<span style=\"font-weight: bold\">Agentic AI systems are revolutionizing autonomous decision-making by integrating core components like memory, </span>     \n", "<span style=\"font-weight: bold\">tools, and planning mechanisms.</span> These systems are designed to operate independently, adapting to dynamic           \n", "environments without constant human oversight. A key example is the use of agentic AI in autonomous vehicles, where\n", "AI processes real-time data from sensors to navigate and make decisions, enhancing safety and efficiency (Source:  \n", "Daffodil).                                                                                                         \n", "\n", "Agentic AI's architecture typically includes several layers: a knowledge foundation, an intelligence layer, and an \n", "action layer. The knowledge foundation uses real-time data retrieval to update its knowledge base, while the       \n", "intelligence layer employs planning engines to break down complex tasks into manageable steps. The action layer    \n", "executes decisions through APIs and service connections, ensuring seamless integration with business systems       \n", "(Source: Microsoft Community Hub).                                                                                 \n", "\n", "These systems are not only transforming industries like healthcare and finance but also enhancing operational      \n", "efficiency in logistics and customer service. For instance, in healthcare, agentic AI assists in diagnostics and   \n", "treatment planning by analyzing patient data and providing real-time insights (Source: AIMultiple).                \n", "\n", "                                                      <span style=\"font-weight: bold\">Sources</span>                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Daffodil: https://insights.daffodilsw.com/blog/top-20-agentic-ai-use-cases-in-the-real-world                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Microsoft Community Hub:                                                                                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://techcommunity.microsoft.com/blog/machinelearningblog/baseline-agentic-ai-systems-architecture/4207137   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>AIMultiple: https://research.aimultiple.com/agentic-ai/                                                         \n", "\n", "\n", "                                     <span style=\"font-weight: bold; text-decoration: underline\">Design Principles for Agentic AI Systems</span>                                      \n", "\n", "<span style=\"font-weight: bold\">Agentic AI systems must prioritize autonomy, adaptability, and collaboration to effectively operate in dynamic </span>    \n", "<span style=\"font-weight: bold\">environments.</span> These systems, unlike traditional AI, are designed to independently perceive, reason, and act towards\n", "specific goals. For instance, autonomous vehicles utilize Agentic AI to navigate complex traffic scenarios without \n", "human intervention, showcasing the system's ability to adapt and make real-time decisions. Key design principles   \n", "include:                                                                                                           \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Autonomy:</span> Systems should function with minimal human oversight, as seen in AI-driven trading platforms that     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>autonomously analyze market trends and execute trades.                                                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Adaptability:</span> The ability to learn and adjust to new conditions is crucial. For example, AI in healthcare can   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>refine treatment plans based on patient data.                                                                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Collaboration:</span> Multi-agent systems, like those used in disaster management, require seamless interaction between\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>agents to achieve shared objectives.                                                                            \n", "\n", "These principles ensure that Agentic AI systems are robust, scalable, and capable of handling complex tasks across \n", "various industries.                                                                                                \n", "\n", "                                                      <span style=\"font-weight: bold\">Sources</span>                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Introduction to Agentic AI and Its Design Patterns:                                                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://lekha-bhan88.medium.com/introduction-to-agentic-ai-and-its-design-patterns-af8b7b3ef738                 \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>A Comprehensive Reference Guide to Building Agentic AI Systems:                                                 \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://medium.com/aingineer/a-comprehensive-reference-guide-to-building-agentic-ai-systems-823319cb282a        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>The Architecture of Agentic AI: Building Systems that Think and Act Autonomously:                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://www.arionresearch.com/blog/the-architecture-of-agentic-ai-building-systems-that-think-and-act-autonomous\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>ly                                                                                                              \n", "\n", "\n", "                                            <span style=\"font-weight: bold; text-decoration: underline\">Agentic AI Design Patterns</span>                                             \n", "\n", "<span style=\"font-weight: bold\">Agentic AI design patterns provide structured solutions for developing autonomous AI systems, enhancing their </span>     \n", "<span style=\"font-weight: bold\">decision-making and adaptability.</span> These patterns are crucial for building AI agents capable of reasoning, planning,\n", "and collaborating effectively. Key patterns include:                                                               \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Reflection Pattern:</span> This pattern allows AI agents to self-evaluate and refine their outputs, improving accuracy \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>and reliability. For instance, in content creation, an AI can generate a draft, critique it, and iteratively    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>enhance the content.                                                                                            \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Tool Use Pattern:</span> AI agents extend their capabilities by interacting with external tools. For example, an AI can\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>use APIs to fetch real-time data, enhancing its decision-making process.                                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Planning Pattern:</span> This involves breaking down complex tasks into manageable steps. An AI travel assistant, for  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>example, can plan a vacation itinerary by optimizing flights, accommodations, and activities.                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Multi-Agent Collaboration:</span> Multiple agents work together to achieve a common goal. In supply chain management,  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>different agents handle demand prediction, inventory optimization, and logistics coordination.                  \n", "\n", "These patterns streamline workflows and enable AI systems to tackle complex challenges efficiently.                \n", "\n", "                                                      <span style=\"font-weight: bold\">Sources</span>                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Introduction to Agentic AI and Its Design Patterns:                                                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://lekha-bhan88.medium.com/introduction-to-agentic-ai-and-its-design-patterns-af8b7b3ef738                 \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Top 4 Agentic AI Architecture Design Patterns:                                                                  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://mlubbad.medium.com/top-4-agentic-ai-architecture-design-patterns-2ad890a543e8                           \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Agentic AI Design Patterns Examples: https://vitalflux.com/agentic-ai-design-patterns-examples/                 \n", "\n", "\n", "                                    <span style=\"font-weight: bold; text-decoration: underline\">Current Frameworks for Building Agentic AI</span>                                     \n", "\n", "<span style=\"font-weight: bold\">Agentic AI frameworks are revolutionizing AI development by enabling autonomous decision-making and adaptability.</span>  \n", "These frameworks, such as Lyzr AI, Autogen, and LangChain, offer unique features that cater to different needs.    \n", "Lyzr AI, for instance, integrates advanced NLP capabilities and machine learning tools, providing a scalable       \n", "solution for businesses. It includes pre-built agents like Jazon and Skott, designed for specific business         \n", "functions like sales and content creation, ensuring a streamlined deployment process.                              \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Lyzr AI</span>: Known for its enterprise-grade security and scalability, Lyzr offers local deployment options, ensuring\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>data privacy. It is particularly suited for businesses requiring robust, customizable AI solutions.             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Autogen</span>: This framework excels in handling complex tasks with its multi-agent conversation capabilities. It     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>supports dynamic task allocation and is ideal for projects needing advanced planning and execution.             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">LangChain</span>: Popular for its extensive community support and comprehensive tools, LangChain is favored for        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>projects that require a large-scale, collaborative approach.                                                    \n", "\n", "These frameworks are pivotal in transforming traditional AI into proactive, goal-oriented systems, enhancing       \n", "efficiency and decision-making across various industries.                                                          \n", "\n", "                                                      <span style=\"font-weight: bold\">Sources</span>                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>The Best AI Agent Frameworks for 2024: A Complete Overview: https://www.lyzr.ai/blog/best-ai-agent-frameworks/  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Top 3 Trending Agentic AI Frameworks: LangGraph vs AutoGen vs Crew AI:                                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://www.datagrom.com/data-science-machine-learning-ai-blog/langgraph-vs-autogen-vs-crewai-comparison-agentic\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>-ai-frameworks                                                                                                  \n", "\n", "\n", "                                     <span style=\"font-weight: bold; text-decoration: underline\">Real-World Applications and Case Studies</span>                                      \n", "\n", "<span style=\"font-weight: bold\">Agentic AI systems are revolutionizing industries by enabling autonomous decision-making and complex task </span>         \n", "<span style=\"font-weight: bold\">execution.</span> For instance, in the healthcare sector, Agentic AI systems like IBM's Watson Health are transforming    \n", "diagnostics and treatment planning. Watson Health analyzes patient data to assist healthcare providers in making   \n", "informed decisions, improving diagnosis accuracy by 30% and reducing treatment time by 25% (Source: nanili.ai). In \n", "finance, Agentic AI is utilized for autonomous trading and fraud detection. Systems like JPMorgan Chase’s COIN     \n", "program analyze commercial loan agreements, saving 360,000 hours annually and reducing errors by 90% (Source:      \n", "CEOWORLD magazine).                                                                                                \n", "\n", "Agentic AI's impact extends to logistics, where it optimizes supply chains by predicting demand and managing       \n", "inventory autonomously. FedEx employs Agentic AI for intelligent logistics management, analyzing real-time data to \n", "enhance operational efficiency (Source: daffodilsw.com). These examples highlight the transformative potential of  \n", "Agentic AI across various domains, driving efficiency and innovation.                                              \n", "\n", "                                                      <span style=\"font-weight: bold\">Sources</span>                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>The Evolution of AI: From Traditional AI to Agentic AI:                                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://www.nanili.ai/blog/the-evolution-of-ai-from-traditional-ai-to-agentic-ai                                \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Agentic AI vs. Traditional AI: What's the Difference and How Will It Transform Business?:                       \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://ceoworld.biz/2024/11/15/agentic-ai-vs-traditional-ai-whats-the-difference-and-how-will-it-transform-busi\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>ness/                                                                                                           \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Top 20 Agentic AI Use Cases in the Real World:                                                                  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://insights.daffodilsw.com/blog/top-20-agentic-ai-use-cases-in-the-real-world                              \n", "\n", "\n", "                                         <span style=\"font-weight: bold; text-decoration: underline\">Challenges and Future Directions</span>                                          \n", "\n", "<span style=\"font-weight: bold\">Agentic AI systems face significant challenges in deployment, primarily due to unforeseen consequences and opacity.</span>\n", "These systems, characterized by their autonomy, can make unexpected decisions, complicating error diagnosis. For   \n", "instance, <PERSON><PERSON><PERSON>'s AI assistants autonomously handle two-thirds of customer service requests, but their            \n", "decision-making processes remain opaque, raising concerns about transparency and trust. Additionally, data privacy \n", "and security are critical, as these systems process vast amounts of sensitive information. Ensuring fairness and   \n", "avoiding bias is another challenge, as agentic AI can perpetuate existing biases in training data.                 \n", "\n", "Looking forward, several trends will shape the future of agentic AI:                                               \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Multi-Agent Systems:</span> These involve multiple AI agents collaborating to handle complex tasks, requiring robust   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>verification and validation mechanisms.                                                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Continuous Improvement:</span> Ongoing governance and adaptation are essential as technology evolves, necessitating    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>regular audits and human oversight.                                                                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Collaborative Development:</span> Partnerships among experts, policymakers, and the public are crucial to align AI     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>development with societal values.                                                                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Regulatory Frameworks:</span> Comprehensive regulations, like the European Union’s AI Act, will govern AI use, ensuring\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>ethical and responsible deployment.                                                                             \n", "\n", "                                                      <span style=\"font-weight: bold\">Sources</span>                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>The Future of Agentic AI: Opportunities and Challenges:                                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://medium.com/@hardik_vjti/the-future-of-agentic-ai-opportunities-and-challenges-99b7e226285f              \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>The Rise of Agentic AI: A Look Back at 2024 and Predictions for 2025:                                           \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://bardai.ai/2025/01/16/the-rise-of-agentic-ai-a-look-back-at-2024-and-predictions-for-2025/               \n", "\n", "\n", "                                                    <span style=\"font-weight: bold; text-decoration: underline\">Conclusion</span>                                                     \n", "\n", "Agentic AI systems are at the forefront of transforming industries through their autonomous decision-making        \n", "capabilities. The foundational architecture, comprising memory, tools, and planning mechanisms, enables these      \n", "systems to operate independently and adapt to dynamic environments. Key design principles such as autonomy,        \n", "adaptability, and collaboration ensure robust and scalable AI solutions. Design patterns like reflection, tool use,\n", "and multi-agent collaboration enhance system capabilities, providing structured solutions to common challenges.    \n", "\n", "Current frameworks like Lyzr AI, Autogen, and LangChain offer diverse features, catering to various industry needs.\n", "Real-world applications demonstrate significant impacts in healthcare, finance, and logistics, showcasing the      \n", "potential of Agentic AI to drive efficiency and innovation. However, challenges such as transparency, data privacy,\n", "and bias remain, necessitating ongoing governance and regulatory frameworks.                                       \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Next Steps:</span> Emphasize collaborative development and regulatory compliance to align AI advancements with societal\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>values, ensuring ethical and responsible deployment.                                                            \n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃                                                  \u001b[1mIntroduction\u001b[0m                                                   ┃\n", "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n", "\n", "Agentic AI systems represent a significant evolution in artificial intelligence, transitioning from traditional    \n", "models to autonomous agents capable of complex decision-making. These systems are designed to operate              \n", "independently, adapting to dynamic environments without constant human oversight. Unlike traditional AI, which     \n", "often requires human intervention, Agentic AI systems prioritize autonomy, adaptability, and collaboration,        \n", "enabling them to perceive, reason, and act towards specific goals. This shift is transforming industries by        \n", "enhancing operational efficiency and enabling complex task execution across various domains, such as healthcare,   \n", "finance, and logistics. As Agentic AI continues to evolve, it promises to drive innovation and efficiency,         \n", "reshaping the future of technology and its applications.                                                           \n", "\n", "\n", "                                      \u001b[1;4mFoundational Architecture of Agentic AI\u001b[0m                                      \n", "\n", "\u001b[1mAgentic AI systems are revolutionizing autonomous decision-making by integrating core components like memory, \u001b[0m     \n", "\u001b[1mtools, and planning mechanisms.\u001b[0m These systems are designed to operate independently, adapting to dynamic           \n", "environments without constant human oversight. A key example is the use of agentic AI in autonomous vehicles, where\n", "AI processes real-time data from sensors to navigate and make decisions, enhancing safety and efficiency (Source:  \n", "Daffodil).                                                                                                         \n", "\n", "Agentic AI's architecture typically includes several layers: a knowledge foundation, an intelligence layer, and an \n", "action layer. The knowledge foundation uses real-time data retrieval to update its knowledge base, while the       \n", "intelligence layer employs planning engines to break down complex tasks into manageable steps. The action layer    \n", "executes decisions through APIs and service connections, ensuring seamless integration with business systems       \n", "(Source: Microsoft Community Hub).                                                                                 \n", "\n", "These systems are not only transforming industries like healthcare and finance but also enhancing operational      \n", "efficiency in logistics and customer service. For instance, in healthcare, agentic AI assists in diagnostics and   \n", "treatment planning by analyzing patient data and providing real-time insights (Source: AIMultiple).                \n", "\n", "                                                      \u001b[1mSources\u001b[0m                                                      \n", "\n", "\u001b[1;33m • \u001b[0mDaffodil: https://insights.daffodilsw.com/blog/top-20-agentic-ai-use-cases-in-the-real-world                    \n", "\u001b[1;33m • \u001b[0mMicrosoft Community Hub:                                                                                        \n", "\u001b[1;33m   \u001b[0mhttps://techcommunity.microsoft.com/blog/machinelearningblog/baseline-agentic-ai-systems-architecture/4207137   \n", "\u001b[1;33m • \u001b[0mAIMultiple: https://research.aimultiple.com/agentic-ai/                                                         \n", "\n", "\n", "                                     \u001b[1;4mDesign Principles for Agentic AI Systems\u001b[0m                                      \n", "\n", "\u001b[1mAgentic AI systems must prioritize autonomy, adaptability, and collaboration to effectively operate in dynamic \u001b[0m    \n", "\u001b[1menvironments.\u001b[0m These systems, unlike traditional AI, are designed to independently perceive, reason, and act towards\n", "specific goals. For instance, autonomous vehicles utilize Agentic AI to navigate complex traffic scenarios without \n", "human intervention, showcasing the system's ability to adapt and make real-time decisions. Key design principles   \n", "include:                                                                                                           \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mAutonomy:\u001b[0m Systems should function with minimal human oversight, as seen in AI-driven trading platforms that     \n", "\u001b[1;33m   \u001b[0mautonomously analyze market trends and execute trades.                                                          \n", "\u001b[1;33m • \u001b[0m\u001b[1mAdaptability:\u001b[0m The ability to learn and adjust to new conditions is crucial. For example, AI in healthcare can   \n", "\u001b[1;33m   \u001b[0mrefine treatment plans based on patient data.                                                                   \n", "\u001b[1;33m • \u001b[0m\u001b[1mCollaboration:\u001b[0m Multi-agent systems, like those used in disaster management, require seamless interaction between\n", "\u001b[1;33m   \u001b[0magents to achieve shared objectives.                                                                            \n", "\n", "These principles ensure that Agentic AI systems are robust, scalable, and capable of handling complex tasks across \n", "various industries.                                                                                                \n", "\n", "                                                      \u001b[1mSources\u001b[0m                                                      \n", "\n", "\u001b[1;33m • \u001b[0mIntroduction to Agentic AI and Its Design Patterns:                                                             \n", "\u001b[1;33m   \u001b[0mhttps://lekha-bhan88.medium.com/introduction-to-agentic-ai-and-its-design-patterns-af8b7b3ef738                 \n", "\u001b[1;33m • \u001b[0mA Comprehensive Reference Guide to Building Agentic AI Systems:                                                 \n", "\u001b[1;33m   \u001b[0mhttps://medium.com/aingineer/a-comprehensive-reference-guide-to-building-agentic-ai-systems-823319cb282a        \n", "\u001b[1;33m • \u001b[0mThe Architecture of Agentic AI: Building Systems that Think and Act Autonomously:                               \n", "\u001b[1;33m   \u001b[0mhttps://www.arionresearch.com/blog/the-architecture-of-agentic-ai-building-systems-that-think-and-act-autonomous\n", "\u001b[1;33m   \u001b[0mly                                                                                                              \n", "\n", "\n", "                                            \u001b[1;4mAgentic AI Design Patterns\u001b[0m                                             \n", "\n", "\u001b[1mAgentic AI design patterns provide structured solutions for developing autonomous AI systems, enhancing their \u001b[0m     \n", "\u001b[1mdecision-making and adaptability.\u001b[0m These patterns are crucial for building AI agents capable of reasoning, planning,\n", "and collaborating effectively. Key patterns include:                                                               \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mReflection Pattern:\u001b[0m This pattern allows AI agents to self-evaluate and refine their outputs, improving accuracy \n", "\u001b[1;33m   \u001b[0mand reliability. For instance, in content creation, an AI can generate a draft, critique it, and iteratively    \n", "\u001b[1;33m   \u001b[0menhance the content.                                                                                            \n", "\u001b[1;33m • \u001b[0m\u001b[1mTool Use Pattern:\u001b[0m AI agents extend their capabilities by interacting with external tools. For example, an AI can\n", "\u001b[1;33m   \u001b[0muse APIs to fetch real-time data, enhancing its decision-making process.                                        \n", "\u001b[1;33m • \u001b[0m\u001b[1mPlanning Pattern:\u001b[0m This involves breaking down complex tasks into manageable steps. An AI travel assistant, for  \n", "\u001b[1;33m   \u001b[0me<PERSON><PERSON>, can plan a vacation itinerary by optimizing flights, accommodations, and activities.                   \n", "\u001b[1;33m • \u001b[0m\u001b[1mMulti-Agent Collaboration:\u001b[0m Multiple agents work together to achieve a common goal. In supply chain management,  \n", "\u001b[1;33m   \u001b[0mdifferent agents handle demand prediction, inventory optimization, and logistics coordination.                  \n", "\n", "These patterns streamline workflows and enable AI systems to tackle complex challenges efficiently.                \n", "\n", "                                                      \u001b[1mSources\u001b[0m                                                      \n", "\n", "\u001b[1;33m • \u001b[0mIntroduction to Agentic AI and Its Design Patterns:                                                             \n", "\u001b[1;33m   \u001b[0mhttps://lekha-bhan88.medium.com/introduction-to-agentic-ai-and-its-design-patterns-af8b7b3ef738                 \n", "\u001b[1;33m • \u001b[0mTop 4 Agentic AI Architecture Design Patterns:                                                                  \n", "\u001b[1;33m   \u001b[0mhttps://mlubbad.medium.com/top-4-agentic-ai-architecture-design-patterns-2ad890a543e8                           \n", "\u001b[1;33m • \u001b[0mAgentic AI Design Patterns Examples: https://vitalflux.com/agentic-ai-design-patterns-examples/                 \n", "\n", "\n", "                                    \u001b[1;4mCurrent Frameworks for Building Agentic AI\u001b[0m                                     \n", "\n", "\u001b[1mAgentic AI frameworks are revolutionizing AI development by enabling autonomous decision-making and adaptability.\u001b[0m  \n", "These frameworks, such as Lyzr AI, Autogen, and LangChain, offer unique features that cater to different needs.    \n", "Lyzr AI, for instance, integrates advanced NLP capabilities and machine learning tools, providing a scalable       \n", "solution for businesses. It includes pre-built agents like Jazon and Skott, designed for specific business         \n", "functions like sales and content creation, ensuring a streamlined deployment process.                              \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mLyzr AI\u001b[0m: Known for its enterprise-grade security and scalability, Lyzr offers local deployment options, ensuring\n", "\u001b[1;33m   \u001b[0mdata privacy. It is particularly suited for businesses requiring robust, customizable AI solutions.             \n", "\u001b[1;33m • \u001b[0m\u001b[1mAutogen\u001b[0m: This framework excels in handling complex tasks with its multi-agent conversation capabilities. It     \n", "\u001b[1;33m   \u001b[0msupports dynamic task allocation and is ideal for projects needing advanced planning and execution.             \n", "\u001b[1;33m • \u001b[0m\u001b[1m<PERSON><PERSON><PERSON><PERSON>n\u001b[0m: Popular for its extensive community support and comprehensive tools, LangChain is favored for        \n", "\u001b[1;33m   \u001b[0mprojects that require a large-scale, collaborative approach.                                                    \n", "\n", "These frameworks are pivotal in transforming traditional AI into proactive, goal-oriented systems, enhancing       \n", "efficiency and decision-making across various industries.                                                          \n", "\n", "                                                      \u001b[1mSources\u001b[0m                                                      \n", "\n", "\u001b[1;33m • \u001b[0mThe Best AI Agent Frameworks for 2024: A Complete Overview: https://www.lyzr.ai/blog/best-ai-agent-frameworks/  \n", "\u001b[1;33m • \u001b[0mTop 3 Trending Agentic AI Frameworks: LangGraph vs AutoGen vs Crew AI:                                          \n", "\u001b[1;33m   \u001b[0mhttps://www.datagrom.com/data-science-machine-learning-ai-blog/langgraph-vs-autogen-vs-crewai-comparison-agentic\n", "\u001b[1;33m   \u001b[0m-ai-frameworks                                                                                                  \n", "\n", "\n", "                                     \u001b[1;4mReal-World Applications and Case Studies\u001b[0m                                      \n", "\n", "\u001b[1mAgentic AI systems are revolutionizing industries by enabling autonomous decision-making and complex task \u001b[0m         \n", "\u001b[1mexecution.\u001b[0m For instance, in the healthcare sector, Agentic AI systems like IBM's Watson Health are transforming    \n", "diagnostics and treatment planning. Watson Health analyzes patient data to assist healthcare providers in making   \n", "informed decisions, improving diagnosis accuracy by 30% and reducing treatment time by 25% (Source: nanili.ai). In \n", "finance, Agentic AI is utilized for autonomous trading and fraud detection. Systems like JPMorgan Chase’s COIN     \n", "program analyze commercial loan agreements, saving 360,000 hours annually and reducing errors by 90% (Source:      \n", "CEOWORLD magazine).                                                                                                \n", "\n", "Agentic AI's impact extends to logistics, where it optimizes supply chains by predicting demand and managing       \n", "inventory autonomously. FedEx employs Agentic AI for intelligent logistics management, analyzing real-time data to \n", "enhance operational efficiency (Source: daffodilsw.com). These examples highlight the transformative potential of  \n", "Agentic AI across various domains, driving efficiency and innovation.                                              \n", "\n", "                                                      \u001b[1mSources\u001b[0m                                                      \n", "\n", "\u001b[1;33m • \u001b[0mThe Evolution of AI: From Traditional AI to Agentic AI:                                                         \n", "\u001b[1;33m   \u001b[0mhttps://www.nanili.ai/blog/the-evolution-of-ai-from-traditional-ai-to-agentic-ai                                \n", "\u001b[1;33m • \u001b[0mAgentic AI vs. Traditional AI: What's the Difference and How Will It Transform Business?:                       \n", "\u001b[1;33m   \u001b[0mhttps://ceoworld.biz/2024/11/15/agentic-ai-vs-traditional-ai-whats-the-difference-and-how-will-it-transform-busi\n", "\u001b[1;33m   \u001b[0mness/                                                                                                           \n", "\u001b[1;33m • \u001b[0mTop 20 Agentic AI Use Cases in the Real World:                                                                  \n", "\u001b[1;33m   \u001b[0mhttps://insights.daffodilsw.com/blog/top-20-agentic-ai-use-cases-in-the-real-world                              \n", "\n", "\n", "                                         \u001b[1;4mChallenges and Future Directions\u001b[0m                                          \n", "\n", "\u001b[1mAgentic AI systems face significant challenges in deployment, primarily due to unforeseen consequences and opacity.\u001b[0m\n", "These systems, characterized by their autonomy, can make unexpected decisions, complicating error diagnosis. For   \n", "instance, <PERSON><PERSON><PERSON>'s AI assistants autonomously handle two-thirds of customer service requests, but their            \n", "decision-making processes remain opaque, raising concerns about transparency and trust. Additionally, data privacy \n", "and security are critical, as these systems process vast amounts of sensitive information. Ensuring fairness and   \n", "avoiding bias is another challenge, as agentic AI can perpetuate existing biases in training data.                 \n", "\n", "Looking forward, several trends will shape the future of agentic AI:                                               \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mMulti-Agent Systems:\u001b[0m These involve multiple AI agents collaborating to handle complex tasks, requiring robust   \n", "\u001b[1;33m   \u001b[0mverification and validation mechanisms.                                                                         \n", "\u001b[1;33m • \u001b[0m\u001b[1mContinuous Improvement:\u001b[0m Ongoing governance and adaptation are essential as technology evolves, necessitating    \n", "\u001b[1;33m   \u001b[0mregular audits and human oversight.                                                                             \n", "\u001b[1;33m • \u001b[0m\u001b[1mCollaborative Development:\u001b[0m Partnerships among experts, policymakers, and the public are crucial to align AI     \n", "\u001b[1;33m   \u001b[0mdevelopment with societal values.                                                                               \n", "\u001b[1;33m • \u001b[0m\u001b[1mRegulatory Frameworks:\u001b[0m Comprehensive regulations, like the European Union’s AI Act, will govern AI use, ensuring\n", "\u001b[1;33m   \u001b[0methical and responsible deployment.                                                                             \n", "\n", "                                                      \u001b[1mSources\u001b[0m                                                      \n", "\n", "\u001b[1;33m • \u001b[0mThe Future of Agentic AI: Opportunities and Challenges:                                                         \n", "\u001b[1;33m   \u001b[0mhttps://medium.com/@hardik_vjti/the-future-of-agentic-ai-opportunities-and-challenges-99b7e226285f              \n", "\u001b[1;33m • \u001b[0mThe Rise of Agentic AI: A Look Back at 2024 and Predictions for 2025:                                           \n", "\u001b[1;33m   \u001b[0mhttps://bardai.ai/2025/01/16/the-rise-of-agentic-ai-a-look-back-at-2024-and-predictions-for-2025/               \n", "\n", "\n", "                                                    \u001b[1;4mConclusion\u001b[0m                                                     \n", "\n", "Agentic AI systems are at the forefront of transforming industries through their autonomous decision-making        \n", "capabilities. The foundational architecture, comprising memory, tools, and planning mechanisms, enables these      \n", "systems to operate independently and adapt to dynamic environments. Key design principles such as autonomy,        \n", "adaptability, and collaboration ensure robust and scalable AI solutions. Design patterns like reflection, tool use,\n", "and multi-agent collaboration enhance system capabilities, providing structured solutions to common challenges.    \n", "\n", "Current frameworks like Lyzr AI, Autogen, and LangChain offer diverse features, catering to various industry needs.\n", "Real-world applications demonstrate significant impacts in healthcare, finance, and logistics, showcasing the      \n", "potential of Agentic AI to drive efficiency and innovation. However, challenges such as transparency, data privacy,\n", "and bias remain, necessitating ongoing governance and regulatory frameworks.                                       \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mNext Steps:\u001b[0m Emphasize collaborative development and regulatory compliance to align AI advancements with societal\n", "\u001b[1;33m   \u001b[0mvalues, ensuring ethical and responsible deployment.                                                            \n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["topic = \"Detailed report on how to build Agentic AI systems, design patterns and current frameworks\"\n", "await call_planner_agent(agent=reporter_agent,\n", "                         prompt=topic)"]}, {"cell_type": "code", "execution_count": null, "id": "SvT_7UGgGiQA", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "SvT_7UGgGiQA", "outputId": "47702d82-f92a-4991-faec-486b25f07f9a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Generating Report Plan ---\n", "--- Generating Report Plan Completed ---\n", "--- Generating Search Queries for Section: NVIDIA's Market Dominance in GPUs ------ Generating Search Queries for Section: Strategic Acquisitions and Partnerships ---\n", "--- Generating Search Queries for Section: Technological Innovations and AI Leadership ---\n", "\n", "--- Generating Search Queries for Section: Financial Performance and Growth Strategy ---\n", "--- Generating Search Queries for Section: NVIDIA's Market Dominance in GPUs Completed ---\n", "--- Searching Web for Queries ---\n", "--- Generating Search Queries for Section: Financial Performance and Growth Strategy Completed ---\n", "--- Searching Web for Queries ---\n", "--- Generating Search Queries for Section: Technological Innovations and AI Leadership Completed ---\n", "--- Searching Web for Queries ---\n", "--- Generating Search Queries for Section: Strategic Acquisitions and Partnerships Completed ---\n", "--- Searching Web for Queries ---\n", "--- Searching Web for Queries Completed ---\n", "--- Writing Section : Strategic Acquisitions and Partnerships ---\n", "--- Searching Web for Queries Completed ---\n", "--- Writing Section : Financial Performance and Growth Strategy ---\n", "--- Searching Web for Queries Completed ---\n", "--- Writing Section : NVIDIA's Market Dominance in GPUs ---\n", "--- Searching Web for Queries Completed ---\n", "--- Writing Section : Technological Innovations and AI Leadership ---\n", "--- Writing Section : Strategic Acquisitions and Partnerships Completed ---\n", "--- Writing Section : Financial Performance and Growth Strategy Completed ---\n", "--- Writing Section : NVIDIA's Market Dominance in GPUs Completed ---\n", "--- Writing Section : Technological Innovations and AI Leadership Completed ---\n", "--- Formatting Completed Sections ---\n", "--- Formatting Completed Sections is Done ---\n", "--- Writing Final Section: Introduction ------ Writing Final Section: Conclusion ---\n", "\n", "--- Writing Final Section: Introduction Completed ---\n", "--- Writing Final Section: Conclusion Completed ---\n", "--- Compiling Final Report ---\n", "--- Compiling Final Report Done ---\n", "==================================================\n", "Final Report:\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃                                                  <span style=\"font-weight: bold\">Introduction</span>                                                   ┃\n", "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n", "\n", "NVIDIA stands as a formidable leader in the technology market, particularly in the GPU and AI sectors. With an     \n", "impressive 90% market share in the discrete GPU market, NVIDIA has outpaced competitors like AMD and Intel through \n", "technological innovation and strategic positioning. The company's strategic acquisitions, such as Mellanox         \n", "Technologies and Arm Limited, have bolstered its capabilities in AI and data centers, further solidifying its      \n", "dominance. NVIDIA's advancements in AI and accelerated computing, coupled with robust financial performance,       \n", "underscore its pivotal role in shaping the future of technology. This report will delve into how NVIDIA continues  \n", "to outperform its competitors, setting new standards in the industry.                                              \n", "\n", "\n", "                                         <span style=\"font-weight: bold; text-decoration: underline\">NVIDIA's Market Dominance in GPUs</span>                                         \n", "\n", "<span style=\"font-weight: bold\">NVIDIA commands an unprecedented 90% of the global GPU market share as of Q3 2024, leaving competitors AMD and </span>    \n", "<span style=\"font-weight: bold\">Intel far behind.</span> This dominance is attributed to NVIDIA's strategic advancements and the anticipated release of   \n", "the RTX 50 series. According to Jon <PERSON>, NVIDIA's market share surged from 84% in early 2024 to 90% by \n", "Q3, while AMD's share dwindled to 10%, and Intel's presence nearly vanished.                                       \n", "\n", "Several factors contribute to NVIDIA's market leadership:                                                          \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Technological Innovation</span>: The RTX 50 series, featuring advanced AI capabilities and enhanced ray tracing, has   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>generated significant consumer interest.                                                                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Strategic Positioning</span>: NVIDIA's focus on AI and data center solutions has expanded its market reach, with AI GPU\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>market shares estimated between 70% and 95%.                                                                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Manufacturing Prowess</span>: NVIDIA's robust manufacturing capabilities have outpaced competitors, allowing for rapid \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>deployment of new technologies.                                                                                 \n", "\n", "Despite a 7.9% decline in overall GPU shipments year-over-year, NVIDIA's strategic initiatives have solidified its \n", "position as the leading force in the GPU sector, raising questions about innovation and pricing in a               \n", "near-monopolistic market.                                                                                          \n", "\n", "                                                      <span style=\"font-weight: bold\">Sources</span>                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>NVIDIA Crushes Rivals: Secures Unprecedented 90% of GPU Market in Q3 2024:                                      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://finance.yahoo.com/news/nvidia-crushes-rivals-secures-unprecedented-102235255.html                       \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Nvidia's desktop GPU dominance grows to 88% as market returns to normal:                                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://www.techspot.com/news/103293-nvidia-desktop-gpu-market-dominance-grows-88-sector.html                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Nvidia AI GPU Market Share 2024: Unrivaled Dominance and Market Trends:                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://deepai.tn/papers/nvidia-ai-gpu-market-share-2024/                                                       \n", "\n", "\n", "                                      <span style=\"font-weight: bold; text-decoration: underline\">Strategic Acquisitions and Partnerships</span>                                      \n", "\n", "<span style=\"font-weight: bold\">NVIDIA's acquisition of Mellanox Technologies and Arm Limited has significantly enhanced its AI and data center </span>   \n", "<span style=\"font-weight: bold\">capabilities.</span> The $7 billion acquisition of Mellanox in 2019 allowed NVIDIA to integrate high-performance          \n", "networking solutions, transforming it into a comprehensive data center service provider. This acquisition enabled  \n", "NVIDIA to offer end-to-end technologies from AI computing to networking, crucial for next-generation data centers  \n", "[1][2].                                                                                                            \n", "\n", "In a $40 billion deal, NVIDIA's acquisition of Arm Limited aimed to merge its AI computing platform with Arm's     \n", "ecosystem, enhancing energy-efficient AI hardware solutions. This strategic move is expected to redefine computing \n", "in the AI era, particularly in edge computing and IoT applications [3][4].                                         \n", "\n", "NVIDIA's partnership with AWS further accelerates AI innovation. The collaboration includes the integration of     \n", "NVIDIA's Blackwell GPU platform into AWS infrastructure, enabling faster and more secure deployment of AI models.  \n", "This partnership supports the development of multi-trillion parameter large language models, crucial for advancing \n", "generative AI capabilities [5][6].                                                                                 \n", "\n", "                                                      <span style=\"font-weight: bold\">Sources</span>                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Nvidia finalizes Mellanox deal to boost AI products, 2020:                                                      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://www.techtarget.com/searchenterpriseai/news/252482488/Nvidia-finalizes-Mellanox-deal-to-boost-AI-products\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>With an Eye on Intelligent Next-Gen Data Centers, NVIDIA Acquires Mellanox, 2020:                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://www.allaboutcircuits.com/news/with-an-eye-on-intelligent-next-gen-data-centers-nvidia-acquires-mellanox-\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>for-7billion/                                                                                                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Mapping Nvidia's Expansion with 17 Strategic Acquisitions, 2024:                                                \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://cioinfluence.com/it-and-devops/mapping-nvidias-expansion-with-17-strategic-acquisitions/                \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Nvidia's Acquisition of Arm: A Game-Changer for the AI Ecosystem, 2024:                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://medium.com/vanguard-industry-foresight/nvidias-acquisition-of-arm-a-game-changer-for-the-ai-ecosystem-35\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>1f49e7e0ce                                                                                                      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>AWS and NVIDIA Extend Collaboration to Advance Generative AI, 2024:                                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://press.aboutamazon.com/2024/3/aws-and-nvidia-extend-collaboration-to-advance-generative-ai-innovation    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>AWS and NVIDIA Form Game-Changing Strategic Alliance for Advanced Computing Solutions, 2024:                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://www.aimlmag.com/aws-and-nvidia-form-game-changing-strategic-alliance-for-advanced-computing-solutions/  \n", "\n", "\n", "                                    <span style=\"font-weight: bold; text-decoration: underline\">Technological Innovations and AI Leadership</span>                                    \n", "\n", "<span style=\"font-weight: bold\">NVIDIA's advancements in AI and accelerated computing are reshaping the future of technology.</span> The introduction of  \n", "the Blackwell GPU platform, with its 208 billion transistors and 8 TB/s memory bandwidth, exemplifies NVIDIA's     \n", "commitment to high-performance AI computing. This platform supports AI models with up to 10 trillion parameters,   \n", "significantly enhancing training and inference capabilities. NVIDIA's edge computing solutions, such as the Jetson \n", "and EGX platforms, enable real-time AI processing at the data source, reducing latency and improving security.     \n", "\n", "NVIDIA's role in AI extends beyond hardware. The CUDA-X libraries, including the new cuPyNumeric, accelerate       \n", "scientific research by enabling GPU-accelerated data science and machine learning applications. At SC24, NVIDIA    \n", "showcased its Omniverse Blueprint for real-time digital twins, which accelerates simulations by up to 1,200x,      \n", "setting a new standard for interactivity.                                                                          \n", "\n", "A notable case study is NVIDIA's collaboration with Foxconn to scale production of AI systems like the Blackwell   \n", "supercomputer, highlighting the company's strategic partnerships to meet growing demand. These innovations         \n", "demonstrate NVIDIA's leadership in AI and computing, driving advancements across industries.                       \n", "\n", "                                                      <span style=\"font-weight: bold\">Sources</span>                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>NVIDIA Unveils AI &amp; Supercomputing Advances at SC 2024:                                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://www.techrepublic.com/article/nvidia-ai-supercomputing-2024/                                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>AI Will Drive Scientific Breakthroughs, NVIDIA CEO Says at SC24:                                                \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://blogs.nvidia.com/blog/supercomputing-24/                                                                \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Nvidia's SC24 special address hints at a new era in computing and AI:                                           \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://siliconangle.com/2024/11/20/nvidias-sc24-special-address-hints-new-era-computing-ai/                    \n", "\n", "\n", "                                     <span style=\"font-weight: bold; text-decoration: underline\">Financial Performance and Growth Strategy</span>                                     \n", "\n", "<span style=\"font-weight: bold\">NVIDIA's financial performance in 2024 has been extraordinary, driven by AI demand and strategic innovation.</span> The   \n", "company reported a record revenue of $26 billion in Q1 2024, an 18% increase quarter-over-quarter and a 262%       \n", "year-over-year growth, primarily fueled by its data center segment, which contributed $22.6 billion. This segment's\n", "success is attributed to the widespread adoption of NVIDIA's technology in AI and high-performance computing       \n", "applications, with major customers like OpenAI and Meta. NVIDIA's market capitalization soared to over $3.28       \n", "trillion, making it the second-most valuable company globally.                                                     \n", "\n", "NVIDIA's growth strategy focuses on continuous innovation and substantial R&amp;D investments, which increased from    \n", "$2.38 billion in 2019 to $8.68 billion in 2024. This strategy includes the development of AI-specific hardware and \n", "expansion into data centers, positioning NVIDIA as a leader in the AI revolution. The company also announced a     \n", "10-for-1 stock split to make stock ownership more accessible. Despite challenges like supply chain constraints and \n", "emerging competition, NVIDIA's robust financial health and strategic initiatives suggest a positive outlook for    \n", "sustained growth.                                                                                                  \n", "\n", "                                                      <span style=\"font-weight: bold\">Sources</span>                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>NVIDIA Q1 2024 Earnings: AI Demand Fuels Growth Leading to Stock Split:                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://finance.yahoo.com/news/nvidia-q1-2024-earnings-ai-*********.html                                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>NVIDIA's AI Dominance in 2024: $28 Billion Revenue and What's Next?:                                            \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://medium.com/quarterscope/nvidias-ai-dominance-in-2024-28-billion-revenue-and-what-s-next-4e117875e141    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Decoding NVIDIA Corp (NVDA): A Strategic SWOT Insight:                                                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://finance.yahoo.com/news/decoding-nvidia-corp-nvda-strategic-*********.html                               \n", "\n", "\n", "                                                    <span style=\"font-weight: bold; text-decoration: underline\">Conclusion</span>                                                     \n", "\n", "NVIDIA's success is driven by its strategic focus on technological innovation, strategic acquisitions, and robust  \n", "financial performance. The company's dominance in the GPU market, with a 90% share, is bolstered by the advanced   \n", "RTX 50 series and a strong presence in AI and data centers. Strategic acquisitions like Mellanox and Arm have      \n", "expanded NVIDIA's capabilities, while partnerships with AWS enhance its AI offerings. Technological innovations,   \n", "such as the Blackwell GPU platform, position NVIDIA as a leader in AI and accelerated computing. Financially,      \n", "NVIDIA's record revenue and market capitalization underscore its growth strategy, emphasizing R&amp;D and market       \n", "expansion. These elements collectively ensure NVIDIA's competitive edge and sustained growth.                      \n", "\n", "                                                                                           \n", " <span style=\"font-weight: bold\"> Competitive Advantage     </span> <span style=\"font-weight: bold\"> Description                                                 </span> \n", " ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ \n", "  Market Dominance            90% GPU market share, leading AI and data center solutions   \n", "  Strategic Acquisitions      Enhanced capabilities through Mellanox and Arm acquisitions  \n", "  Technological Innovations   Advanced AI processors and edge computing solutions          \n", "  Financial Performance       Record revenue growth and strong market capitalization       \n", "                                                                                           \n", "\n", "NVIDIA's strategic initiatives suggest a promising future, with continued innovation and market leadership.        \n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃                                                  \u001b[1mIntroduction\u001b[0m                                                   ┃\n", "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n", "\n", "NVIDIA stands as a formidable leader in the technology market, particularly in the GPU and AI sectors. With an     \n", "impressive 90% market share in the discrete GPU market, NVIDIA has outpaced competitors like AMD and Intel through \n", "technological innovation and strategic positioning. The company's strategic acquisitions, such as Mellanox         \n", "Technologies and Arm Limited, have bolstered its capabilities in AI and data centers, further solidifying its      \n", "dominance. NVIDIA's advancements in AI and accelerated computing, coupled with robust financial performance,       \n", "underscore its pivotal role in shaping the future of technology. This report will delve into how NVIDIA continues  \n", "to outperform its competitors, setting new standards in the industry.                                              \n", "\n", "\n", "                                         \u001b[1;4mNVIDIA's Market Dominance in GPUs\u001b[0m                                         \n", "\n", "\u001b[1mNVIDIA commands an unprecedented 90% of the global GPU market share as of Q3 2024, leaving competitors AMD and \u001b[0m    \n", "\u001b[1mIntel far behind.\u001b[0m This dominance is attributed to NVIDIA's strategic advancements and the anticipated release of   \n", "the RTX 50 series. According to Jon <PERSON>, NVIDIA's market share surged from 84% in early 2024 to 90% by \n", "Q3, while AMD's share dwindled to 10%, and Intel's presence nearly vanished.                                       \n", "\n", "Several factors contribute to NVIDIA's market leadership:                                                          \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mTechnological Innovation\u001b[0m: The RTX 50 series, featuring advanced AI capabilities and enhanced ray tracing, has   \n", "\u001b[1;33m   \u001b[0mgenerated significant consumer interest.                                                                        \n", "\u001b[1;33m • \u001b[0m\u001b[1mStrategic Positioning\u001b[0m: NVIDIA's focus on AI and data center solutions has expanded its market reach, with AI GPU\n", "\u001b[1;33m   \u001b[0mmarket shares estimated between 70% and 95%.                                                                    \n", "\u001b[1;33m • \u001b[0m\u001b[1mManufacturing Prowess\u001b[0m: NVIDIA's robust manufacturing capabilities have outpaced competitors, allowing for rapid \n", "\u001b[1;33m   \u001b[0mdeployment of new technologies.                                                                                 \n", "\n", "Despite a 7.9% decline in overall GPU shipments year-over-year, NVIDIA's strategic initiatives have solidified its \n", "position as the leading force in the GPU sector, raising questions about innovation and pricing in a               \n", "near-monopolistic market.                                                                                          \n", "\n", "                                                      \u001b[1mSources\u001b[0m                                                      \n", "\n", "\u001b[1;33m • \u001b[0mNVIDIA Crushes Rivals: Secures Unprecedented 90% of GPU Market in Q3 2024:                                      \n", "\u001b[1;33m   \u001b[0mhttps://finance.yahoo.com/news/nvidia-crushes-rivals-secures-unprecedented-102235255.html                       \n", "\u001b[1;33m • \u001b[0mNvidia's desktop GPU dominance grows to 88% as market returns to normal:                                        \n", "\u001b[1;33m   \u001b[0mhttps://www.techspot.com/news/103293-nvidia-desktop-gpu-market-dominance-grows-88-sector.html                   \n", "\u001b[1;33m • \u001b[0mNvidia AI GPU Market Share 2024: Unrivaled Dominance and Market Trends:                                         \n", "\u001b[1;33m   \u001b[0mhttps://deepai.tn/papers/nvidia-ai-gpu-market-share-2024/                                                       \n", "\n", "\n", "                                      \u001b[1;4mStrategic Acquisitions and Partnerships\u001b[0m                                      \n", "\n", "\u001b[1mNVIDIA's acquisition of Mellanox Technologies and Arm Limited has significantly enhanced its AI and data center \u001b[0m   \n", "\u001b[1mcapabilities.\u001b[0m The $7 billion acquisition of Mellanox in 2019 allowed NVIDIA to integrate high-performance          \n", "networking solutions, transforming it into a comprehensive data center service provider. This acquisition enabled  \n", "NVIDIA to offer end-to-end technologies from AI computing to networking, crucial for next-generation data centers  \n", "[1][2].                                                                                                            \n", "\n", "In a $40 billion deal, NVIDIA's acquisition of Arm Limited aimed to merge its AI computing platform with Arm's     \n", "ecosystem, enhancing energy-efficient AI hardware solutions. This strategic move is expected to redefine computing \n", "in the AI era, particularly in edge computing and IoT applications [3][4].                                         \n", "\n", "NVIDIA's partnership with AWS further accelerates AI innovation. The collaboration includes the integration of     \n", "NVIDIA's Blackwell GPU platform into AWS infrastructure, enabling faster and more secure deployment of AI models.  \n", "This partnership supports the development of multi-trillion parameter large language models, crucial for advancing \n", "generative AI capabilities [5][6].                                                                                 \n", "\n", "                                                      \u001b[1mSources\u001b[0m                                                      \n", "\n", "\u001b[1;33m • \u001b[0mNvidia finalizes Mellanox deal to boost AI products, 2020:                                                      \n", "\u001b[1;33m   \u001b[0mhttps://www.techtarget.com/searchenterpriseai/news/252482488/Nvidia-finalizes-Mellanox-deal-to-boost-AI-products\n", "\u001b[1;33m • \u001b[0mWith an Eye on Intelligent Next-Gen Data Centers, NVIDIA Acquires Mellanox, 2020:                               \n", "\u001b[1;33m   \u001b[0mhttps://www.allaboutcircuits.com/news/with-an-eye-on-intelligent-next-gen-data-centers-nvidia-acquires-mellanox-\n", "\u001b[1;33m   \u001b[0mfor-7billion/                                                                                                   \n", "\u001b[1;33m • \u001b[0mMapping Nvidia's Expansion with 17 Strategic Acquisitions, 2024:                                                \n", "\u001b[1;33m   \u001b[0mhttps://cioinfluence.com/it-and-devops/mapping-nvidias-expansion-with-17-strategic-acquisitions/                \n", "\u001b[1;33m • \u001b[0mNvidia's Acquisition of Arm: A Game-Changer for the AI Ecosystem, 2024:                                         \n", "\u001b[1;33m   \u001b[0mhttps://medium.com/vanguard-industry-foresight/nvidias-acquisition-of-arm-a-game-changer-for-the-ai-ecosystem-35\n", "\u001b[1;33m   \u001b[0m1f49e7e0ce                                                                                                      \n", "\u001b[1;33m • \u001b[0mAWS and NVIDIA Extend Collaboration to Advance Generative AI, 2024:                                             \n", "\u001b[1;33m   \u001b[0mhttps://press.aboutamazon.com/2024/3/aws-and-nvidia-extend-collaboration-to-advance-generative-ai-innovation    \n", "\u001b[1;33m • \u001b[0mAWS and NVIDIA Form Game-Changing Strategic Alliance for Advanced Computing Solutions, 2024:                    \n", "\u001b[1;33m   \u001b[0mhttps://www.aimlmag.com/aws-and-nvidia-form-game-changing-strategic-alliance-for-advanced-computing-solutions/  \n", "\n", "\n", "                                    \u001b[1;4mTechnological Innovations and AI Leadership\u001b[0m                                    \n", "\n", "\u001b[1mNVIDIA's advancements in AI and accelerated computing are reshaping the future of technology.\u001b[0m The introduction of  \n", "the Blackwell GPU platform, with its 208 billion transistors and 8 TB/s memory bandwidth, exemplifies NVIDIA's     \n", "commitment to high-performance AI computing. This platform supports AI models with up to 10 trillion parameters,   \n", "significantly enhancing training and inference capabilities. NVIDIA's edge computing solutions, such as the Jetson \n", "and EGX platforms, enable real-time AI processing at the data source, reducing latency and improving security.     \n", "\n", "NVIDIA's role in AI extends beyond hardware. The CUDA-X libraries, including the new cuPyNumeric, accelerate       \n", "scientific research by enabling GPU-accelerated data science and machine learning applications. At SC24, NVIDIA    \n", "showcased its Omniverse Blueprint for real-time digital twins, which accelerates simulations by up to 1,200x,      \n", "setting a new standard for interactivity.                                                                          \n", "\n", "A notable case study is NVIDIA's collaboration with Foxconn to scale production of AI systems like the Blackwell   \n", "supercomputer, highlighting the company's strategic partnerships to meet growing demand. These innovations         \n", "demonstrate NVIDIA's leadership in AI and computing, driving advancements across industries.                       \n", "\n", "                                                      \u001b[1mSources\u001b[0m                                                      \n", "\n", "\u001b[1;33m • \u001b[0mNVIDIA Unveils AI & Supercomputing Advances at SC 2024:                                                         \n", "\u001b[1;33m   \u001b[0mhttps://www.techrepublic.com/article/nvidia-ai-supercomputing-2024/                                             \n", "\u001b[1;33m • \u001b[0mAI Will Drive Scientific Breakthroughs, NVIDIA CEO Says at SC24:                                                \n", "\u001b[1;33m   \u001b[0mhttps://blogs.nvidia.com/blog/supercomputing-24/                                                                \n", "\u001b[1;33m • \u001b[0mNvidia's SC24 special address hints at a new era in computing and AI:                                           \n", "\u001b[1;33m   \u001b[0mhttps://siliconangle.com/2024/11/20/nvidias-sc24-special-address-hints-new-era-computing-ai/                    \n", "\n", "\n", "                                     \u001b[1;4mFinancial Performance and Growth Strategy\u001b[0m                                     \n", "\n", "\u001b[1mNVIDIA's financial performance in 2024 has been extraordinary, driven by AI demand and strategic innovation.\u001b[0m The   \n", "company reported a record revenue of $26 billion in Q1 2024, an 18% increase quarter-over-quarter and a 262%       \n", "year-over-year growth, primarily fueled by its data center segment, which contributed $22.6 billion. This segment's\n", "success is attributed to the widespread adoption of NVIDIA's technology in AI and high-performance computing       \n", "applications, with major customers like OpenAI and Meta. NVIDIA's market capitalization soared to over $3.28       \n", "trillion, making it the second-most valuable company globally.                                                     \n", "\n", "NVIDIA's growth strategy focuses on continuous innovation and substantial R&D investments, which increased from    \n", "$2.38 billion in 2019 to $8.68 billion in 2024. This strategy includes the development of AI-specific hardware and \n", "expansion into data centers, positioning NVIDIA as a leader in the AI revolution. The company also announced a     \n", "10-for-1 stock split to make stock ownership more accessible. Despite challenges like supply chain constraints and \n", "emerging competition, NVIDIA's robust financial health and strategic initiatives suggest a positive outlook for    \n", "sustained growth.                                                                                                  \n", "\n", "                                                      \u001b[1mSources\u001b[0m                                                      \n", "\n", "\u001b[1;33m • \u001b[0mNVIDIA Q1 2024 Earnings: AI Demand Fuels Growth Leading to Stock Split:                                         \n", "\u001b[1;33m   \u001b[0mhttps://finance.yahoo.com/news/nvidia-q1-2024-earnings-ai-*********.html                                        \n", "\u001b[1;33m • \u001b[0mNVIDIA's AI Dominance in 2024: $28 Billion Revenue and What's Next?:                                            \n", "\u001b[1;33m   \u001b[0mhttps://medium.com/quarterscope/nvidias-ai-dominance-in-2024-28-billion-revenue-and-what-s-next-4e117875e141    \n", "\u001b[1;33m • \u001b[0mDecoding NVIDIA Corp (NVDA): A Strategic SWOT Insight:                                                          \n", "\u001b[1;33m   \u001b[0mhttps://finance.yahoo.com/news/decoding-nvidia-corp-nvda-strategic-*********.html                               \n", "\n", "\n", "                                                    \u001b[1;4mConclusion\u001b[0m                                                     \n", "\n", "NVIDIA's success is driven by its strategic focus on technological innovation, strategic acquisitions, and robust  \n", "financial performance. The company's dominance in the GPU market, with a 90% share, is bolstered by the advanced   \n", "RTX 50 series and a strong presence in AI and data centers. Strategic acquisitions like Mellanox and Arm have      \n", "expanded NVIDIA's capabilities, while partnerships with AWS enhance its AI offerings. Technological innovations,   \n", "such as the Blackwell GPU platform, position NVIDIA as a leader in AI and accelerated computing. Financially,      \n", "NVIDIA's record revenue and market capitalization underscore its growth strategy, emphasizing R&D and market       \n", "expansion. These elements collectively ensure NVIDIA's competitive edge and sustained growth.                      \n", "\n", "                                                                                           \n", " \u001b[1m \u001b[0m\u001b[1mCompetitive Advantage\u001b[0m\u001b[1m    \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDescription\u001b[0m\u001b[1m                                                \u001b[0m\u001b[1m \u001b[0m \n", " ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ \n", "  Market Dominance            90% GPU market share, leading AI and data center solutions   \n", "  Strategic Acquisitions      Enhanced capabilities through Mellanox and Arm acquisitions  \n", "  Technological Innovations   Advanced AI processors and edge computing solutions          \n", "  Financial Performance       Record revenue growth and strong market capitalization       \n", "                                                                                           \n", "\n", "NVIDIA's strategic initiatives suggest a promising future, with continued innovation and market leadership.        \n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["topic = \"Detailed report on how is NVIDIA winning the game against its competitors\"\n", "await call_planner_agent(agent=reporter_agent,\n", "                         prompt=topic)"]}, {"cell_type": "code", "execution_count": null, "id": "W1ng0JUSKai8", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "W1ng0JUSKai8", "outputId": "0e8d76c0-0f1e-48ba-e194-8670209be93a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Generating Report Plan ---\n", "--- Generating Report Plan Completed ---\n", "--- Generating Search Queries for Section: DeepSeek's Technological Innovations ---\n", "--- Generating Search Queries for Section: Market Disruption and Economic Impact ---\n", "--- Generating Search Queries for Section: Comparison with Established AI Models ---\n", "--- Generating Search Queries for Section: Challenges and Criticisms ---\n", "--- Generating Search Queries for Section: Comparison with Established AI Models Completed ---\n", "--- Searching Web for Queries ---\n", "--- Generating Search Queries for Section: Challenges and Criticisms Completed ---\n", "--- Searching Web for Queries ---\n", "--- Generating Search Queries for Section: Market Disruption and Economic Impact Completed ---\n", "--- Searching Web for Queries ---\n", "--- Generating Search Queries for Section: DeepSeek's Technological Innovations Completed ---\n", "--- Searching Web for Queries ---\n", "--- Searching Web for Queries Completed ---\n", "--- Writing Section : DeepSeek's Technological Innovations ---\n", "--- Searching Web for Queries Completed ---\n", "--- Writing Section : Comparison with Established AI Models ---\n", "--- Searching Web for Queries Completed ---\n", "--- Writing Section : Market Disruption and Economic Impact ---\n", "--- Searching Web for Queries Completed ---\n", "--- Writing Section : Challenges and Criticisms ---\n", "--- Writing Section : DeepSeek's Technological Innovations Completed ---\n", "--- Writing Section : Comparison with Established AI Models Completed ---\n", "--- Writing Section : Market Disruption and Economic Impact Completed ---\n", "--- Writing Section : Challenges and Criticisms Completed ---\n", "--- Formatting Completed Sections ---\n", "--- Formatting Completed Sections is Done ---\n", "--- Writing Final Section: Introduction ------ Writing Final Section: Conclusion ---\n", "\n", "--- Writing Final Section: Introduction Completed ---\n", "--- Writing Final Section: Conclusion Completed ---\n", "--- Compiling Final Report ---\n", "--- Compiling Final Report Done ---\n", "==================================================\n", "Final Report:\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃                                                  <span style=\"font-weight: bold\">Introduction</span>                                                   ┃\n", "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n", "\n", "DeepSeek is a groundbreaking player in the AI market, known for its innovative technological advancements and      \n", "disruptive economic strategies. By leveraging open-source models and efficient AI development techniques, DeepSeek \n", "has redefined the landscape of AI model efficiency and accessibility. Its introduction of cost-effective models has\n", "sparked significant price wars, challenging established tech giants like Nvidia and OpenAI. This report delves into\n", "DeepSeek's technological innovations, market impact, and the challenges it faces, offering a comprehensive analysis\n", "of its role in reshaping the AI industry.                                                                          \n", "\n", "\n", "                                       <span style=\"font-weight: bold; text-decoration: underline\">DeepSeek's Technological Innovations</span>                                        \n", "\n", "<span style=\"font-weight: bold\">DeepSeek's innovative use of the Mixture-of-Experts (MoE) architecture and efficient AI development techniques has </span>\n", "<span style=\"font-weight: bold\">redefined AI model efficiency.</span> The DeepSeek-V3 model, launched in December 2024, exemplifies this with its 671     \n", "billion parameters, yet only 37 billion are activated per task, significantly reducing computational costs. This   \n", "model was trained using just 2,048 Nvidia H800 GPUs over two months, costing approximately $5.6 million, a fraction\n", "of the cost for similar models from major tech companies.                                                          \n", "\n", "DeepSeek's approach includes several key innovations:                                                              \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Multi-Head Latent Attention (MLA):</span> Enhances the model's ability to process complex inputs by focusing on        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>different data aspects simultaneously.                                                                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">FP8 Mixed Precision Training:</span> Reduces memory usage and accelerates training without sacrificing accuracy.       \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Auxiliary-Loss-Free Load Balancing:</span> Ensures efficient distribution of computational tasks across the model's    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>parameters.                                                                                                     \n", "\n", "These advancements allow DeepSeek to deliver high performance on benchmarks like MMLU-Pro and MATH 500, often      \n", "surpassing models like GPT-4o. DeepSeek's commitment to open-source models democratizes access to advanced AI,     \n", "challenging the dominance of established players and reshaping the AI landscape.                                   \n", "\n", "                                                      <span style=\"font-weight: bold\">Sources</span>                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>All About DeepSeek — The Chinese AI Startup Challenging US Big Tech:                                            \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://www.forbes.com/sites/janakirammsv/2025/01/26/all-about-deepseekthe-chinese-ai-startup-challenging-the-us\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>-big-tech/                                                                                                      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Exploring DeepSeek-V3: A Technical Overview:                                                                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://medium.com/@lmpo/exploring-deepseek-version-3-a-technical-deep-dive-0b3d2c78b777                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>DeepSeek: What you need to know about the AI that dethroned ChatGP<PERSON>:                                            \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://www.digitaltrends.com/computing/what-is-deepseek-everything-you-need-to-know/                           \n", "\n", "\n", "                                       <span style=\"font-weight: bold; text-decoration: underline\">Market Disruption and Economic Impact</span>                                       \n", "\n", "<span style=\"font-weight: bold\">DeepSeek's aggressive pricing strategy has triggered a significant price war in the AI market, challenging major </span>  \n", "<span style=\"font-weight: bold\">players like Nvidia and OpenAI.</span> The introduction of DeepSeek-V2 in May 2024, with its high performance at a low    \n", "cost, has forced companies such as ByteDance, Tencent, and Alibaba to lower their prices. This disruption has      \n", "extended beyond China, affecting global tech giants. For instance, Nvidia's stock plummeted by 12.5% following     \n", "DeepSeek's market entry, highlighting the financial impact on established companies reliant on high-cost AI        \n", "infrastructure.                                                                                                    \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Key Impacts:</span>                                                                                                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span><span style=\"font-weight: bold\">Nvidia:</span> Experienced a record $593 billion market cap loss, with shares dropping 17%.                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span><span style=\"font-weight: bold\">OpenAI:</span> Faces pressure to reduce prices as DeepSeek's models rival their offerings at a fraction of the cost.\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span><span style=\"font-weight: bold\">Global AI Market:</span> The emergence of cost-efficient models like DeepSeek-V3 has sparked discussions on the     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">      </span>sustainability of current AI investments.                                                                    \n", "\n", "DeepSeek's rise underscores a shift towards more accessible AI technologies, challenging the traditional balance   \n", "between innovation and profitability. This shift is prompting a reevaluation of investment strategies in AI        \n", "infrastructure, as companies seek to remain competitive in a rapidly evolving market landscape.                    \n", "\n", "                                                      <span style=\"font-weight: bold\">Sources</span>                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>DeepSeek price is challenges Nvidia, ChatGPT, and Silicon Valley - Quartz:                                      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://qz.com/deepseeks-aggressive-pricing-is-challenging-silicon-val-1851748345                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>DeepSeek's cheap price is challenging Nvidia, ChatGPT, and Silicon ...:                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://finance.yahoo.com/news/deepseeks-cheap-price-challenging-nvidia-180341018.html                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>DeepSeek And The Looming AI Price War Will Affect Us All - Forbes:                                              \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://www.forbes.com/sites/craigsmith/2025/01/28/the-looming-ai-price-war-will-affect-us-all/                 \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>What is DeepSeek, and why is it causing Nvidia and other stocks to ...:                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://www.cbsnews.com/news/what-is-deepseek-ai-china-stock-nvidia-nvda-asml/                                  \n", "\n", "\n", "                                       <span style=\"font-weight: bold; text-decoration: underline\">Comparison with Established AI Models</span>                                       \n", "\n", "<span style=\"font-weight: bold\">DeepSeek V3 offers a cost-effective alternative to proprietary models like OpenAI's GPT-4o, with competitive </span>      \n", "<span style=\"font-weight: bold\">performance across key benchmarks.</span> DeepSeek V3, an open-source model, features 671 billion parameters and utilizes \n", "a Mixture-of-Experts (MoE) architecture, activating only 37 billion parameters per task. This design enhances      \n", "computational efficiency, allowing DeepSeek V3 to achieve high performance in math and coding tasks, such as       \n", "scoring 90.2 on the Math-500 benchmark, surpassing GPT-4o's 74.6.                                                  \n", "\n", "In terms of cost, DeepSeek V3 is significantly cheaper, with input and output token costs at $0.14 and $0.28 per   \n", "million tokens, respectively, compared to GPT-4o's $2.50 and $10.00. This makes DeepSeek V3 approximately 29.8     \n", "times more cost-effective. Additionally, DeepSeek V3's open-source nature under the MIT license provides broad     \n", "accessibility for research and commercial use, contrasting with GPT-4o's proprietary restrictions.                 \n", "\n", "                                                      <span style=\"font-weight: bold\">Sources</span>                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>DeepSeek V3: New Open AI Model Surpasses Rivals and Challenges GPT-4o:                                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://winbuzzer.com/2024/12/27/deepseek-v3-new-open-ai-model-surpasses-rivals-and-challenges-gpt-4o-xcxwbn/   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>DeepSeek-V3 vs GPT-4o - Detailed Performance &amp; Feature Comparison:                                              \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://docsbot.ai/models/compare/deepseek-v3/gpt-4o                                                            \n", "\n", "\n", "                                             <span style=\"font-weight: bold; text-decoration: underline\">Challenges and Criticisms</span>                                             \n", "\n", "<span style=\"font-weight: bold\">DeepSeek faces significant challenges and criticisms, particularly regarding privacy, geopolitical implications, </span>  \n", "<span style=\"font-weight: bold\">and business sustainability.</span> Privacy concerns are paramount, as DeepSeek's privacy policy indicates that user data,\n", "including keystrokes and IP addresses, is stored on servers in China, raising alarms about potential government    \n", "access (TechRadar, 2025). This has led to comparisons with TikTok, highlighting fears of surveillance and data     \n", "misuse (Wired, 2025).                                                                                              \n", "\n", "Geopolitically, DeepSeek's rapid rise has sparked debates about U.S. export controls' effectiveness, as the        \n", "platform's success suggests Chinese AI can thrive despite restrictions (Observer, 2025). This has implications for \n", "global AI leadership and security, with concerns that DeepSeek could be used for misinformation or cyber-espionage \n", "(Axios, 2025).                                                                                                     \n", "\n", "Sustainability of DeepSeek's business model is also questioned. While its open-source approach and low costs       \n", "disrupt the market, reliance on Chinese infrastructure and potential censorship issues may hinder global adoption  \n", "(Forbes, 2025). The platform's ability to maintain competitive pricing while ensuring data security and ethical use\n", "remains a critical challenge.                                                                                      \n", "\n", "                                                      <span style=\"font-weight: bold\">Sources</span>                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>TechRadar:                                                                                                      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://www.techradar.com/computing/cyber-security/is-deepseek-ai-safe-or-is-it-just-a-data-minefield-waiting-to\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>-blow-up?t                                                                                                      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Wired: https://www.wired.com/story/deepseek-ai-china-privacy-data/                                              \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Observer: https://observer.com/2025/01/china-deepseek-ai-shocks-markets-sparks-national-security-fears/         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Axios: https://www.axios.com/2025/01/28/deepseek-ai-model-energy-power-demand                                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Forbes:                                                                                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>https://www.forbes.com/sites/janakirammsv/2025/01/26/all-about-deepseekthe-chinese-ai-startup-challenging-the-us\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>-big-tech/                                                                                                      \n", "\n", "\n", "                                                    <span style=\"font-weight: bold; text-decoration: underline\">Conclusion</span>                                                     \n", "\n", "DeepSeek has emerged as a formidable player in the AI landscape, introducing technological innovations that        \n", "challenge established norms. Its Mixture-of-Experts architecture and efficient AI development techniques have set  \n", "new standards for model efficiency and cost-effectiveness. The market disruption caused by DeepSeek's aggressive   \n", "pricing strategy has pressured major players like Nvidia and OpenAI to reconsider their pricing models,            \n", "highlighting a shift towards more accessible AI technologies. However, DeepSeek faces challenges, including privacy\n", "concerns and geopolitical implications, which may impact its global adoption and business sustainability.          \n", "\n", "                                  <span style=\"font-weight: bold\">Key Disruptive Elements Introduced by DeepSeek</span>                                   \n", "\n", "                                                                                                                \n", " <span style=\"font-weight: bold\"> Element                           </span> <span style=\"font-weight: bold\"> Description                                                              </span> \n", " ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ \n", "  Mixture-of-Experts Architecture     Enhances efficiency by activating only necessary parameters per task.     \n", "  Aggressive Pricing Strategy         Triggers price wars, challenging major AI companies to lower costs.       \n", "  Open-Source Accessibility           Democratizes AI access, contrasting with proprietary models like GPT-4o.  \n", "  Privacy and Geopolitical Concerns   Raises issues about data security and global AI leadership dynamics.      \n", "                                                                                                                \n", "\n", "DeepSeek's innovations and market impact suggest a need for ongoing evaluation of AI investment strategies and     \n", "regulatory frameworks to address emerging challenges.                                                              \n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃                                                  \u001b[1mIntroduction\u001b[0m                                                   ┃\n", "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n", "\n", "DeepSeek is a groundbreaking player in the AI market, known for its innovative technological advancements and      \n", "disruptive economic strategies. By leveraging open-source models and efficient AI development techniques, DeepSeek \n", "has redefined the landscape of AI model efficiency and accessibility. Its introduction of cost-effective models has\n", "sparked significant price wars, challenging established tech giants like Nvidia and OpenAI. This report delves into\n", "DeepSeek's technological innovations, market impact, and the challenges it faces, offering a comprehensive analysis\n", "of its role in reshaping the AI industry.                                                                          \n", "\n", "\n", "                                       \u001b[1;4mDeepSeek's Technological Innovations\u001b[0m                                        \n", "\n", "\u001b[1mDeepSeek's innovative use of the Mixture-of-Experts (MoE) architecture and efficient AI development techniques has \u001b[0m\n", "\u001b[1mredefined AI model efficiency.\u001b[0m The DeepSeek-V3 model, launched in December 2024, exemplifies this with its 671     \n", "billion parameters, yet only 37 billion are activated per task, significantly reducing computational costs. This   \n", "model was trained using just 2,048 Nvidia H800 GPUs over two months, costing approximately $5.6 million, a fraction\n", "of the cost for similar models from major tech companies.                                                          \n", "\n", "DeepSeek's approach includes several key innovations:                                                              \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1m<PERSON><PERSON>i-Head Latent Attention (MLA):\u001b[0m Enhances the model's ability to process complex inputs by focusing on        \n", "\u001b[1;33m   \u001b[0mdifferent data aspects simultaneously.                                                                          \n", "\u001b[1;33m • \u001b[0m\u001b[1mFP8 Mixed Precision Training:\u001b[0m Reduces memory usage and accelerates training without sacrificing accuracy.       \n", "\u001b[1;33m • \u001b[0m\u001b[1mAuxiliary-Loss-Free Load Balancing:\u001b[0m Ensures efficient distribution of computational tasks across the model's    \n", "\u001b[1;33m   \u001b[0mparameters.                                                                                                     \n", "\n", "These advancements allow DeepSeek to deliver high performance on benchmarks like MMLU-Pro and MATH 500, often      \n", "surpassing models like GPT-4o. DeepSeek's commitment to open-source models democratizes access to advanced AI,     \n", "challenging the dominance of established players and reshaping the AI landscape.                                   \n", "\n", "                                                      \u001b[1mSources\u001b[0m                                                      \n", "\n", "\u001b[1;33m • \u001b[0mAll About DeepSeek — The Chinese AI Startup Challenging US Big Tech:                                            \n", "\u001b[1;33m   \u001b[0mhttps://www.forbes.com/sites/janakirammsv/2025/01/26/all-about-deepseekthe-chinese-ai-startup-challenging-the-us\n", "\u001b[1;33m   \u001b[0m-big-tech/                                                                                                      \n", "\u001b[1;33m • \u001b[0mExploring DeepSeek-V3: A Technical Overview:                                                                    \n", "\u001b[1;33m   \u001b[0mhttps://medium.com/@lmpo/exploring-deepseek-version-3-a-technical-deep-dive-0b3d2c78b777                        \n", "\u001b[1;33m • \u001b[0mDeepSeek: What you need to know about the AI that dethroned <PERSON>t<PERSON><PERSON>:                                            \n", "\u001b[1;33m   \u001b[0mhttps://www.digitaltrends.com/computing/what-is-deepseek-everything-you-need-to-know/                           \n", "\n", "\n", "                                       \u001b[1;4mMarket Disruption and Economic Impact\u001b[0m                                       \n", "\n", "\u001b[1mDeepSeek's aggressive pricing strategy has triggered a significant price war in the AI market, challenging major \u001b[0m  \n", "\u001b[1mplayers like Nvidia and OpenAI.\u001b[0m The introduction of DeepSeek-V2 in May 2024, with its high performance at a low    \n", "cost, has forced companies such as ByteDance, Tencent, and Alibaba to lower their prices. This disruption has      \n", "extended beyond China, affecting global tech giants. For instance, Nvidia's stock plummeted by 12.5% following     \n", "DeepSeek's market entry, highlighting the financial impact on established companies reliant on high-cost AI        \n", "infrastructure.                                                                                                    \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mKey Impacts:\u001b[0m                                                                                                    \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mNvidia:\u001b[0m Experienced a record $593 billion market cap loss, with shares dropping 17%.                         \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mOpenAI:\u001b[0m Faces pressure to reduce prices as DeepSeek's models rival their offerings at a fraction of the cost.\n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mGlobal AI Market:\u001b[0m The emergence of cost-efficient models like DeepSeek-V3 has sparked discussions on the     \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0msustainability of current AI investments.                                                                    \n", "\n", "DeepSeek's rise underscores a shift towards more accessible AI technologies, challenging the traditional balance   \n", "between innovation and profitability. This shift is prompting a reevaluation of investment strategies in AI        \n", "infrastructure, as companies seek to remain competitive in a rapidly evolving market landscape.                    \n", "\n", "                                                      \u001b[1mSources\u001b[0m                                                      \n", "\n", "\u001b[1;33m • \u001b[0mDeepSeek price is challenges Nvidia, ChatGPT, and Silicon Valley - Quartz:                                      \n", "\u001b[1;33m   \u001b[0mhttps://qz.com/deepseeks-aggressive-pricing-is-challenging-silicon-val-1851748345                               \n", "\u001b[1;33m • \u001b[0mDeepSeek's cheap price is challenging Nvidia, ChatGPT, and Silicon ...:                                         \n", "\u001b[1;33m   \u001b[0mhttps://finance.yahoo.com/news/deepseeks-cheap-price-challenging-nvidia-180341018.html                          \n", "\u001b[1;33m • \u001b[0mDeepSeek And The Looming AI Price War Will Affect Us All - Forbes:                                              \n", "\u001b[1;33m   \u001b[0mhttps://www.forbes.com/sites/craigsmith/2025/01/28/the-looming-ai-price-war-will-affect-us-all/                 \n", "\u001b[1;33m • \u001b[0mWhat is DeepSeek, and why is it causing Nvidia and other stocks to ...:                                         \n", "\u001b[1;33m   \u001b[0mhttps://www.cbsnews.com/news/what-is-deepseek-ai-china-stock-nvidia-nvda-asml/                                  \n", "\n", "\n", "                                       \u001b[1;4mComparison with Established AI Models\u001b[0m                                       \n", "\n", "\u001b[1mDeepSeek V3 offers a cost-effective alternative to proprietary models like OpenAI's GPT-4o, with competitive \u001b[0m      \n", "\u001b[1mperformance across key benchmarks.\u001b[0m DeepSeek V3, an open-source model, features 671 billion parameters and utilizes \n", "a Mixture-of-Experts (MoE) architecture, activating only 37 billion parameters per task. This design enhances      \n", "computational efficiency, allowing DeepSeek V3 to achieve high performance in math and coding tasks, such as       \n", "scoring 90.2 on the Math-500 benchmark, surpassing GPT-4o's 74.6.                                                  \n", "\n", "In terms of cost, DeepSeek V3 is significantly cheaper, with input and output token costs at $0.14 and $0.28 per   \n", "million tokens, respectively, compared to GPT-4o's $2.50 and $10.00. This makes DeepSeek V3 approximately 29.8     \n", "times more cost-effective. Additionally, DeepSeek V3's open-source nature under the MIT license provides broad     \n", "accessibility for research and commercial use, contrasting with GPT-4o's proprietary restrictions.                 \n", "\n", "                                                      \u001b[1mSources\u001b[0m                                                      \n", "\n", "\u001b[1;33m • \u001b[0mDeepSeek V3: New Open AI Model Surpasses Rivals and Challenges GPT-4o:                                          \n", "\u001b[1;33m   \u001b[0mhttps://winbuzzer.com/2024/12/27/deepseek-v3-new-open-ai-model-surpasses-rivals-and-challenges-gpt-4o-xcxwbn/   \n", "\u001b[1;33m • \u001b[0mDeepSeek-V3 vs GPT-4o - Detailed Performance & Feature Comparison:                                              \n", "\u001b[1;33m   \u001b[0mhttps://docsbot.ai/models/compare/deepseek-v3/gpt-4o                                                            \n", "\n", "\n", "                                             \u001b[1;4mChallenges and Criticisms\u001b[0m                                             \n", "\n", "\u001b[1mDeepSeek faces significant challenges and criticisms, particularly regarding privacy, geopolitical implications, \u001b[0m  \n", "\u001b[1mand business sustainability.\u001b[0m Privacy concerns are paramount, as DeepSeek's privacy policy indicates that user data,\n", "including keystrokes and IP addresses, is stored on servers in China, raising alarms about potential government    \n", "access (TechRadar, 2025). This has led to comparisons with TikTok, highlighting fears of surveillance and data     \n", "misuse (Wired, 2025).                                                                                              \n", "\n", "Geopolitically, DeepSeek's rapid rise has sparked debates about U.S. export controls' effectiveness, as the        \n", "platform's success suggests Chinese AI can thrive despite restrictions (Observer, 2025). This has implications for \n", "global AI leadership and security, with concerns that DeepSeek could be used for misinformation or cyber-espionage \n", "(Axios, 2025).                                                                                                     \n", "\n", "Sustainability of DeepSeek's business model is also questioned. While its open-source approach and low costs       \n", "disrupt the market, reliance on Chinese infrastructure and potential censorship issues may hinder global adoption  \n", "(Forbes, 2025). The platform's ability to maintain competitive pricing while ensuring data security and ethical use\n", "remains a critical challenge.                                                                                      \n", "\n", "                                                      \u001b[1mSources\u001b[0m                                                      \n", "\n", "\u001b[1;33m • \u001b[0mTechRadar:                                                                                                      \n", "\u001b[1;33m   \u001b[0mhttps://www.techradar.com/computing/cyber-security/is-deepseek-ai-safe-or-is-it-just-a-data-minefield-waiting-to\n", "\u001b[1;33m   \u001b[0m-blow-up?t                                                                                                      \n", "\u001b[1;33m • \u001b[0mWired: https://www.wired.com/story/deepseek-ai-china-privacy-data/                                              \n", "\u001b[1;33m • \u001b[0mObserver: https://observer.com/2025/01/china-deepseek-ai-shocks-markets-sparks-national-security-fears/         \n", "\u001b[1;33m • \u001b[0mAxios: https://www.axios.com/2025/01/28/deepseek-ai-model-energy-power-demand                                   \n", "\u001b[1;33m • \u001b[0mForbes:                                                                                                         \n", "\u001b[1;33m   \u001b[0mhttps://www.forbes.com/sites/janakirammsv/2025/01/26/all-about-deepseekthe-chinese-ai-startup-challenging-the-us\n", "\u001b[1;33m   \u001b[0m-big-tech/                                                                                                      \n", "\n", "\n", "                                                    \u001b[1;4mConclusion\u001b[0m                                                     \n", "\n", "DeepSeek has emerged as a formidable player in the AI landscape, introducing technological innovations that        \n", "challenge established norms. Its Mixture-of-Experts architecture and efficient AI development techniques have set  \n", "new standards for model efficiency and cost-effectiveness. The market disruption caused by DeepSeek's aggressive   \n", "pricing strategy has pressured major players like Nvidia and OpenAI to reconsider their pricing models,            \n", "highlighting a shift towards more accessible AI technologies. However, DeepSeek faces challenges, including privacy\n", "concerns and geopolitical implications, which may impact its global adoption and business sustainability.          \n", "\n", "                                  \u001b[1mKey Disruptive Elements Introduced by DeepSeek\u001b[0m                                   \n", "\n", "                                                                                                                \n", " \u001b[1m \u001b[0m\u001b[1mElement\u001b[0m\u001b[1m                          \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDescription\u001b[0m\u001b[1m                                                             \u001b[0m\u001b[1m \u001b[0m \n", " ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ \n", "  Mixture-of-Experts Architecture     Enhances efficiency by activating only necessary parameters per task.     \n", "  Aggressive Pricing Strategy         Triggers price wars, challenging major AI companies to lower costs.       \n", "  Open-Source Accessibility           Democratizes AI access, contrasting with proprietary models like GPT-4o.  \n", "  Privacy and Geopolitical Concerns   Raises issues about data security and global AI leadership dynamics.      \n", "                                                                                                                \n", "\n", "DeepSeek's innovations and market impact suggest a need for ongoing evaluation of AI investment strategies and     \n", "regulatory frameworks to address emerging challenges.                                                              \n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["topic = \"Detailed report on how DeepSeek has disrupted the AI Market\"\n", "await call_planner_agent(agent=reporter_agent,\n", "                         prompt=topic)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}