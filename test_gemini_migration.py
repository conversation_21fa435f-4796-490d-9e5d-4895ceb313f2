#!/usr/bin/env python3
"""
Test script to verify Google Gemini migration works correctly.
This script tests the basic functionality that was migrated from OpenAI to Gemini.
"""

import os
import sys
from getpass import getpass

def test_imports():
    """Test that all required imports work correctly."""
    print("Testing imports...")
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        print("✓ langchain_google_genai import successful")
    except ImportError as e:
        print(f"✗ Failed to import langchain_google_genai: {e}")
        return False
    
    try:
        from langchain_core.messages import HumanMessage, SystemMessage
        print("✓ langchain_core.messages import successful")
    except ImportError as e:
        print(f"✗ Failed to import langchain_core.messages: {e}")
        return False
    
    try:
        import google.generativeai as genai
        print("✓ google.generativeai import successful")
    except ImportError as e:
        print(f"✗ Failed to import google.generativeai: {e}")
        return False
    
    return True

def test_model_initialization():
    """Test that the Gemini model can be initialized."""
    print("\nTesting model initialization...")
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        
        # Check if API key is set
        if not os.getenv('GOOGLE_API_KEY'):
            print("⚠ GOOGLE_API_KEY not set. Model initialization test skipped.")
            return True
        
        # Initialize the model
        llm = ChatGoogleGenerativeAI(model="gemini-1.5-pro", temperature=0)
        print("✓ ChatGoogleGenerativeAI model initialized successfully")
        return True
        
    except Exception as e:
        print(f"✗ Failed to initialize Gemini model: {e}")
        return False

def test_token_estimation():
    """Test the token estimation function that replaced tiktoken."""
    print("\nTesting token estimation function...")
    
    def estimate_tokens(text: str) -> int:
        return len(text) // 4
    
    test_text = "This is a test string for token estimation."
    estimated_tokens = estimate_tokens(test_text)
    
    print(f"✓ Token estimation working. Text: '{test_text}' -> ~{estimated_tokens} tokens")
    return True

def main():
    """Run all tests."""
    print("🧪 Testing Google Gemini Migration")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_model_initialization,
        test_token_estimation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Migration appears successful.")
        return 0
    else:
        print("❌ Some tests failed. Please check the migration.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
